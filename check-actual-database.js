const initSqlJs = require('sql.js');
const fs = require('fs');
const path = require('path');

async function checkActualDatabase() {
  console.log('🔍 Checking actual database files...');
  
  // Check multiple possible database locations
  const possiblePaths = [
    path.join(process.cwd(), 'data', 'leadwave.db'),
    path.join(process.env.APPDATA, 'Electron', 'leadwave.db'),
    path.join(process.cwd(), 'leadwave.db'),
    path.join(process.env.USERPROFILE, 'AppData', 'Roaming', 'Electron', 'leadwave.db')
  ];
  
  console.log('\n📁 Checking database locations:');
  
  for (const dbPath of possiblePaths) {
    console.log(`\n  Checking: ${dbPath}`);
    
    if (fs.existsSync(dbPath)) {
      console.log(`  ✅ Found database file!`);
      
      try {
        const SQL = await initSqlJs();
        const filebuffer = fs.readFileSync(dbPath);
        const db = new SQL.Database(filebuffer);
        
        // Check recent messages
        const messages = db.exec('SELECT id, session_id, content, message_type, direction, created_at FROM message_history ORDER BY id DESC LIMIT 10');
        
        if (messages.length > 0 && messages[0].values.length > 0) {
          console.log(`  📨 Found ${messages[0].values.length} recent messages:`);
          messages[0].values.forEach((row, index) => {
            const [id, sessionId, content, type, direction, createdAt] = row;
            console.log(`    ${index + 1}. [${direction}] "${content}" (${type}) - Session: ${sessionId}`);
          });
          
          // Check for PHP messages
          const phpMessages = messages[0].values.filter(row => {
            const content = row[2]; // content is at index 2
            return content && (content.toLowerCase().includes('php') || content.toLowerCase().includes('development'));
          });
          
          if (phpMessages.length > 0) {
            console.log(`  🎯 Found ${phpMessages.length} PHP-related messages!`);
            const sessionId = phpMessages[0][1]; // session_id is at index 1
            
            // Check flows for this session
            const flows = db.exec('SELECT * FROM chatbot_flows WHERE session_id = ? AND is_active = 1', [sessionId]);
            
            if (flows.length > 0 && flows[0].values.length > 0) {
              console.log(`  🤖 Found ${flows[0].values.length} active flow(s) for session ${sessionId}:`);
              
              // Get column names
              const flowColumns = flows[0].columns;
              const nameIndex = flowColumns.indexOf('name');
              const keywordsIndex = flowColumns.indexOf('trigger_keywords');
              
              flows[0].values.forEach((row, index) => {
                const name = row[nameIndex];
                const keywords = row[keywordsIndex];
                console.log(`    ${index + 1}. "${name}" - Keywords: "${keywords}"`);
              });
            } else {
              console.log(`  ❌ No active flows found for session ${sessionId}`);
              
              // Check all flows
              const allFlows = db.exec('SELECT session_id, name, trigger_keywords FROM chatbot_flows WHERE is_active = 1');
              if (allFlows.length > 0 && allFlows[0].values.length > 0) {
                console.log(`  ⚠️  But found ${allFlows[0].values.length} active flow(s) for other sessions:`);
                allFlows[0].values.forEach((row, index) => {
                  const [flowSessionId, name, keywords] = row;
                  console.log(`    ${index + 1}. "${name}" - Session: ${flowSessionId} - Keywords: "${keywords}"`);
                });
                console.log(`  🚨 SESSION MISMATCH! Messages in session "${sessionId}" but flows in different sessions!`);
              }
            }
            
            // Check active conversations
            const conversations = db.exec('SELECT * FROM chatbot_conversations WHERE session_id = ? AND status = ?', [sessionId, 'active']);
            
            if (conversations.length > 0 && conversations[0].values.length > 0) {
              console.log(`  ⚠️  Found ${conversations[0].values.length} active conversation(s):`);
              conversations[0].values.forEach((row, index) => {
                console.log(`    ${index + 1}. Conversation ID: ${row[0]}, Flow ID: ${row[2]}`);
              });
              console.log(`  🚨 ACTIVE CONVERSATIONS BLOCKING NEW TRIGGERS!`);
            } else {
              console.log(`  ✅ No active conversations found`);
            }
          }
          
        } else {
          console.log(`  📭 No messages found in this database`);
        }
        
        db.close();
        
      } catch (error) {
        console.log(`  ❌ Error reading database: ${error.message}`);
      }
      
    } else {
      console.log(`  ❌ File not found`);
    }
  }
  
  // Also check what the DatabaseService is actually using
  console.log('\n🔧 Checking DatabaseService configuration...');
  const DatabaseService = require('./src/services/database.service');
  const dbService = new DatabaseService();
  
  // Access the private dbPath property (this is a bit hacky but for debugging)
  console.log(`DatabaseService would use: ${dbService.dbPath || 'Unknown path'}`);
}

checkActualDatabase().catch(console.error);
