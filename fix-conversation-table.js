const initSqlJs = require('sql.js');
const fs = require('fs');
const path = require('path');

async function fixConversationTable() {
  console.log('🔧 Fixing chatbot_conversations table schema...');
  
  // Use the actual database path from AppData
  const dbPath = path.join(process.env.APPDATA, 'Electron', 'leadwave.db');
  
  if (!fs.existsSync(dbPath)) {
    console.log('❌ Database not found at:', dbPath);
    return;
  }
  
  const SQL = await initSqlJs();
  const filebuffer = fs.readFileSync(dbPath);
  const db = new SQL.Database(filebuffer);
  
  try {
    // Check current table schema
    console.log('\n🔍 Checking current table schema...');
    const tableInfo = db.exec('PRAGMA table_info(chatbot_conversations)');
    
    if (tableInfo.length > 0) {
      console.log('📋 Current columns:');
      tableInfo[0].values.forEach(row => {
        const [cid, name, type, notnull, dflt_value, pk] = row;
        console.log(`   ${name}: ${type}${notnull ? ' NOT NULL' : ''}${dflt_value ? ` DEFAULT ${dflt_value}` : ''}`);
      });
      
      // Check if status column exists
      const hasStatus = tableInfo[0].values.some(row => row[1] === 'status');
      const hasIsActive = tableInfo[0].values.some(row => row[1] === 'is_active');
      
      if (!hasStatus && !hasIsActive) {
        console.log('\n🔧 Adding missing status column...');
        db.exec('ALTER TABLE chatbot_conversations ADD COLUMN status TEXT DEFAULT "active"');
        console.log('✅ Added status column');
      } else if (hasIsActive && !hasStatus) {
        console.log('\n🔧 Converting is_active to status column...');
        // Add status column
        db.exec('ALTER TABLE chatbot_conversations ADD COLUMN status TEXT');
        // Update status based on is_active
        db.exec('UPDATE chatbot_conversations SET status = CASE WHEN is_active = 1 THEN "active" ELSE "completed" END');
        console.log('✅ Added and populated status column');
      } else if (hasStatus) {
        console.log('\n✅ Status column already exists');
      }
      
      // Check if completed_at column exists
      const hasCompletedAt = tableInfo[0].values.some(row => row[1] === 'completed_at');
      if (!hasCompletedAt) {
        console.log('\n🔧 Adding missing completed_at column...');
        db.exec('ALTER TABLE chatbot_conversations ADD COLUMN completed_at DATETIME');
        console.log('✅ Added completed_at column');
      }
      
    } else {
      console.log('❌ Table chatbot_conversations not found');
      return;
    }
    
    // Save the updated database
    const data = db.export();
    fs.writeFileSync(dbPath, Buffer.from(data));
    console.log('\n💾 Database updated successfully');
    
    // Verify the fix
    console.log('\n🧪 Testing the fix...');
    const testQuery = db.exec('SELECT id, flow_id, user_phone, status, created_at FROM chatbot_conversations LIMIT 3');
    
    if (testQuery.length > 0 && testQuery[0].values.length > 0) {
      console.log('✅ Query works! Sample conversations:');
      testQuery[0].values.forEach((row, index) => {
        const [id, flowId, userPhone, status, createdAt] = row;
        console.log(`   ${index + 1}. ID: ${id}, Flow: ${flowId}, User: ${userPhone}, Status: ${status}`);
      });
    } else {
      console.log('📭 No conversations found (table is empty)');
    }
    
    // Check for active conversations
    const activeConversations = db.exec('SELECT * FROM chatbot_conversations WHERE status = "active"');
    
    if (activeConversations.length > 0 && activeConversations[0].values.length > 0) {
      console.log(`\n⚠️  Found ${activeConversations[0].values.length} active conversation(s):`);
      activeConversations[0].values.forEach((row, index) => {
        console.log(`   ${index + 1}. Conversation ${row[0]}: Flow ${row[2]}, User ${row[3]}`);
      });
      
      console.log('\n🔧 Cleaning up old active conversations...');
      db.exec('UPDATE chatbot_conversations SET status = "completed", completed_at = CURRENT_TIMESTAMP WHERE status = "active"');
      
      // Save again
      const data2 = db.export();
      fs.writeFileSync(dbPath, Buffer.from(data2));
      console.log('✅ Cleaned up active conversations');
    } else {
      console.log('\n✅ No active conversations found');
    }
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    db.close();
  }
  
  console.log('\n🎉 Fix complete! Try sending "PHP" again - the chatbot flow should now work properly.');
}

fixConversationTable().catch(console.error);
