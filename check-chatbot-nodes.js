const initSqlJs = require('sql.js');
const fs = require('fs');
const path = require('path');

async function checkChatbotNodes() {
  console.log('🔍 Checking chatbot nodes and flow configuration...');
  
  const dbPath = path.join(process.env.APPDATA, 'Electron', 'leadwave.db');
  
  if (!fs.existsSync(dbPath)) {
    console.log('❌ Database not found');
    return;
  }
  
  const SQL = await initSqlJs();
  const filebuffer = fs.readFileSync(dbPath);
  const db = new SQL.Database(filebuffer);
  
  try {
    // 1. Check chatbot flows
    console.log('\n🤖 CHATBOT FLOWS:');
    const flows = db.exec('SELECT * FROM chatbot_flows WHERE is_active = 1');
    if (flows.length > 0) {
      const columns = flows[0].columns;
      flows[0].values.forEach((row, index) => {
        const flowData = {};
        row.forEach((value, colIndex) => {
          flowData[columns[colIndex]] = value;
        });
        console.log(`  Flow ${index + 1}:`);
        console.log(`    ID: ${flowData.id}`);
        console.log(`    Name: "${flowData.name}"`);
        console.log(`    Session: ${flowData.session_id}`);
        console.log(`    Keywords: "${flowData.trigger_keywords}"`);
        console.log(`    Active: ${flowData.is_active}`);
        console.log(`    Cooldown: ${flowData.cooldown_minutes} minutes`);
      });
    }
    
    // 2. Check chatbot nodes for each flow
    console.log('\n🔗 CHATBOT NODES:');
    const nodes = db.exec('SELECT * FROM chatbot_nodes ORDER BY flow_id, position');
    if (nodes.length > 0) {
      const columns = nodes[0].columns;
      nodes[0].values.forEach((row, index) => {
        const nodeData = {};
        row.forEach((value, colIndex) => {
          nodeData[columns[colIndex]] = value;
        });
        console.log(`  Node ${index + 1}:`);
        console.log(`    ID: ${nodeData.id}`);
        console.log(`    Flow: ${nodeData.flow_id}`);
        console.log(`    Position: ${nodeData.position}`);
        console.log(`    Type: ${nodeData.node_type}`);
        console.log(`    Content: "${nodeData.content}"`);
        console.log(`    Config: ${nodeData.config || 'null'}`);
        console.log('');
      });
    } else {
      console.log('  ❌ No chatbot nodes found! This is why the flow isn\'t working.');
      console.log('  You need to add nodes to your chatbot flow.');
    }
    
    // 3. Check recent conversations
    console.log('\n💬 RECENT CHATBOT CONVERSATIONS:');
    const conversations = db.exec('SELECT * FROM chatbot_conversations ORDER BY id DESC LIMIT 3');
    if (conversations.length > 0) {
      const columns = conversations[0].columns;
      conversations[0].values.forEach((row, index) => {
        const convData = {};
        row.forEach((value, colIndex) => {
          convData[columns[colIndex]] = value;
        });
        console.log(`  Conversation ${index + 1}:`);
        console.log(`    ID: ${convData.id}`);
        console.log(`    Session: ${convData.session_id}`);
        console.log(`    Flow: ${convData.flow_id}`);
        console.log(`    User: ${convData.user_phone}`);
        console.log(`    Current Node: ${convData.current_node_id}`);
        console.log(`    Active: ${convData.is_active}`);
        console.log(`    Started: ${convData.started_at}`);
        console.log(`    Last Activity: ${convData.last_activity}`);
        console.log('');
      });
    } else {
      console.log('  No chatbot conversations found');
    }
    
    // 4. Check if there are any application logs
    console.log('\n📋 DIAGNOSIS:');
    
    if (flows.length === 0) {
      console.log('  ❌ No active chatbot flows found');
    } else if (nodes.length === 0) {
      console.log('  ❌ ISSUE FOUND: Chatbot flow exists but has no nodes!');
      console.log('  💡 SOLUTION: Add nodes to your chatbot flow in the UI');
    } else {
      // Check if nodes have proper content
      let hasValidNodes = false;
      if (nodes.length > 0) {
        nodes[0].values.forEach(row => {
          const contentIndex = nodes[0].columns.indexOf('content');
          const content = row[contentIndex];
          if (content && content !== 'undefined' && content.trim() !== '') {
            hasValidNodes = true;
          }
        });
      }
      
      if (!hasValidNodes) {
        console.log('  ❌ ISSUE FOUND: Chatbot nodes exist but have no valid content!');
        console.log('  💡 SOLUTION: Edit your chatbot flow nodes to add proper message content');
      } else {
        console.log('  ✅ Chatbot flow and nodes look correct');
        console.log('  🤔 The issue might be in the message processing logic');
        console.log('  💡 Check the application logs for errors during message processing');
      }
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    db.close();
  }
}

checkChatbotNodes().catch(console.error);
