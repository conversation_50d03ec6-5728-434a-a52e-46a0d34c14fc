const initSqlJs = require('sql.js');
const path = require('path');
const fs = require('fs').promises;
const { app } = require('electron');

class DatabaseService {
  constructor(dbName = 'leadwave.db') {
    // Use app.getPath('userData') for production, fallback to process.cwd() for development
    let dataDir;
    try {
      if (app && app.getPath) {
        dataDir = app.getPath('userData');
      } else {
        // Fallback for development or when app is not available
        dataDir = path.join(process.cwd(), 'data');
      }
    } catch (error) {
      // Fallback if app is not available (development mode)
      dataDir = path.join(process.cwd(), 'data');
    }

    this.dbPath = path.join(dataDir, dbName);
    this.db = null;
    this.SQL = null;
    this.isInitialized = false;
    this.isShuttingDown = false;
  }

  async initialize() {
    try {
      // Initialize sql.js
      this.SQL = await initSqlJs();

      // Ensure data directory exists
      const dataDir = path.dirname(this.dbPath);
      await fs.mkdir(dataDir, { recursive: true });

      // Check if database exists and is valid
      const dbExists = await fs.access(this.dbPath).then(() => true).catch(() => false);

      let filebuffer = null;
      let isFirstTimeInstall = false;
      let isCorruptedDatabase = false;

      if (dbExists) {
        try {
          // Load existing database
          filebuffer = await fs.readFile(this.dbPath);

          // Validate database file is not empty or corrupted
          if (filebuffer.length === 0) {
            isCorruptedDatabase = true;
            filebuffer = null;
          }
        } catch (error) {
          isCorruptedDatabase = true;
          filebuffer = null;
        }
      } else {
        isFirstTimeInstall = true;
      }

      // If database is corrupted, treat as first-time install for fresh start
      if (isCorruptedDatabase) {
        isFirstTimeInstall = true;
        // Remove corrupted database file
        try {
          await fs.unlink(this.dbPath);
        } catch (error) {
          // Ignore error if file doesn't exist
        }
      }

      // Create database connection
      this.db = new this.SQL.Database(filebuffer);

      // Enable foreign keys
      this.db.run('PRAGMA foreign_keys = ON');

      // Check foreign key status
      const fkStatus = this.db.prepare("PRAGMA foreign_keys");
      fkStatus.step();
      fkStatus.free();

      // Create all tables
      await this.createTables();

      // Insert default settings for new installations
      await this.insertDefaultSettings();

      // Add missing columns for compatibility
      await this.addMissingColumns();
      await this.runCallResponderMigrations();

      // Clear data for first-time installations or corrupted databases
      if (isFirstTimeInstall) {
        await this.clearUserData();
        // Also clear any existing auth sessions for fresh start
        await this.clearAuthSessions();
      }

      // Save database to file
      await this.saveDatabase();

      this.isInitialized = true;

      // Add attachment columns to chatbot_nodes if they don't exist
      // await this.addChatbotAttachmentColumns(); // Temporarily disabled

      // Database initialized successfully

      return { success: true };
    } catch (error) {
      console.error('❌ Database initialization error:', error);
      throw error;
    }
  }

  async createTables() {
    const tables = [
      // WhatsApp Devices/Sessions
      `CREATE TABLE IF NOT EXISTS whatsapp_sessions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        session_id TEXT UNIQUE NOT NULL,
        name TEXT NOT NULL,
        device_name TEXT, -- Friendly device name
        phone_number TEXT,
        profile_picture TEXT, -- Profile picture URL
        status TEXT DEFAULT 'disconnected', -- disconnected, connecting, connected, qr_ready
        qr_code TEXT,
        last_connected DATETIME,
        last_seen DATETIME, -- Last activity timestamp
        connected_at DATETIME, -- When session was connected
        disconnected_at DATETIME, -- When session was disconnected
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        is_active BOOLEAN DEFAULT 1,
        session_data TEXT -- JSON string for storing session info
      )`,

      // Message Templates
      `CREATE TABLE IF NOT EXISTS message_templates (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL UNIQUE, -- Added UNIQUE constraint to prevent duplicates
        category TEXT DEFAULT 'general', -- welcome, marketing, support, etc.
        type TEXT DEFAULT 'text', -- text, image, document, contact, poll, buttons, list, location, video, audio, cta_button, copy_code, flow, mixed_buttons
        content TEXT NOT NULL,
        variables TEXT, -- JSON array of variable names
        attachments TEXT, -- JSON array of attachment paths/URLs
        buttons TEXT, -- JSON array of button configurations
        list_sections TEXT, -- JSON array of list sections for list templates

        poll_options TEXT, -- JSON array of poll options
        contact_info TEXT, -- JSON object with contact information
        location_info TEXT, -- JSON object with location coordinates
        media_settings TEXT, -- JSON object with media-specific settings (caption, viewOnce, etc.)
        interactive_settings TEXT, -- JSON object with interactive message settings
        is_active BOOLEAN DEFAULT 1,
        usage_count INTEGER DEFAULT 0,
        last_used DATETIME,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        cta_data TEXT, -- JSON object for CTA button configuration
        copy_data TEXT, -- JSON object for copy code button configuration
        flow_data TEXT, -- JSON object for flow message configuration
        mixed_buttons_data TEXT -- JSON array for mixed interactive buttons configuration
      )`,

      // Contacts
      `CREATE TABLE IF NOT EXISTS contacts (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        phone_number TEXT UNIQUE NOT NULL,
        name TEXT,
        email TEXT,
        company TEXT,
        position TEXT,
        notes TEXT,
        tags TEXT, -- JSON array of tags
        custom_fields TEXT, -- JSON object for custom fields
        var1 TEXT, -- Custom variable 1
        var2 TEXT, -- Custom variable 2
        var3 TEXT, -- Custom variable 3
        var4 TEXT, -- Custom variable 4
        var5 TEXT, -- Custom variable 5
        var6 TEXT, -- Custom variable 6
        var7 TEXT, -- Custom variable 7
        var8 TEXT, -- Custom variable 8
        var9 TEXT, -- Custom variable 9
        var10 TEXT, -- Custom variable 10
        whatsapp_verified BOOLEAN DEFAULT 0, -- Whether number is verified on WhatsApp
        verification_status TEXT DEFAULT 'pending', -- pending, verified, invalid
        verification_date DATETIME,
        is_active BOOLEAN DEFAULT 1,
        last_message_at DATETIME,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,

      // Contact Groups
      `CREATE TABLE IF NOT EXISTS contact_groups (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        description TEXT,
        color TEXT DEFAULT '#3b82f6',
        is_active BOOLEAN DEFAULT 1,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,

      // Contact Group Members
      `CREATE TABLE IF NOT EXISTS contact_group_members (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        group_id INTEGER NOT NULL,
        contact_id INTEGER NOT NULL,
        added_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (group_id) REFERENCES contact_groups(id) ON DELETE RESTRICT,
        FOREIGN KEY (contact_id) REFERENCES contacts(id) ON DELETE RESTRICT,
        UNIQUE(group_id, contact_id)
      )`,

      // Bulk Message Campaigns
      `CREATE TABLE IF NOT EXISTS bulk_campaigns (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        template_id INTEGER,
        session_ids TEXT NOT NULL, -- JSON array of session IDs for multi-device support
        message_content TEXT, -- Actual message content (from template or custom)
        message_type TEXT DEFAULT 'text', -- text, template, media, etc.
        contact_group_ids TEXT, -- JSON array of contact group IDs
        device_rotation BOOLEAN DEFAULT 1, -- Whether to rotate between devices
        attachment_data TEXT, -- JSON object with attachment file data and type
        status TEXT DEFAULT 'draft', -- draft, scheduled, pending, running, completed, paused, stopped, failed
        total_contacts INTEGER DEFAULT 0,
        sent_count INTEGER DEFAULT 0,
        failed_count INTEGER DEFAULT 0,
        delivery_delay INTEGER DEFAULT 5, -- seconds between messages
        max_retries INTEGER DEFAULT 3, -- maximum retry attempts for failed messages
        scheduled_at DATETIME,
        started_at DATETIME,
        completed_at DATETIME,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (template_id) REFERENCES message_templates(id) ON DELETE SET NULL
      )`,

      // Bulk Campaign Recipients
      `CREATE TABLE IF NOT EXISTS bulk_campaign_recipients (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        campaign_id INTEGER NOT NULL,
        contact_id INTEGER NOT NULL,
        status TEXT DEFAULT 'pending', -- pending, sent, failed, delivered, retry
        sent_at DATETIME,
        delivered_at DATETIME,
        error_message TEXT,
        message_id TEXT, -- WhatsApp message ID
        retry_count INTEGER DEFAULT 0,
        session_id TEXT, -- Which session was used to send this message
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (campaign_id) REFERENCES bulk_campaigns(id) ON DELETE CASCADE,
        FOREIGN KEY (contact_id) REFERENCES contacts(id)
      )`,

      // Message History
      `CREATE TABLE IF NOT EXISTS message_history (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        session_id INTEGER NOT NULL,
        contact_phone TEXT NOT NULL,
        message_id TEXT, -- WhatsApp message ID
        direction TEXT NOT NULL, -- incoming, outgoing
        message_type TEXT DEFAULT 'text', -- text, image, document, audio, video
        content TEXT,
        media_path TEXT,
        timestamp DATETIME NOT NULL,
        status TEXT, -- sent, delivered, read, failed
        campaign_id INTEGER, -- if sent via bulk campaign
        template_id INTEGER, -- if sent using template
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (session_id) REFERENCES whatsapp_sessions(id),
        FOREIGN KEY (campaign_id) REFERENCES bulk_campaigns(id),
        FOREIGN KEY (template_id) REFERENCES message_templates(id)
      )`,

      // Auto Reply Rules
      `CREATE TABLE IF NOT EXISTS auto_reply_rules (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        session_id TEXT NOT NULL,
        name TEXT NOT NULL,
        response TEXT,
        template_id INTEGER,
        is_active BOOLEAN DEFAULT 1,
        priority INTEGER DEFAULT 1,
        cooldown_minutes INTEGER DEFAULT 0, -- cooldown period in minutes
        response_count INTEGER DEFAULT 0,
        last_used DATETIME,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (template_id) REFERENCES message_templates(id)
      )`,

      // Chatbot Flows
      `CREATE TABLE IF NOT EXISTS chatbot_flows (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        session_id TEXT NOT NULL,
        name TEXT NOT NULL,
        description TEXT,
        trigger_keywords TEXT NOT NULL, -- comma separated keywords
        keyword_match_type TEXT DEFAULT 'contains', -- exact, contains, starts_with, ends_with
        keyword_case_sensitive BOOLEAN DEFAULT 0, -- case sensitive matching
        is_active BOOLEAN DEFAULT 1,
        welcome_message TEXT,
        fallback_message TEXT,
        cooldown_minutes INTEGER DEFAULT 0, -- cooldown period in minutes
        conversation_count INTEGER DEFAULT 0,
        last_triggered DATETIME,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,

      // Chatbot Nodes
      `CREATE TABLE IF NOT EXISTS chatbot_nodes (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        flow_id INTEGER NOT NULL,
        name TEXT NOT NULL,
        message TEXT NOT NULL,
        node_type TEXT NOT NULL, -- message, question, action, condition
        options TEXT, -- JSON array of options for question nodes
        next_node_id INTEGER,
        position INTEGER DEFAULT 0,
        template_id INTEGER,
        attachment_data TEXT, -- JSON object with attachment file data and type
        attachment_type TEXT, -- image, video, audio, document
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (flow_id) REFERENCES chatbot_flows(id) ON DELETE CASCADE,
        FOREIGN KEY (template_id) REFERENCES message_templates(id)
      )`,

      // Chatbot Conversations (for tracking user conversations)
      `CREATE TABLE IF NOT EXISTS chatbot_conversations (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        session_id TEXT NOT NULL,
        flow_id INTEGER NOT NULL,
        user_phone TEXT NOT NULL,
        current_node_id INTEGER,
        conversation_data TEXT, -- JSON data for storing user responses
        is_active BOOLEAN DEFAULT 1,
        started_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        last_activity DATETIME DEFAULT CURRENT_TIMESTAMP,
        completed_at DATETIME,
        FOREIGN KEY (flow_id) REFERENCES chatbot_flows(id) ON DELETE CASCADE
      )`,

      // Chatbot Saved Data (for Action nodes)
      `CREATE TABLE IF NOT EXISTS chatbot_saved_data (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        conversation_id INTEGER NOT NULL,
        flow_id INTEGER NOT NULL,
        data TEXT NOT NULL, -- JSON object with saved data
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (conversation_id) REFERENCES chatbot_conversations(id) ON DELETE CASCADE,
        FOREIGN KEY (flow_id) REFERENCES chatbot_flows(id) ON DELETE CASCADE
      )`,

      // Auto Reply Cooldowns (for tracking cooldown periods)
      `CREATE TABLE IF NOT EXISTS auto_reply_cooldowns (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        rule_id INTEGER NOT NULL,
        user_phone TEXT NOT NULL,
        last_reply_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (rule_id) REFERENCES auto_reply_rules(id) ON DELETE CASCADE,
        UNIQUE(rule_id, user_phone)
      )`,

      // Chatbot Flow Cooldowns (for tracking per-user cooldown periods)
      `CREATE TABLE IF NOT EXISTS chatbot_flow_cooldowns (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        flow_id INTEGER NOT NULL,
        user_phone TEXT NOT NULL,
        last_triggered_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (flow_id) REFERENCES chatbot_flows(id) ON DELETE CASCADE,
        UNIQUE(flow_id, user_phone)
      )`,

      // Call Response Settings - Enhanced for advanced call responder
      `CREATE TABLE IF NOT EXISTS call_responses (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        session_id TEXT NOT NULL, -- Changed to TEXT to match whatsapp_sessions.session_id
        name TEXT NOT NULL,
        call_types TEXT NOT NULL, -- JSON array: ['received', 'outgoing', 'missed', 'rejected']
        message_type TEXT DEFAULT 'text', -- 'text' or 'template'
        message_content TEXT, -- Custom message content
        template_id INTEGER, -- Template ID if using template
        attachment_file TEXT, -- File path for attachment
        attachment_type TEXT, -- 'image', 'video', 'audio', 'document'
        delay_minutes INTEGER DEFAULT 1, -- Delay in minutes after call ends
        is_active BOOLEAN DEFAULT 1,
        usage_count INTEGER DEFAULT 0,
        last_used DATETIME,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (template_id) REFERENCES message_templates(id)
      )`,



      // Application Settings
      `CREATE TABLE IF NOT EXISTS app_settings (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        key TEXT UNIQUE NOT NULL,
        value TEXT,
        type TEXT DEFAULT 'string', -- string, number, boolean, json
        description TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,

      // Backup History
      `CREATE TABLE IF NOT EXISTS backup_history (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        backup_id TEXT UNIQUE NOT NULL,
        timestamp DATETIME NOT NULL,
        description TEXT,
        file_path TEXT,
        google_drive_file_id TEXT,
        encrypted BOOLEAN DEFAULT 0,
        size INTEGER,
        includes TEXT, -- JSON object with backup includes
        status TEXT DEFAULT 'completed', -- completed, failed, in_progress
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,

      // Google Drive Configuration
      `CREATE TABLE IF NOT EXISTS google_drive_config (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        folder_id TEXT,
        folder_url TEXT,
        credentials TEXT, -- Encrypted JSON credentials
        auto_upload BOOLEAN DEFAULT 0,
        auto_backup_enabled BOOLEAN DEFAULT 0,
        auto_backup_schedule TEXT DEFAULT '0 2 * * *', -- Daily at 2 AM
        retention_days INTEGER DEFAULT 30,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,

      // Backup Schedules
      `CREATE TABLE IF NOT EXISTS backup_schedules (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        schedule_pattern TEXT NOT NULL, -- Cron pattern
        enabled BOOLEAN DEFAULT 1,
        include_database BOOLEAN DEFAULT 1,
        include_settings BOOLEAN DEFAULT 1,
        include_templates BOOLEAN DEFAULT 1,
        include_contacts BOOLEAN DEFAULT 1,
        include_attachments BOOLEAN DEFAULT 1,
        encrypt_backup BOOLEAN DEFAULT 1,
        upload_to_drive BOOLEAN DEFAULT 0,
        last_run DATETIME,
        next_run DATETIME,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,

      // Activity Logs
      `CREATE TABLE IF NOT EXISTS activity_logs (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        session_id INTEGER,
        action TEXT, -- action for compatibility
        action_type TEXT NOT NULL, -- message_sent, contact_added, campaign_started, etc.
        description TEXT NOT NULL,
        metadata TEXT, -- JSON object with additional data
        timestamp DATETIME DEFAULT CURRENT_TIMESTAMP, -- timestamp for compatibility
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (session_id) REFERENCES whatsapp_sessions(id)
      )`
    ];

    for (const sql of tables) {
      this.db.run(sql);
    }

    // Handle template duplicates migration
    await this.handleTemplateDuplicatesMigration();

    // Clean up any existing duplicates immediately
    await this.cleanupTemplateDuplicates();

    // Create indexes for better performance
    const indexes = [
      'CREATE INDEX IF NOT EXISTS idx_contacts_phone ON contacts(phone_number)',
      'CREATE INDEX IF NOT EXISTS idx_message_history_session_contact ON message_history(session_id, contact_phone)',
      'CREATE INDEX IF NOT EXISTS idx_message_history_timestamp ON message_history(timestamp)',
      'CREATE INDEX IF NOT EXISTS idx_bulk_recipients_campaign ON bulk_campaign_recipients(campaign_id)',
      'CREATE INDEX IF NOT EXISTS idx_activity_logs_session ON activity_logs(session_id)',
      'CREATE INDEX IF NOT EXISTS idx_activity_logs_created ON activity_logs(created_at)'
    ];

    for (const sql of indexes) {
      this.db.run(sql);
    }

    // Apply database migrations
    await this.applyMigrations();

    // Add new columns for interactive message types (if they don't exist)
    try {
      this.db.run(`ALTER TABLE message_templates ADD COLUMN cta_data TEXT`);
    } catch (error) {
      // Column already exists, ignore error
    }

    try {
      this.db.run(`ALTER TABLE message_templates ADD COLUMN copy_data TEXT`);
    } catch (error) {
      // Column already exists, ignore error
    }

    try {
      this.db.run(`ALTER TABLE message_templates ADD COLUMN flow_data TEXT`);
    } catch (error) {
      // Column already exists, ignore error
    }



    // Insert default settings
    await this.insertDefaultSettings();

    // Run migrations for enhanced Auto Reply and Chatbot modules
    await this.runAutoReplyChatbotMigrations();

    // Run AI Chatbot module migrations
    await this.runAIChatbotMigrations();
  }

  /**
   * Run migrations for Auto Reply and Chatbot modules
   */
  async runAutoReplyChatbotMigrations() {
    try {
      console.log('🔄 Running Auto Reply and Chatbot migrations...');

      // Force migration for auto_reply_rules to remove keywords
      console.log('🔄 Checking auto_reply_rules table schema...');

      try {
        const autoReplyColumns = this.db.exec("PRAGMA table_info(auto_reply_rules)")[0];
        if (autoReplyColumns) {
          const columnNames = autoReplyColumns.values.map(row => row[1]);

          // Check if we need to remove keywords column (new schema)
          if (columnNames.includes('keywords') || columnNames.includes('match_type')) {
            console.log('🔄 FORCE MIGRATING auto_reply_rules table to remove keywords and match_type...');

            // Backup existing data
            let existingRules = [];
            try {
              const result = this.db.exec("SELECT * FROM auto_reply_rules");
              if (result.length > 0) {
                existingRules = result[0].values;
              }
            } catch (e) {
              console.log('No existing auto_reply_rules data to migrate');
            }

            // Drop and recreate table with new schema
            this.db.run("DROP TABLE IF EXISTS auto_reply_rules_backup");
            this.db.run("DROP TABLE IF EXISTS auto_reply_rules");

            // Create new table
            this.db.run(`CREATE TABLE auto_reply_rules (
              id INTEGER PRIMARY KEY AUTOINCREMENT,
              session_id TEXT NOT NULL,
              name TEXT NOT NULL,
              response TEXT,
              template_id INTEGER,
              is_active BOOLEAN DEFAULT 1,
              priority INTEGER DEFAULT 1,
              cooldown_minutes INTEGER DEFAULT 0,
              response_count INTEGER DEFAULT 0,
              last_used DATETIME,
              created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
              updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
              FOREIGN KEY (template_id) REFERENCES message_templates(id)
            )`);

            // Migrate data if exists
            for (const row of existingRules) {
              try {
                this.db.run(`
                  INSERT INTO auto_reply_rules (
                    session_id, name, response, template_id,
                    is_active, priority, cooldown_minutes, response_count, created_at, updated_at
                  ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                `, [
                  row[1] || 'default_session', // session_id
                  row[2] || 'Migrated Rule', // name
                  row[4] || row[6] || 'Hello!', // response
                  row[7] || null, // template_id
                  row[8] || 1, // is_active
                  row[10] || 1, // priority
                  0, // cooldown_minutes
                  row[9] || 0, // response_count
                  row[11] || new Date().toISOString(), // created_at
                  row[12] || new Date().toISOString() // updated_at
                ]);
              } catch (e) {
                console.warn('Failed to migrate auto_reply_rule:', e.message);
              }
            }

            console.log('✅ auto_reply_rules table migrated');
          } else {
            console.log('✅ auto_reply_rules table already has correct schema (no keywords/match_type)');
          }
        } else {
          console.log('⚠️ auto_reply_rules table does not exist - will be created with new schema');
        }
      } catch (e) {
        console.log('auto_reply_rules table does not exist, will be created fresh');

        // Create the table with new schema if it doesn't exist
        try {
          this.db.run(`CREATE TABLE IF NOT EXISTS auto_reply_rules (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            session_id TEXT NOT NULL,
            name TEXT NOT NULL,
            response TEXT,
            template_id INTEGER,
            is_active BOOLEAN DEFAULT 1,
            priority INTEGER DEFAULT 1,
            cooldown_minutes INTEGER DEFAULT 0,
            response_count INTEGER DEFAULT 0,
            last_used DATETIME,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (template_id) REFERENCES message_templates(id)
          )`);
          console.log('✅ auto_reply_rules table created with new schema');
        } catch (createError) {
          console.error('❌ Failed to create auto_reply_rules table:', createError.message);
        }
      }

      // Check if chatbot_flows table needs migration
      try {
        const chatbotFlowColumns = this.db.exec("PRAGMA table_info(chatbot_flows)")[0];
        if (chatbotFlowColumns) {
          const columnNames = chatbotFlowColumns.values.map(row => row[1]);

          if (!columnNames.includes('trigger_keywords')) {
            console.log('🔄 Migrating chatbot_flows table...');

            // Add missing columns
            try {
              this.db.run("ALTER TABLE chatbot_flows ADD COLUMN trigger_keywords TEXT DEFAULT 'help'");
            } catch (e) {
              console.log('trigger_keywords column already exists or error:', e.message);
            }
            try {
              this.db.run("ALTER TABLE chatbot_flows ADD COLUMN conversation_count INTEGER DEFAULT 0");
            } catch (e) {
              console.log('conversation_count column already exists or error:', e.message);
            }
            try {
              this.db.run("ALTER TABLE chatbot_flows ADD COLUMN last_triggered DATETIME");
            } catch (e) {
              console.log('last_triggered column already exists or error:', e.message);
            }
            try {
              this.db.run("ALTER TABLE chatbot_flows ADD COLUMN cooldown_minutes INTEGER DEFAULT 0");
            } catch (e) {
              console.log('cooldown_minutes column already exists or error:', e.message);
            }

            console.log('✅ chatbot_flows table migrated');
          }
        }
      } catch (e) {
        console.log('chatbot_flows table does not exist, will be created fresh');
      }

      // Check if chatbot_nodes table needs attachment columns
      try {
        const chatbotNodeColumns = this.db.exec("PRAGMA table_info(chatbot_nodes)")[0];
        if (chatbotNodeColumns) {
          const columnNames = chatbotNodeColumns.values.map(row => row[1]);

          // Only add missing attachment columns, don't recreate the table
          if (!columnNames.includes('attachment_data')) {
            console.log('🔄 Adding attachment_data column to chatbot_nodes table...');
            try {
              this.db.run("ALTER TABLE chatbot_nodes ADD COLUMN attachment_data TEXT");
              console.log('✅ Added attachment_data column');
            } catch (e) {
              console.log('attachment_data column already exists or error:', e.message);
            }
          }

          if (!columnNames.includes('attachment_type')) {
            console.log('🔄 Adding attachment_type column to chatbot_nodes table...');
            try {
              this.db.run("ALTER TABLE chatbot_nodes ADD COLUMN attachment_type TEXT");
              console.log('✅ Added attachment_type column');
            } catch (e) {
              console.log('attachment_type column already exists or error:', e.message);
            }
          }
        }
      } catch (e) {
        console.log('chatbot_nodes table does not exist, will be created fresh');
      }

      console.log('✅ Auto Reply and Chatbot migrations completed');
    } catch (error) {
      console.error('❌ Error running Auto Reply and Chatbot migrations:', error);
      // Don't throw error to prevent breaking the app
    }
  }

  /**
   * Run AI Chatbot module migrations
   */
  async runAIChatbotMigrations() {
    try {
      console.log('🤖 Running AI Chatbot module migrations...');

      // Check if AI Chatbot tables exist
      const tables = [
        'ai_providers',
        'ai_chatbots',
        'ai_conversations',
        'ai_messages',
        'ai_intents',
        'ai_knowledge_base',
        'ai_global_settings'
      ];

      let needsInitialization = false;

      // Check specifically for ai_providers table (new table)
      try {
        const result = this.db.exec(`SELECT name FROM sqlite_master WHERE type='table' AND name='ai_providers'`);
        if (!result || result.length === 0) {
          needsInitialization = true;
          console.log('🔄 AI Providers table not found, triggering schema update...');
        }
      } catch (error) {
        needsInitialization = true;
        console.log('🔄 Error checking AI Providers table, triggering schema update...');
      }

      // If ai_providers doesn't exist, check other tables too
      if (!needsInitialization) {
        for (const table of tables) {
          try {
            const result = this.db.exec(`SELECT name FROM sqlite_master WHERE type='table' AND name='${table}'`);
            if (!result || result.length === 0) {
              needsInitialization = true;
              break;
            }
          } catch (error) {
            needsInitialization = true;
            break;
          }
        }
      }

      if (needsInitialization) {
        console.log('🔄 Initializing AI Chatbot database schema...');
        await this.createAIChatbotSchema();
        console.log('✅ AI Chatbot schema initialized successfully');
      } else {
        console.log('✅ AI Chatbot tables already exist');
        // Run data migration to fix any model mismatches
        await this.fixAIProviderModelMismatches();
      }

    } catch (error) {
      console.error('❌ Error running AI Chatbot migrations:', error);
      // Don't throw error to prevent breaking the app
    }
  }

  /**
   * Fix AI provider model mismatches
   */
  async fixAIProviderModelMismatches() {
    try {
      console.log('🔧 Checking for AI provider model mismatches...');

      // Get all providers
      const providers = this.db.exec('SELECT * FROM ai_providers WHERE is_active = 1');

      if (!providers || providers.length === 0) {
        console.log('✅ No AI providers found to check');
        return;
      }

      const providerData = providers[0]?.values || [];
      const columns = providers[0]?.columns || [];

      let fixedCount = 0;

      for (const row of providerData) {
        const provider = {};
        columns.forEach((col, index) => {
          provider[col] = row[index];
        });

        const { id, type, model } = provider;
        let newModel = model;
        let needsUpdate = false;

        // Fix Gemini providers with OpenAI models
        if (type === 'gemini' && (model.startsWith('gpt-') || model === 'gpt-3.5-turbo')) {
          newModel = 'gemini-pro';
          needsUpdate = true;
          console.log(`🔧 Fixing Gemini provider "${provider.name}" - changing model from "${model}" to "${newModel}"`);
        }
        // Fix OpenAI providers with Gemini models
        else if (type === 'openai' && model.startsWith('gemini-')) {
          newModel = 'gpt-3.5-turbo';
          needsUpdate = true;
          console.log(`🔧 Fixing OpenAI provider "${provider.name}" - changing model from "${model}" to "${newModel}"`);
        }

        if (needsUpdate) {
          this.db.run(`
            UPDATE ai_providers SET
              model = ?,
              updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
          `, [newModel, id]);
          fixedCount++;
        }
      }

      if (fixedCount > 0) {
        console.log(`✅ Fixed ${fixedCount} AI provider model mismatches`);
      } else {
        console.log('✅ No AI provider model mismatches found');
      }

    } catch (error) {
      console.error('❌ Error fixing AI provider model mismatches:', error);
      // Don't throw error to prevent breaking the app
    }
  }

  /**
   * Create AI Chatbot database schema
   */
  async createAIChatbotSchema() {
    try {
      // AI Providers table - separate configuration for reusability
      this.db.run(`CREATE TABLE IF NOT EXISTS ai_providers (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        type TEXT NOT NULL CHECK (type IN ('openai', 'gemini')),
        api_key TEXT NOT NULL,
        model TEXT NOT NULL,
        temperature REAL DEFAULT 0.7,
        max_tokens INTEGER DEFAULT 1000,
        is_active BOOLEAN DEFAULT 1,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`);

      // Main AI Chatbots table
      this.db.run(`CREATE TABLE IF NOT EXISTS ai_chatbots (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        description TEXT,
        provider_id INTEGER NOT NULL,
        system_prompt TEXT,
        language TEXT DEFAULT 'en',
        is_active BOOLEAN DEFAULT 1,
        session_ids TEXT,
        trigger_keywords TEXT,
        stop_keywords TEXT,
        features TEXT,
        personality TEXT DEFAULT 'professional',
        industry TEXT DEFAULT 'general',
        response_delay INTEGER DEFAULT 1000,
        fallback_message TEXT,
        max_conversation_length INTEGER DEFAULT 50,
        enable_learning BOOLEAN DEFAULT 1,
        confidence_threshold REAL DEFAULT 0.7,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (provider_id) REFERENCES ai_providers(id) ON DELETE RESTRICT
      )`);

      // AI Conversations table
      this.db.run(`CREATE TABLE IF NOT EXISTS ai_conversations (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        chatbot_id INTEGER NOT NULL,
        session_id TEXT NOT NULL,
        user_phone TEXT NOT NULL,
        conversation_id TEXT NOT NULL,
        status TEXT DEFAULT 'active' CHECK (status IN ('active', 'completed', 'escalated', 'timeout')),
        context TEXT,
        satisfaction_score REAL,
        resolved BOOLEAN DEFAULT 0,
        escalated_to_human BOOLEAN DEFAULT 0,
        response_time REAL,
        message_count INTEGER DEFAULT 0,
        language_detected TEXT,
        sentiment_score REAL,
        intent_detected TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (chatbot_id) REFERENCES ai_chatbots(id) ON DELETE CASCADE
      )`);

      // AI Messages table
      this.db.run(`CREATE TABLE IF NOT EXISTS ai_messages (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        conversation_id INTEGER NOT NULL,
        message_type TEXT NOT NULL CHECK (message_type IN ('user', 'bot', 'system')),
        content TEXT NOT NULL,
        metadata TEXT,
        tokens_used INTEGER,
        processing_time REAL,
        confidence_score REAL,
        intent TEXT,
        sentiment TEXT,
        language TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (conversation_id) REFERENCES ai_conversations(id) ON DELETE CASCADE
      )`);

      // AI Intents table
      this.db.run(`CREATE TABLE IF NOT EXISTS ai_intents (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        chatbot_id INTEGER NOT NULL,
        name TEXT NOT NULL,
        description TEXT,
        training_phrases TEXT NOT NULL,
        response_templates TEXT,
        action_type TEXT,
        action_data TEXT,
        confidence_threshold REAL DEFAULT 0.7,
        is_active BOOLEAN DEFAULT 1,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (chatbot_id) REFERENCES ai_chatbots(id) ON DELETE CASCADE
      )`);

      // AI Knowledge Base table
      this.db.run(`CREATE TABLE IF NOT EXISTS ai_knowledge_base (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        chatbot_id INTEGER NOT NULL,
        category TEXT,
        question TEXT NOT NULL,
        answer TEXT NOT NULL,
        keywords TEXT,
        confidence_threshold REAL DEFAULT 0.8,
        usage_count INTEGER DEFAULT 0,
        last_used DATETIME,
        is_active BOOLEAN DEFAULT 1,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (chatbot_id) REFERENCES ai_chatbots(id) ON DELETE CASCADE
      )`);

      // Global AI Settings table
      this.db.run(`CREATE TABLE IF NOT EXISTS ai_global_settings (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        key TEXT UNIQUE NOT NULL,
        value TEXT NOT NULL,
        description TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`);

      // Additional tables for advanced features
      this.db.run(`CREATE TABLE IF NOT EXISTS ai_decision_flows (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        chatbot_id INTEGER NOT NULL,
        name TEXT NOT NULL,
        description TEXT,
        trigger_keywords TEXT,
        flow_data TEXT NOT NULL,
        is_active BOOLEAN DEFAULT 1,
        priority INTEGER DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (chatbot_id) REFERENCES ai_chatbots(id) ON DELETE CASCADE
      )`);

      this.db.run(`CREATE TABLE IF NOT EXISTS ai_form_templates (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        chatbot_id INTEGER NOT NULL,
        name TEXT NOT NULL,
        description TEXT,
        fields TEXT NOT NULL,
        submit_message TEXT,
        validation_rules TEXT,
        is_active BOOLEAN DEFAULT 1,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (chatbot_id) REFERENCES ai_chatbots(id) ON DELETE CASCADE
      )`);

      this.db.run(`CREATE TABLE IF NOT EXISTS ai_form_submissions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        form_template_id INTEGER NOT NULL,
        conversation_id INTEGER NOT NULL,
        user_phone TEXT NOT NULL,
        submission_data TEXT NOT NULL,
        status TEXT DEFAULT 'completed' CHECK (status IN ('in_progress', 'completed', 'abandoned')),
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (form_template_id) REFERENCES ai_form_templates(id) ON DELETE CASCADE,
        FOREIGN KEY (conversation_id) REFERENCES ai_conversations(id) ON DELETE CASCADE
      )`);

      this.db.run(`CREATE TABLE IF NOT EXISTS ai_appointments (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        chatbot_id INTEGER NOT NULL,
        conversation_id INTEGER NOT NULL,
        user_phone TEXT NOT NULL,
        appointment_type TEXT,
        appointment_date DATETIME,
        duration INTEGER,
        status TEXT DEFAULT 'scheduled' CHECK (status IN ('scheduled', 'confirmed', 'cancelled', 'completed')),
        notes TEXT,
        reminder_sent BOOLEAN DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (chatbot_id) REFERENCES ai_chatbots(id) ON DELETE CASCADE,
        FOREIGN KEY (conversation_id) REFERENCES ai_conversations(id) ON DELETE CASCADE
      )`);

      this.db.run(`CREATE TABLE IF NOT EXISTS ai_learning_data (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        chatbot_id INTEGER NOT NULL,
        conversation_id INTEGER NOT NULL,
        user_input TEXT NOT NULL,
        bot_response TEXT NOT NULL,
        user_feedback TEXT,
        correction TEXT,
        context TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (chatbot_id) REFERENCES ai_chatbots(id) ON DELETE CASCADE,
        FOREIGN KEY (conversation_id) REFERENCES ai_conversations(id) ON DELETE CASCADE
      )`);

      // Create indexes for performance
      this.db.run(`CREATE INDEX IF NOT EXISTS idx_ai_providers_type ON ai_providers(type)`);
      this.db.run(`CREATE INDEX IF NOT EXISTS idx_ai_providers_is_active ON ai_providers(is_active)`);
      this.db.run(`CREATE INDEX IF NOT EXISTS idx_ai_chatbots_provider_id ON ai_chatbots(provider_id)`);
      this.db.run(`CREATE INDEX IF NOT EXISTS idx_ai_conversations_chatbot_id ON ai_conversations(chatbot_id)`);
      this.db.run(`CREATE INDEX IF NOT EXISTS idx_ai_conversations_user_phone ON ai_conversations(user_phone)`);
      this.db.run(`CREATE INDEX IF NOT EXISTS idx_ai_conversations_created_at ON ai_conversations(created_at)`);
      this.db.run(`CREATE INDEX IF NOT EXISTS idx_ai_messages_conversation_id ON ai_messages(conversation_id)`);
      this.db.run(`CREATE INDEX IF NOT EXISTS idx_ai_messages_created_at ON ai_messages(created_at)`);
      this.db.run(`CREATE INDEX IF NOT EXISTS idx_ai_knowledge_base_chatbot_id ON ai_knowledge_base(chatbot_id)`);
      this.db.run(`CREATE INDEX IF NOT EXISTS idx_ai_intents_chatbot_id ON ai_intents(chatbot_id)`);
      this.db.run(`CREATE INDEX IF NOT EXISTS idx_ai_appointments_chatbot_id ON ai_appointments(chatbot_id)`);
      this.db.run(`CREATE INDEX IF NOT EXISTS idx_ai_appointments_date ON ai_appointments(appointment_date)`);

      // Insert default global settings
      this.db.run(`INSERT OR IGNORE INTO ai_global_settings (key, value, description) VALUES
        ('global_config', '{"enableGlobalFallback": true, "enableAnalytics": true, "enableLearning": true}', 'Global AI configuration settings'),
        ('rate_limits', '{"maxConcurrentConversations": 100, "rateLimitPerUser": 10, "rateLimitWindow": 60}', 'Rate limiting configuration'),
        ('features', '{"enableSentimentAnalysis": true, "enableLanguageDetection": true, "enableProfanityFilter": true}', 'Global feature flags')`);

      // Add keyword columns to existing ai_chatbots table if they don't exist
      try {
        const tableInfo = this.db.prepare('PRAGMA table_info(ai_chatbots)').all();
        const columnNames = tableInfo.map(col => col.name);

        if (!columnNames.includes('trigger_keywords')) {
          console.log('🔄 Adding trigger_keywords column to ai_chatbots table...');
          this.db.run('ALTER TABLE ai_chatbots ADD COLUMN trigger_keywords TEXT');
          console.log('✅ Added trigger_keywords column');
        }

        if (!columnNames.includes('stop_keywords')) {
          console.log('🔄 Adding stop_keywords column to ai_chatbots table...');
          this.db.run('ALTER TABLE ai_chatbots ADD COLUMN stop_keywords TEXT');
          console.log('✅ Added stop_keywords column');
        }
      } catch (migrationError) {
        console.log('⚠️ Keyword columns migration error (may already exist):', migrationError.message);
      }

      console.log('✅ AI Chatbot database schema created successfully');
    } catch (error) {
      console.error('❌ Error creating AI Chatbot schema:', error);
      throw error;
    }
  }

  async insertDefaultSettings() {
    const defaultSettings = [
      { key: 'app_theme', value: 'light', type: 'string', description: 'Application theme' },
      { key: 'message_delay', value: '5', type: 'number', description: 'Default delay between bulk messages (seconds)' },
      { key: 'auto_reply_enabled', value: 'true', type: 'boolean', description: 'Enable auto reply globally' },
      { key: 'call_response_enabled', value: 'true', type: 'boolean', description: 'Enable call response globally' },
      { key: 'max_sessions', value: '10', type: 'number', description: 'Maximum WhatsApp sessions allowed' },
      { key: 'backup_enabled', value: 'true', type: 'boolean', description: 'Enable automatic database backups' },

      { key: 'backup_interval', value: '24', type: 'number', description: 'Backup interval in hours' },
      { key: 'backup_auto_upload', value: 'false', type: 'boolean', description: 'Auto upload backups to Google Drive' },
      { key: 'backup_encryption', value: 'true', type: 'boolean', description: 'Encrypt backups by default' },
      { key: 'backup_retention_days', value: '30', type: 'number', description: 'Number of days to keep backups' },
      { key: 'google_drive_folder_id', value: '', type: 'string', description: 'Google Drive folder ID for backups' },
      { key: 'google_drive_folder_url', value: '', type: 'string', description: 'Google Drive folder URL for backups' },
      { key: 'app_language', value: 'en', type: 'string', description: 'Application language' },
      { key: 'window_show_title_bar', value: 'true', type: 'boolean', description: 'Show window title bar and menu' }
    ];

    for (const setting of defaultSettings) {
      this.db.run(`
        INSERT OR IGNORE INTO app_settings (key, value, type, description)
        VALUES (?, ?, ?, ?)
      `, [setting.key, setting.value, setting.type, setting.description]);
    }
  }

  // Save database to file
  async saveDatabase() {
    try {
      if (!this.db) {
        console.warn('⚠️ Database not initialized, skipping save');
        return;
      }

      if (this.isShuttingDown) {
        console.warn('⚠️ Database is shutting down, skipping save');
        return;
      }

      // Saving database
      const data = this.db.export();

      // Ensure directory exists before writing
      await fs.mkdir(path.dirname(this.dbPath), { recursive: true });

      // Write to temporary file first, then rename for atomic operation
      const tempPath = this.dbPath + '.tmp';

      try {
        await fs.writeFile(tempPath, data);
        // Atomic rename
        await fs.rename(tempPath, this.dbPath);
      } catch (error) {
        // Clean up temp file if it exists
        try {
          await fs.unlink(tempPath);
        } catch (cleanupError) {
          // Ignore cleanup errors
        }

        // If EBADF error, wait and retry once
        if (error.code === 'EBADF' || error.message.includes('bad file descriptor')) {
          console.warn('File descriptor error during database save, retrying...');
          await new Promise(resolve => setTimeout(resolve, 100));
          await fs.writeFile(tempPath, data);
          await fs.rename(tempPath, this.dbPath);
        } else {
          throw error;
        }
      }

      // Database saved successfully
    } catch (error) {
      console.error('❌ Error saving database:', error);
      console.error('Database path:', this.dbPath);
      console.error('Error details:', error.message);

      // Clean up temp file if it exists
      try {
        const tempPath = this.dbPath + '.tmp';
        await fs.unlink(tempPath);
      } catch (cleanupError) {
        // Ignore cleanup errors
      }

      throw error; // Re-throw to let caller handle
    }
  }

  // Utility methods for database operations
  async run(sql, params = []) {
    try {
      // Convert undefined values to null and objects to strings for sql.js compatibility
      const cleanParams = params.map(param => {
        if (param === undefined) return null;
        if (param === null) return null;
        if (typeof param === 'object') {
          console.warn('⚠️ Object parameter detected, converting to JSON string:', param);
          return JSON.stringify(param);
        }
        return param;
      });

      // Executing SQL query

      // Use prepared statement approach for better error handling
      let result, lastID = null, changes = 0;

      if (sql.trim().toUpperCase().startsWith('INSERT')) {
        // For INSERT statements, use prepared statement
        const stmt = this.db.prepare(sql);
        try {
          stmt.run(cleanParams);
          changes = this.db.getRowsModified();

          // Get last inserted row ID
          if (changes > 0) {
            const lastIdStmt = this.db.prepare("SELECT last_insert_rowid() as lastID");
            if (lastIdStmt.step()) {
              lastID = lastIdStmt.getAsObject().lastID;
            }
            lastIdStmt.free();
          }
        } finally {
          stmt.free();
        }
      } else {
        // For other statements, use the regular run method
        result = this.db.run(sql, cleanParams);
        changes = this.db.getRowsModified();
      }

      // SQL executed successfully

      // Save database with error handling
      try {
        await this.saveDatabase();
      } catch (saveError) {
        // Continue execution even if save fails - log only in development
        if (process.env.NODE_ENV === 'development') {
          console.error('❌ Failed to save database after operation:', saveError.message);
        }
      }

      // Debug: Log if INSERT didn't work (development only)
      if (process.env.NODE_ENV === 'development' && sql.trim().toUpperCase().startsWith('INSERT') && changes === 0) {
        console.error('❌ INSERT failed - no rows affected');
        console.error('SQL:', sql);
        console.error('Params:', cleanParams);

        // Check foreign key violations
        try {
          const fkCheck = this.db.prepare("PRAGMA foreign_key_check");
          const violations = [];
          while (fkCheck.step()) {
            violations.push(fkCheck.getAsObject());
          }
          fkCheck.free();
          if (violations.length > 0) {
            console.error('Foreign key violations:', violations);
          } else {
            console.log('🔧 No foreign key violations found');
          }

          // Check if message_templates table has any data
          const templateCheck = this.db.prepare("SELECT COUNT(*) as count FROM message_templates");
          if (templateCheck.step()) {
            const templateCount = templateCheck.getAsObject().count;
            console.log('🔧 Message templates count:', templateCount);
          }
          templateCheck.free();

          // Check table constraints and indexes
          const constraintCheck = this.db.prepare("SELECT sql FROM sqlite_master WHERE type='index' AND tbl_name='bulk_campaigns'");
          console.log('🔧 Table indexes:');
          while (constraintCheck.step()) {
            console.log('  -', constraintCheck.getAsObject().sql);
          }
          constraintCheck.free();

          // Try a simple test with minimal data
          console.log('🔧 Attempting minimal INSERT test...');
          try {
            const minimalTest = this.db.run("INSERT INTO bulk_campaigns (name, session_ids) VALUES (?, ?)", ['MINIMAL_TEST', '[]']);
            const minimalChanges = this.db.getRowsModified();
            console.log('🔧 Minimal test changes:', minimalChanges);
            if (minimalChanges > 0) {
              // Clean up test record
              this.db.run("DELETE FROM bulk_campaigns WHERE name = 'MINIMAL_TEST'");
            }
          } catch (minimalError) {
            console.error('🔧 Minimal test error:', minimalError);
          }

        } catch (e) {
          console.error('Could not check foreign keys:', e);
        }
      }



      return {
        success: true,
        lastID: lastID,
        insertId: lastID, // For compatibility
        changes: changes,
        data: { lastID: lastID }
      };
    } catch (error) {
      console.error('Database query error:', error);
      return { success: false, error: error.message };
    }
  }

  async get(sql, params = []) {
    try {
      // Convert undefined values to null for sql.js compatibility
      const cleanParams = params.map(param => param === undefined ? null : param);
      const stmt = this.db.prepare(sql);
      const result = stmt.getAsObject(cleanParams);
      stmt.free();

      // Return the data directly if it exists, otherwise return null
      return Object.keys(result).length > 0 ? result : null;
    } catch (error) {
      console.error('Database query error:', error);
      return null;
    }
  }

  async all(sql, params = []) {
    try {
      // Convert undefined values to null for sql.js compatibility
      const cleanParams = params.map(param => param === undefined ? null : param);
      const stmt = this.db.prepare(sql);

      // Bind parameters if any are provided
      if (cleanParams.length > 0) {
        stmt.bind(cleanParams);
      }

      const results = [];
      while (stmt.step()) {
        results.push(stmt.getAsObject());
      }
      stmt.free();
      return { success: true, data: Array.isArray(results) ? results : [] };
    } catch (error) {
      console.error('Database query error:', error);
      return { success: false, error: error.message, data: [] };
    }
  }

  async query(sql, params = []) {
    try {
      if (sql.trim().toUpperCase().startsWith('SELECT')) {
        const result = await this.all(sql, params);
        // Ensure data is always an array for SELECT queries
        if (result.success && !Array.isArray(result.data)) {
          result.data = [];
        }
        return result;
      } else {
        return await this.run(sql, params);
      }
    } catch (error) {
      console.error('Database query error:', error);
      return {
        success: false,
        error: error.message,
        data: sql.trim().toUpperCase().startsWith('SELECT') ? [] : null
      };
    }
  }

  /**
   * Execute multiple queries in a transaction
   */
  async transaction(queries) {
    try {
      console.log('🔄 Starting database transaction with', queries.length, 'queries');

      // Begin transaction
      await this.run('BEGIN TRANSACTION');

      const results = [];

      for (let i = 0; i < queries.length; i++) {
        const { sql, params = [] } = queries[i];
        console.log(`📝 Executing query ${i + 1}/${queries.length}:`, sql.substring(0, 100) + '...');

        const result = await this.query(sql, params);

        if (!result.success) {
          console.error(`❌ Query ${i + 1} failed:`, result.error);
          // Rollback on any failure
          await this.run('ROLLBACK');
          return {
            success: false,
            error: `Transaction failed at query ${i + 1}: ${result.error}`,
            failedQueryIndex: i
          };
        }

        results.push(result);
      }

      // Commit transaction
      await this.run('COMMIT');
      console.log('✅ Transaction completed successfully');

      return {
        success: true,
        results: results
      };

    } catch (error) {
      console.error('❌ Transaction error:', error);
      try {
        await this.run('ROLLBACK');
      } catch (rollbackError) {
        console.error('❌ Rollback failed:', rollbackError);
      }

      return {
        success: false,
        error: error.message
      };
    }
  }

  async close() {
    this.isShuttingDown = true;

    if (this.db) {
      try {
        await this.saveDatabase();
        this.db.close();
      } catch (error) {
        console.error('Error closing database:', error);
      }
    }
  }

  // Helper method to get database statistics
  async getStats() {
    const stats = {};

    const tables = [
      'whatsapp_sessions', 'message_templates', 'contacts', 'contact_groups',
      'bulk_campaigns', 'message_history', 'auto_reply_rules'
    ];

    for (const table of tables) {
      try {
        const result = await this.query(`SELECT COUNT(*) as count FROM ${table}`);
        stats[table] = result.success && result.data && result.data.length > 0 ? result.data[0].count : 0;
      } catch (error) {
        console.error(`Error getting stats for table ${table}:`, error);
        stats[table] = 0;
      }
    }

    return stats;
  }

  async addMissingColumns() {
    try {
      // Check and add missing columns to whatsapp_sessions table
      const sessionTableResult = await this.query("PRAGMA table_info(whatsapp_sessions)");
      const sessionTableInfo = sessionTableResult.success && Array.isArray(sessionTableResult.data) ? sessionTableResult.data : [];
      const sessionColumnNames = sessionTableInfo.map(col => col.name);

      if (!sessionColumnNames.includes('profile_picture')) {
        try {
          this.db.run("ALTER TABLE whatsapp_sessions ADD COLUMN profile_picture TEXT");
          console.log('✅ Added profile_picture column to whatsapp_sessions table');
        } catch (error) {
          if (!error.message.includes('duplicate column name')) {
            throw error;
          }
          console.log('⚠️ profile_picture column already exists in whatsapp_sessions table');
        }
      }

      if (!sessionColumnNames.includes('last_seen')) {
        try {
          this.db.run("ALTER TABLE whatsapp_sessions ADD COLUMN last_seen DATETIME");
          console.log('✅ Added last_seen column to whatsapp_sessions table');
        } catch (error) {
          if (!error.message.includes('duplicate column name')) {
            throw error;
          }
          console.log('⚠️ last_seen column already exists in whatsapp_sessions table');
        }
      }

      if (!sessionColumnNames.includes('connected_at')) {
        try {
          this.db.run("ALTER TABLE whatsapp_sessions ADD COLUMN connected_at DATETIME");
          console.log('✅ Added connected_at column to whatsapp_sessions table');
        } catch (error) {
          if (!error.message.includes('duplicate column name')) {
            throw error;
          }
          console.log('⚠️ connected_at column already exists in whatsapp_sessions table');
        }
      }

      if (!sessionColumnNames.includes('disconnected_at')) {
        try {
          this.db.run("ALTER TABLE whatsapp_sessions ADD COLUMN disconnected_at DATETIME");
          console.log('✅ Added disconnected_at column to whatsapp_sessions table');
        } catch (error) {
          if (!error.message.includes('duplicate column name')) {
            throw error;
          }
          console.log('⚠️ disconnected_at column already exists in whatsapp_sessions table');
        }
      }

      // Check and add missing columns to message_templates table
      const templateTableResult = await this.query("PRAGMA table_info(message_templates)");
      const templateTableInfo = templateTableResult.success && Array.isArray(templateTableResult.data) ? templateTableResult.data : [];
      const templateColumnNames = templateTableInfo.map(col => col.name);

      if (!templateColumnNames.includes('type')) {
        try {
          this.db.run("ALTER TABLE message_templates ADD COLUMN type TEXT DEFAULT 'text'");
          console.log('✅ Added type column to message_templates table');
        } catch (error) {
          if (!error.message.includes('duplicate column name')) {
            throw error;
          }
          console.log('⚠️ type column already exists in message_templates table');
        }
      }

      if (!templateColumnNames.includes('buttons')) {
        try {
          this.db.run("ALTER TABLE message_templates ADD COLUMN buttons TEXT");
          console.log('✅ Added buttons column to message_templates table');
        } catch (error) {
          if (!error.message.includes('duplicate column name')) {
            throw error;
          }
          console.log('⚠️ buttons column already exists in message_templates table');
        }
      }

      if (!templateColumnNames.includes('list_sections')) {
        try {
          this.db.run("ALTER TABLE message_templates ADD COLUMN list_sections TEXT");
          console.log('✅ Added list_sections column to message_templates table');
        } catch (error) {
          if (!error.message.includes('duplicate column name')) {
            throw error;
          }
          console.log('⚠️ list_sections column already exists in message_templates table');
        }
      }



      if (!templateColumnNames.includes('poll_options')) {
        try {
          this.db.run("ALTER TABLE message_templates ADD COLUMN poll_options TEXT");
          console.log('✅ Added poll_options column to message_templates table');
        } catch (error) {
          if (!error.message.includes('duplicate column name')) {
            throw error;
          }
          console.log('⚠️ poll_options column already exists in message_templates table');
        }
      }

      if (!templateColumnNames.includes('contact_info')) {
        try {
          this.db.run("ALTER TABLE message_templates ADD COLUMN contact_info TEXT");
          console.log('✅ Added contact_info column to message_templates table');
        } catch (error) {
          if (!error.message.includes('duplicate column name')) {
            throw error;
          }
          console.log('⚠️ contact_info column already exists in message_templates table');
        }
      }

      if (!templateColumnNames.includes('location_info')) {
        try {
          this.db.run("ALTER TABLE message_templates ADD COLUMN location_info TEXT");
          console.log('✅ Added location_info column to message_templates table');
        } catch (error) {
          if (!error.message.includes('duplicate column name')) {
            throw error;
          }
          console.log('⚠️ location_info column already exists in message_templates table');
        }
      }

      if (!templateColumnNames.includes('media_settings')) {
        try {
          this.db.run("ALTER TABLE message_templates ADD COLUMN media_settings TEXT");
          console.log('✅ Added media_settings column to message_templates table');
        } catch (error) {
          if (!error.message.includes('duplicate column name')) {
            throw error;
          }
          console.log('⚠️ media_settings column already exists in message_templates table');
        }
      }

      if (!templateColumnNames.includes('interactive_settings')) {
        try {
          this.db.run("ALTER TABLE message_templates ADD COLUMN interactive_settings TEXT");
          console.log('✅ Added interactive_settings column to message_templates table');
        } catch (error) {
          if (!error.message.includes('duplicate column name')) {
            throw error;
          }
          console.log('⚠️ interactive_settings column already exists in message_templates table');
        }
      }

      if (!templateColumnNames.includes('mixed_buttons_data')) {
        try {
          this.db.run(`ALTER TABLE message_templates ADD COLUMN mixed_buttons_data TEXT`);
          console.log('✅ Added mixed_buttons_data column to message_templates table');
        } catch (error) {
          if (!error.message.includes('duplicate column name')) {
            throw error;
          }
          console.log('⚠️ mixed_buttons_data column already exists in message_templates table');
        }
      }

      // Check and add missing columns to contacts table
      const contactTableResult = await this.query("PRAGMA table_info(contacts)");
      const contactTableInfo = contactTableResult.success && Array.isArray(contactTableResult.data) ? contactTableResult.data : [];
      const contactColumnNames = contactTableInfo.map(col => col.name);

      // Add custom variables Var1-Var10
      for (let i = 1; i <= 10; i++) {
        const columnName = `var${i}`;
        if (!contactColumnNames.includes(columnName)) {
          try {
            this.db.run(`ALTER TABLE contacts ADD COLUMN ${columnName} TEXT`);
            console.log(`✅ Added ${columnName} column to contacts table`);
          } catch (error) {
            if (!error.message.includes('duplicate column name')) {
              throw error;
            }
            console.log(`⚠️ ${columnName} column already exists in contacts table`);
          }
        }
      }

      // Add WhatsApp verification columns
      if (!contactColumnNames.includes('whatsapp_verified')) {
        try {
          this.db.run("ALTER TABLE contacts ADD COLUMN whatsapp_verified BOOLEAN DEFAULT 0");
          console.log('✅ Added whatsapp_verified column to contacts table');
        } catch (error) {
          if (!error.message.includes('duplicate column name')) {
            throw error;
          }
          console.log('⚠️ whatsapp_verified column already exists in contacts table');
        }
      }

      if (!contactColumnNames.includes('verification_status')) {
        try {
          this.db.run("ALTER TABLE contacts ADD COLUMN verification_status TEXT DEFAULT 'pending'");
          console.log('✅ Added verification_status column to contacts table');
        } catch (error) {
          if (!error.message.includes('duplicate column name')) {
            throw error;
          }
          console.log('⚠️ verification_status column already exists in contacts table');
        }
      }

      if (!contactColumnNames.includes('verification_date')) {
        try {
          this.db.run("ALTER TABLE contacts ADD COLUMN verification_date DATETIME");
          console.log('✅ Added verification_date column to contacts table');
        } catch (error) {
          if (!error.message.includes('duplicate column name')) {
            throw error;
          }
          console.log('⚠️ verification_date column already exists in contacts table');
        }
      }

      // Add company and position columns if missing
      if (!contactColumnNames.includes('company')) {
        try {
          this.db.run("ALTER TABLE contacts ADD COLUMN company TEXT");
          console.log('✅ Added company column to contacts table');
        } catch (error) {
          if (!error.message.includes('duplicate column name')) {
            throw error;
          }
          console.log('⚠️ company column already exists in contacts table');
        }
      }

      if (!contactColumnNames.includes('position')) {
        try {
          this.db.run("ALTER TABLE contacts ADD COLUMN position TEXT");
          console.log('✅ Added position column to contacts table');
        } catch (error) {
          if (!error.message.includes('duplicate column name')) {
            throw error;
          }
          console.log('⚠️ position column already exists in contacts table');
        }
      }

      // Check and fix bulk_campaigns table schema
      // Simple approach: check if session_ids column exists, if not, recreate table
      let needsMigration = false;

      try {
        // Try to select from session_ids column (new schema)
        await this.query("SELECT session_ids FROM bulk_campaigns LIMIT 1");
        console.log('✅ bulk_campaigns table has correct schema');
      } catch (error) {
        console.log('🔄 bulk_campaigns table needs migration');
        needsMigration = true;
      }

      if (needsMigration) {
        console.log('🔄 Migrating bulk_campaigns table...');

        try {
          // First, drop the temporary table if it exists
          try {
            this.db.run("DROP TABLE IF EXISTS bulk_campaigns_new");
          } catch (e) {
            // Ignore error if table doesn't exist
          }

          // Create new table with correct schema (no session_id column)
          this.db.run(`
            CREATE TABLE bulk_campaigns_new (
              id INTEGER PRIMARY KEY AUTOINCREMENT,
              name TEXT NOT NULL,
              template_id INTEGER,
              session_ids TEXT NOT NULL DEFAULT '[]',
              message_content TEXT,
              message_type TEXT DEFAULT 'text',
              contact_group_ids TEXT,
              device_rotation BOOLEAN DEFAULT 1,
              status TEXT DEFAULT 'draft',
              total_contacts INTEGER DEFAULT 0,
              sent_count INTEGER DEFAULT 0,
              failed_count INTEGER DEFAULT 0,
              delivery_delay INTEGER DEFAULT 5,
              max_retries INTEGER DEFAULT 3,
              scheduled_at DATETIME,
              started_at DATETIME,
              completed_at DATETIME,
              created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
              updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
              FOREIGN KEY (template_id) REFERENCES message_templates(id)
            )
          `);

          // Copy data to new table (only using session_ids column)
          this.db.run(`
            INSERT INTO bulk_campaigns_new (
              id, name, template_id, session_ids, message_content, message_type,
              contact_group_ids, device_rotation, status, total_contacts, sent_count,
              failed_count, delivery_delay, max_retries, scheduled_at, started_at,
              completed_at, created_at, updated_at
            )
            SELECT id, name, template_id,
                   CASE
                     WHEN session_ids IS NOT NULL AND session_ids != '' THEN session_ids
                     WHEN session_id IS NOT NULL AND session_id != '' THEN '[' || '"' || session_id || '"' || ']'
                     ELSE '[]'
                   END as session_ids,
                   message_content, message_type, contact_group_ids, device_rotation,
                   status, total_contacts, sent_count, failed_count, delivery_delay,
                   max_retries, scheduled_at, started_at, completed_at, created_at, updated_at
            FROM bulk_campaigns
          `);

          // Drop old table and rename new one
          this.db.run("DROP TABLE bulk_campaigns");
          this.db.run("ALTER TABLE bulk_campaigns_new RENAME TO bulk_campaigns");

          console.log('✅ Successfully migrated bulk_campaigns table schema');
        } catch (error) {
          console.error('❌ Error migrating bulk_campaigns table:', error);
        }
      }

      // Clean up orphaned foreign key references
      try {
        console.log('🧹 Cleaning up orphaned foreign key references...');

        // Remove orphaned bulk_campaign_recipients
        this.db.run(`
          DELETE FROM bulk_campaign_recipients
          WHERE campaign_id NOT IN (SELECT id FROM bulk_campaigns)
        `);

        // Remove orphaned message_history records
        this.db.run(`
          DELETE FROM message_history
          WHERE campaign_id IS NOT NULL
          AND campaign_id NOT IN (SELECT id FROM bulk_campaigns)
        `);

        console.log('✅ Cleaned up orphaned foreign key references');
      } catch (error) {
        console.error('❌ Error cleaning up orphaned references:', error);
      }

      // Force recreate bulk_campaigns table if it's corrupted
      try {
        console.log('🔧 Testing bulk_campaigns table integrity...');

        // Check if the table has the correct foreign key constraint
        const tableInfo = this.db.prepare("SELECT sql FROM sqlite_master WHERE type='table' AND name='bulk_campaigns'");
        let tableSql = '';
        if (tableInfo.step()) {
          tableSql = tableInfo.getAsObject().sql;
        }
        tableInfo.free();

        console.log('🔧 Current table SQL:', tableSql);

        // Check if the foreign key constraint has "ON DELETE SET NULL"
        const hasCorrectFK = tableSql.includes('ON DELETE SET NULL');
        console.log('🔧 Has correct foreign key constraint:', hasCorrectFK);

        // Try a simple INSERT test
        const testResult = this.db.run(`
          INSERT INTO bulk_campaigns (
            name, session_ids, message_content, message_type,
            contact_group_ids, device_rotation, status, total_contacts
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        `, ['TEST_CAMPAIGN', '["test"]', 'test message', 'text', '[]', 1, 'draft', 0]);

        const changes = this.db.getRowsModified();
        console.log('🔧 Test INSERT changes:', changes);

        // Test a real campaign INSERT to see if it works
        console.log('🔧 Testing real campaign INSERT...');
        try {
          const realTestResult = this.db.run(`
            INSERT INTO bulk_campaigns (
              name, session_ids, message_content, message_type,
              contact_group_ids, device_rotation, status, total_contacts
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
          `, ['REAL_TEST_CAMPAIGN', '["test_session"]', 'Test message content', 'text', '[]', 1, 'draft', 0]);

          const realTestChanges = this.db.getRowsModified();
          console.log('🔧 Real test INSERT changes:', realTestChanges);

          if (realTestChanges > 0) {
            // Get the inserted ID
            const lastIdStmt = this.db.prepare("SELECT last_insert_rowid() as lastID");
            let testCampaignId = null;
            if (lastIdStmt.step()) {
              testCampaignId = lastIdStmt.getAsObject().lastID;
            }
            lastIdStmt.free();
            console.log('🔧 Real test campaign ID:', testCampaignId);

            // Clean up test record
            this.db.run("DELETE FROM bulk_campaigns WHERE name = 'REAL_TEST_CAMPAIGN'");
            console.log('🔧 Cleaned up test campaign');
          }
        } catch (realTestError) {
          console.error('🔧 Real test INSERT error:', realTestError);
        }

        if (changes === 0 || !hasCorrectFK) {
          console.log('❌ Table appears corrupted or has foreign key issues, recreating...');

          // Backup existing data
          const existingData = await this.query('SELECT * FROM bulk_campaigns');

          // Drop and recreate table
          this.db.run('DROP TABLE IF EXISTS bulk_campaigns');
          this.db.run(`
            CREATE TABLE bulk_campaigns (
              id INTEGER PRIMARY KEY AUTOINCREMENT,
              name TEXT NOT NULL,
              template_id INTEGER,
              session_ids TEXT NOT NULL DEFAULT '[]',
              message_content TEXT,
              message_type TEXT DEFAULT 'text',
              contact_group_ids TEXT,
              device_rotation BOOLEAN DEFAULT 1,
              status TEXT DEFAULT 'draft',
              total_contacts INTEGER DEFAULT 0,
              sent_count INTEGER DEFAULT 0,
              failed_count INTEGER DEFAULT 0,
              delivery_delay INTEGER DEFAULT 5,
              max_retries INTEGER DEFAULT 3,
              scheduled_at DATETIME,
              started_at DATETIME,
              completed_at DATETIME,
              created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
              updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
              FOREIGN KEY (template_id) REFERENCES message_templates(id) ON DELETE SET NULL
            )
          `);

          // Restore data if any
          if (existingData.success && existingData.data.length > 0) {
            console.log(`🔄 Restoring ${existingData.data.length} campaigns...`);
            for (const row of existingData.data) {
              this.db.run(`
                INSERT INTO bulk_campaigns (
                  id, name, template_id, session_ids, message_content, message_type,
                  contact_group_ids, device_rotation, status, total_contacts, sent_count,
                  failed_count, delivery_delay, max_retries, scheduled_at, started_at,
                  completed_at, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
              `, [
                row.id, row.name, row.template_id, row.session_ids, row.message_content,
                row.message_type, row.contact_group_ids, row.device_rotation,
                row.status, row.total_contacts, row.sent_count, row.failed_count,
                row.delivery_delay, row.max_retries, row.scheduled_at, row.started_at,
                row.completed_at, row.created_at, row.updated_at
              ]);
            }
          }

          console.log('✅ Successfully recreated bulk_campaigns table');
        } else {
          // Clean up test record
          this.db.run('DELETE FROM bulk_campaigns WHERE name = ?', ['TEST_CAMPAIGN']);
          console.log('✅ Table integrity test passed');
        }
      } catch (error) {
        console.error('❌ Error testing/recreating bulk_campaigns table:', error);
      }

      // Add attachment_data column to bulk_campaigns if it doesn't exist
      try {
        const stmt = this.db.prepare("PRAGMA table_info(bulk_campaigns)");
        const tableInfo = [];
        while (stmt.step()) {
          tableInfo.push(stmt.getAsObject());
        }
        stmt.free();
        const hasAttachmentData = tableInfo.some(column => column.name === 'attachment_data');

        if (!hasAttachmentData) {
          console.log('🔄 Adding attachment_data column to bulk_campaigns table...');
          this.db.run('ALTER TABLE bulk_campaigns ADD COLUMN attachment_data TEXT');
          console.log('✅ Successfully added attachment_data column');
        }
      } catch (error) {
        console.error('❌ Error adding attachment_data column:', error);
      }

    } catch (error) {
      console.error('Error adding missing columns:', error);
    }
  }

  /**
   * Run Call Responder migrations to update schema
   */
  async runCallResponderMigrations() {
    try {
      console.log('🔄 Running Call Responder migrations...');

      // Check if call_responses table exists and has old schema
      const stmt = this.db.prepare("PRAGMA table_info(call_responses)");
      const tableInfo = [];
      while (stmt.step()) {
        tableInfo.push(stmt.getAsObject());
      }
      stmt.free();

      const hasOldSchema = tableInfo.some(col => col.name === 'trigger_type' || col.name === 'response_delay');

      if (hasOldSchema) {
        console.log('📋 Migrating call_responses table to new schema...');

        // Backup existing data
        const dataStmt = this.db.prepare("SELECT * FROM call_responses");
        const existingData = [];
        while (dataStmt.step()) {
          existingData.push(dataStmt.getAsObject());
        }
        dataStmt.free();

        // Drop old table
        this.db.run("DROP TABLE IF EXISTS call_responses");

        // Create new table with updated schema
        this.db.run(`CREATE TABLE call_responses (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          session_id TEXT NOT NULL,
          name TEXT NOT NULL,
          call_types TEXT NOT NULL,
          message_type TEXT DEFAULT 'text',
          message_content TEXT,
          template_id INTEGER,
          attachment_file TEXT,
          attachment_type TEXT,
          delay_minutes INTEGER DEFAULT 1,
          is_active BOOLEAN DEFAULT 1,
          usage_count INTEGER DEFAULT 0,
          last_used DATETIME,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (template_id) REFERENCES message_templates(id)
        )`);

        // Migrate existing data to new schema
        for (const row of existingData) {
          const callTypes = row.trigger_type ? [row.trigger_type] : ['missed', 'rejected'];
          const delayMinutes = row.response_delay ? Math.ceil(row.response_delay / 60) : 1;

          this.db.run(`INSERT INTO call_responses (
            id, session_id, name, call_types, message_type, message_content,
            template_id, delay_minutes, is_active, usage_count, created_at, updated_at
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`, [
            row.id,
            row.session_id,
            row.name,
            JSON.stringify(callTypes),
            row.response_type || 'text',
            row.response_content,
            row.template_id,
            delayMinutes,
            row.is_active,
            row.usage_count || 0,
            row.created_at,
            row.updated_at
          ]);
        }

        console.log('✅ Call Responder migration completed successfully');
      } else {
        console.log('✅ Call Responder table already has correct schema');
      }
    } catch (error) {
      console.error('❌ Error running Call Responder migrations:', error);
    }
  }

  async addChatbotAttachmentColumns() {
    try {
      // Check if chatbot_nodes table exists
      const tableExistsResult = this.db.exec(`
        SELECT name FROM sqlite_master
        WHERE type='table' AND name='chatbot_nodes'
      `);

      if (!tableExistsResult || tableExistsResult.length === 0 || tableExistsResult[0].values.length === 0) {
        console.log('⚠️ chatbot_nodes table does not exist, skipping attachment column additions');
        return;
      }

      // Get current table schema
      const columnsResult = this.db.exec("PRAGMA table_info(chatbot_nodes)");
      const columnNames = columnsResult && columnsResult.length > 0 && columnsResult[0].values
        ? columnsResult[0].values.map(row => row[1]) // column name is at index 1
        : [];

      // Add missing attachment columns
      if (!columnNames.includes('attachment_data')) {
        this.db.run("ALTER TABLE chatbot_nodes ADD COLUMN attachment_data TEXT");
        console.log('✅ Added attachment_data column to chatbot_nodes table');
      } else {
        console.log('⚠️ attachment_data column already exists in chatbot_nodes table');
      }

      if (!columnNames.includes('attachment_type')) {
        this.db.run("ALTER TABLE chatbot_nodes ADD COLUMN attachment_type TEXT");
        console.log('✅ Added attachment_type column to chatbot_nodes table');
      } else {
        console.log('⚠️ attachment_type column already exists in chatbot_nodes table');
      }

    } catch (error) {
      console.error('❌ Error adding chatbot attachment columns:', error);
      throw error;
    }
  }

  // Data Maintenance Methods
  async getDataMaintenanceStats(dataTypes, cutoffDate) {
    try {
      const stats = {};

      for (const dataType of dataTypes) {
        let query;
        let params;

        if (dataType.customCondition) {
          query = `SELECT COUNT(*) as count FROM ${dataType.table} WHERE ${dataType.customCondition}`;
          params = [cutoffDate];
        } else {
          query = `SELECT COUNT(*) as count FROM ${dataType.table} WHERE ${dataType.dateColumn} < ?`;
          params = [cutoffDate];
        }

        const result = await this.query(query, params);
        stats[dataType.id] = result.success && result.data && result.data.length > 0 ? result.data[0].count : 0;
      }

      return { success: true, data: stats };
    } catch (error) {
      console.error('❌ Error getting data maintenance stats:', error);
      return { success: false, error: error.message, data: {} };
    }
  }

  async deleteOldData(dataType, cutoffDate) {
    try {
      let query;
      let params;

      if (dataType.customCondition) {
        query = `DELETE FROM ${dataType.table} WHERE ${dataType.customCondition}`;
        params = [cutoffDate];
      } else {
        query = `DELETE FROM ${dataType.table} WHERE ${dataType.dateColumn} < ?`;
        params = [cutoffDate];
      }

      const result = await this.run(query, params);

      if (result.success) {
        return { success: true, deletedCount: result.changes || 0 };
      } else {
        return { success: false, error: result.error };
      }
    } catch (error) {
      console.error(`❌ Error deleting old data from ${dataType.table}:`, error);
      return { success: false, error: error.message };
    }
  }

  async getTableSizes() {
    try {
      const tables = [
        'activity_logs',
        'auto_reply_cooldowns',
        'chatbot_conversations',
        'contacts',
        'message_templates',
        'whatsapp_sessions',
        'bulk_campaigns',
        'message_history'
      ];

      const sizes = {};

      for (const table of tables) {
        try {
          const result = await this.query(`SELECT COUNT(*) as count FROM ${table}`);
          sizes[table] = result.success && result.data && result.data.length > 0 ? result.data[0].count : 0;
        } catch (error) {
          console.error(`Error getting size for table ${table}:`, error);
          sizes[table] = 0;
        }
      }

      return { success: true, data: sizes };
    } catch (error) {
      console.error('❌ Error getting table sizes:', error);
      return { success: false, error: error.message, data: {} };
    }
  }

  /**
   * Clear user data for fresh installations
   */
  async clearUserData() {
    try {
      // Get list of existing tables first
      const tablesResult = await this.query("SELECT name FROM sqlite_master WHERE type='table'");
      const existingTables = tablesResult.data.map(row => row.name);

      // Clear user-generated data but keep system tables
      const tablesToClear = [
        'contacts',
        'message_templates',
        'whatsapp_sessions',
        'auto_reply_rules',
        'chatbot_flows',
        'chatbot_nodes',
        'bulk_campaigns',
        'bulk_campaign_recipients',
        'message_history',
        'auto_reply_cooldowns',
        'call_responses'
      ];

      for (const tableName of tablesToClear) {
        if (existingTables.includes(tableName)) {
          try {
            await this.run(`DELETE FROM ${tableName} WHERE 1=1`);
            console.log(`✅ Cleared table: ${tableName}`);
          } catch (error) {
            console.log(`⚠️ Could not clear table ${tableName}: ${error.message}`);
          }
        } else {
          console.log(`ℹ️ Table ${tableName} does not exist, skipping`);
        }
      }

      return { success: true };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  /**
   * Clear WhatsApp auth sessions for fresh installations
   */
  async clearAuthSessions() {
    try {
      const os = require('os');
      const path = require('path');
      const fs = require('fs').promises;

      const authDir = path.join(os.homedir(), 'Lead Wave', 'auth');

      try {
        // Remove entire auth directory
        await fs.rm(authDir, { recursive: true, force: true });
      } catch (error) {
        // Ignore if directory doesn't exist
      }

      return { success: true };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  async optimizeDatabase() {
    try {
      console.log('🔧 Starting database optimization...');

      // Run VACUUM to reclaim space and optimize
      await this.run('VACUUM');
      console.log('✅ Database VACUUM completed');

      // Analyze tables for better query planning
      await this.run('ANALYZE');
      console.log('✅ Database ANALYZE completed');

      return { success: true, message: 'Database optimization completed successfully' };
    } catch (error) {
      console.error('❌ Error optimizing database:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Handle template duplicates migration
   * Remove duplicate templates and ensure unique names
   */
  async handleTemplateDuplicatesMigration() {
    try {
      console.log('🔄 Checking for duplicate templates...');

      // Find duplicate template names
      const duplicates = await this.query(`
        SELECT name, COUNT(*) as count
        FROM message_templates
        GROUP BY name
        HAVING COUNT(*) > 1
      `);

      if (duplicates.success && duplicates.data && duplicates.data.length > 0) {
        console.log(`Found ${duplicates.data.length} duplicate template names, fixing...`);

        for (const duplicate of duplicates.data) {
          // Get all templates with this name, ordered by creation date
          const templatesWithSameName = await this.query(`
            SELECT * FROM message_templates
            WHERE name = ?
            ORDER BY created_at ASC
          `, [duplicate.name]);

          if (templatesWithSameName.success && templatesWithSameName.data.length > 1) {
            // Keep the first one, rename the others
            const templates = templatesWithSameName.data;

            for (let i = 1; i < templates.length; i++) {
              const template = templates[i];
              const newName = `${duplicate.name} (${i})`;

              // Update the duplicate template with a new name
              await this.query(`
                UPDATE message_templates
                SET name = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
              `, [newName, template.id]);

              console.log(`Renamed duplicate template "${duplicate.name}" to "${newName}"`);
            }
          }
        }

        console.log('✅ Template duplicates fixed');
      } else {
        console.log('✅ No duplicate templates found');
      }
    } catch (error) {
      console.error('❌ Error handling template duplicates migration:', error);
    }
  }

  /**
   * Clean up template duplicates immediately
   * This is a more aggressive cleanup that deletes duplicates
   */
  async cleanupTemplateDuplicates() {
    try {
      console.log('🧹 Cleaning up template duplicates...');

      // Find and delete duplicates, keeping only the one with the smallest ID (oldest)
      const deleteResult = await this.query(`
        DELETE FROM message_templates
        WHERE id NOT IN (
          SELECT MIN(id)
          FROM message_templates
          GROUP BY name
        )
      `);

      if (deleteResult.success) {
        console.log(`✅ Cleaned up template duplicates`);
      } else {
        console.log('⚠️ No duplicates found to clean up');
      }
    } catch (error) {
      console.error('❌ Error cleaning template duplicates:', error);
    }
  }

  /**
   * Apply database migrations to fix contact deletion issues
   */
  async applyMigrations() {
    try {
      console.log('🔧 Applying database migrations...');

      // Check if contact_group_members table needs migration
      const tableInfoResult = await this.query('PRAGMA table_info(contact_group_members)');
      const tableSqlResult = await this.query("SELECT sql FROM sqlite_master WHERE type='table' AND name='contact_group_members'");

      if (tableSqlResult.success && tableSqlResult.data.length > 0 && tableSqlResult.data[0].sql.includes('ON DELETE CASCADE')) {
        console.log('🔧 Migrating contact_group_members table to fix CASCADE constraints...');

        // Create new table with RESTRICT constraints
        this.db.run(`
          CREATE TABLE contact_group_members_new (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            group_id INTEGER NOT NULL,
            contact_id INTEGER NOT NULL,
            added_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (group_id) REFERENCES contact_groups(id) ON DELETE RESTRICT,
            FOREIGN KEY (contact_id) REFERENCES contacts(id) ON DELETE RESTRICT,
            UNIQUE(group_id, contact_id)
          )
        `);

        // Copy data from old table
        this.db.run(`
          INSERT INTO contact_group_members_new (id, group_id, contact_id, added_at)
          SELECT id, group_id, contact_id, added_at FROM contact_group_members
        `);

        // Drop old table and rename new one
        this.db.run('DROP TABLE contact_group_members');
        this.db.run('ALTER TABLE contact_group_members_new RENAME TO contact_group_members');

        // Recreate indexes
        this.db.run('CREATE INDEX IF NOT EXISTS idx_contact_group_members_group ON contact_group_members(group_id)');
        this.db.run('CREATE INDEX IF NOT EXISTS idx_contact_group_members_contact ON contact_group_members(contact_id)');

        console.log('✅ Successfully migrated contact_group_members table');
      }

      console.log('✅ Database migrations completed');
    } catch (error) {
      console.error('❌ Error applying migrations:', error);
    }
  }
}

module.exports = DatabaseService;