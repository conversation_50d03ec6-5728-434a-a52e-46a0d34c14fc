import React, { useState, useEffect, useCallback } from 'react';
import {
  ChatBubbleOvalLeftEllipsisIcon,
  PlusIcon,
  MagnifyingGlassIcon,
  PencilIcon,
  TrashIcon,
  PlayIcon,
  PauseIcon,
  EyeIcon,
  DocumentTextIcon,
  ArrowPathIcon,
  TagIcon,
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon,
  ExclamationTriangleIcon,
  XMarkIcon,
  ArrowRightIcon,
  BoltIcon,
  PhotoIcon,
  PaperClipIcon,
  UserGroupIcon,
  ChartBarIcon,
  RectangleStackIcon,
  ListBulletIcon,
  ViewColumnsIcon,
  MapPinIcon,
  VideoCameraIcon,
  SpeakerWaveIcon,
  DocumentIcon,
  ChatBubbleLeftRightIcon,
  ArrowDownTrayIcon,
  ArrowUpTrayIcon
} from '@heroicons/react/24/outline';
import { useNotifications } from '../../contexts/NotificationContext';

// Template types with their configurations (matching Templates.js)
const TEMPLATE_TYPES = {
  text: {
    name: 'Text Message',
    description: 'Simple text message with variables',
    color: 'blue'
  },
  image: {
    name: 'Message + Image',
    description: 'Text message with image attachment',
    color: 'green'
  },
  document: {
    name: 'Message + Document',
    description: 'Text message with document attachment',
    color: 'purple'
  },
  contact: {
    name: 'Message + Contact',
    description: 'Text message with contact card',
    color: 'indigo'
  },
  poll: {
    name: 'Message + Poll',
    description: 'Interactive poll with multiple options',
    color: 'yellow'
  },
  buttons: {
    name: 'Interactive Buttons',
    description: 'Message with interactive buttons',
    color: 'blue'
  },
  // Hidden template type - functionality preserved but not shown in UI
  // cta_button: {
  //   name: 'CTA Button',
  //   description: 'Call-to-action button with URL link',
  //   color: 'orange'
  // },
  list: {
    name: 'Interactive List',
    description: 'Message with selectable list options',
    color: 'green'
  },

  mixed_buttons: {
    name: 'Mixed Interactive Buttons',
    description: 'Interactive buttons with multiple types (Quick Reply, CTA URL, CTA Phone, Copy Code)',
    color: 'indigo'
  },
  location: {
    name: 'Message + Location',
    description: 'Text message with location pin',
    color: 'red'
  },
  video: {
    name: 'Message + Video',
    description: 'Text message with video attachment',
    color: 'pink'
  },
  audio: {
    name: 'Message + Audio',
    description: 'Text message with audio attachment',
    color: 'orange'
  }
};

const Chatbot = () => {
  const { showSuccess, showError, showWarning, confirm } = useNotifications();
  // Add error boundary to catch rendering errors
  const [renderError, setRenderError] = useState(null);

  // Debug function to safely render any value
  const safeRender = (value, context = 'unknown') => {
    if (value === null || value === undefined) {
      return '';
    }
    if (typeof value === 'string' || typeof value === 'number' || typeof value === 'boolean') {
      return value;
    }
    if (typeof value === 'object') {
      console.error(`🚨 OBJECT RENDER ATTEMPT in ${context}:`, value);
      console.trace('Stack trace for object render attempt');
      if (value.name) return String(value.name);
      if (value.display_text) return String(value.display_text);
      if (value.title) return String(value.title);
      if (value.text) return String(value.text);
      return JSON.stringify(value);
    }
    return String(value);
  };

  const [flows, setFlows] = useState([]);
  const [sessions, setSessions] = useState([]);
  const [templates, setTemplates] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showFlowModal, setShowFlowModal] = useState(false);
  const [selectedFlow, setSelectedFlow] = useState(null);
  const [flowForm, setFlowForm] = useState({
    name: '',
    description: '',
    sessionId: '',
    triggerKeywords: '',
    keywordMatchType: 'contains',
    keywordCaseSensitive: false,
    isActive: true,
    cooldownMinutes: 0,
    nodes: []
  });
  const [nodeForm, setNodeForm] = useState({
    name: '',
    message: '',
    nodeType: 'message',
    messageType: 'text', // text, template
    options: [],
    nextNodeId: null,
    templateId: '',
    variables: {},
    attachmentFile: null,
    attachmentType: 'image', // image, video, audio, document
    extractVariable: '', // Variable name to extract from user response
    conditions: [], // Conditional logic for condition nodes
    // Action node fields
    actionType: 'webhook',
    webhookUrl: '',
    delaySeconds: 5,
    emailRecipients: '',
    emailSubject: '',
    emailBody: '', // Added email body
    emailTemplate: '', // Added email template
    saveDataFields: [], // Added save data fields
    apiEndpoint: '', // Added API endpoint
    apiMethod: 'POST', // Added API method
    apiHeaders: '', // Added API headers
    apiBody: '', // Added API body
    // Condition node fields
    conditionType: 'user_response',
    responseConditions: [],
    conditionVariable: '',
    conditionOperator: 'equals',
    conditionValue: '',
    randomPaths: [],
    truePath: null, // Added true path for conditions
    falsePath: null // Added false path for conditions
  });


  const [showNodeModal, setShowNodeModal] = useState(false);
  const [editingNodeIndex, setEditingNodeIndex] = useState(-1);

  // Load data on component mount
  useEffect(() => {
    loadFlows();
    loadSessions();
    loadTemplates();
  }, []);



  // Ensure options is always an array of strings when nodeType changes
  useEffect(() => {
    if (nodeForm.nodeType === 'question') {
      if (!Array.isArray(nodeForm.options)) {
        setNodeForm(prev => ({ ...prev, options: [] }));
      }
    }
  }, [nodeForm.nodeType]);

  const loadFlows = async () => {
    try {
      setLoading(true);
      const response = await window.electronAPI.database.query(
        `SELECT cf.*, ws.device_name, COUNT(cn.id) as node_count
         FROM chatbot_flows cf
         LEFT JOIN whatsapp_sessions ws ON cf.session_id = ws.session_id
         LEFT JOIN chatbot_nodes cn ON cf.id = cn.flow_id
         GROUP BY cf.id
         ORDER BY cf.created_at DESC`
      );
      if (response.success) {
        setFlows(response.data || []);
      }
    } catch (error) {
      console.error('Error loading chatbot flows:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadSessions = async () => {
    try {
      const response = await window.electronAPI.whatsapp.getSessions();
      console.log('Sessions response:', response);

      if (response.success && Array.isArray(response.sessions)) {
        // Filter for connected sessions - be more permissive to catch any working sessions
        const connectedSessions = response.sessions.filter(session => {
          const isConnected = session.realTimeStatus === 'connected' ||
                             session.status === 'connected' ||
                             session.isLoggedIn === true;

          console.log(`Session ${typeof session.sessionId === 'string' ? session.sessionId : (typeof session.name === 'string' ? session.name : String(session.sessionId || session.name || 'unknown'))}:`, {
            status: session.status,
            realTimeStatus: session.realTimeStatus,
            isLoggedIn: session.isLoggedIn,
            isConnected
          });

          return isConnected;
        });

        console.log('All sessions:', response.sessions.length);
        console.log('Connected sessions:', connectedSessions.length);
        setSessions(connectedSessions);
      } else {
        console.warn('Invalid sessions response:', response);
        setSessions([]);
      }
    } catch (error) {
      console.error('Error loading sessions:', error);
      setSessions([]);
    }
  };

  const loadTemplates = async () => {
    try {
      const response = await window.electronAPI.database.query(
        `SELECT id, name, content, type, attachments, buttons, list_sections,
         poll_options, contact_info, location_info,
         media_settings, interactive_settings, cta_data, copy_data, flow_data, variables
         FROM message_templates ORDER BY name ASC`
      );
      if (response.success) {
        setTemplates(response.data || []);
      }
    } catch (error) {
      console.error('Error loading templates:', error);
    }
  };

  const handleCreateFlow = async () => {
    if (!flowForm.name.trim() || !flowForm.triggerKeywords.trim() || !flowForm.sessionId) {
      showError('Validation Error', 'Please fill in flow name, trigger keywords, and select a WhatsApp session');
      return;
    }

    if (flowForm.nodes.length === 0) {
      showError('Validation Error', 'Please add at least one node to the flow');
      return;
    }

    try {
      console.log('🔧 Creating chatbot flow with data:', {
        name: flowForm.name.trim(),
        description: flowForm.description.trim() || null,
        sessionId: flowForm.sessionId,
        triggerKeywords: flowForm.triggerKeywords.trim(),
        isActive: flowForm.isActive ? 1 : 0,
        cooldownMinutes: flowForm.cooldownMinutes || 0,
        nodeCount: flowForm.nodes.length
      });

      // Create the flow
      const flowResult = await window.electronAPI.database.query(
        `INSERT INTO chatbot_flows (
          name, description, session_id, trigger_keywords, keyword_match_type, keyword_case_sensitive,
          is_active, cooldown_minutes, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)`,
        [
          flowForm.name.trim(),
          flowForm.description.trim() || null,
          flowForm.sessionId,
          flowForm.triggerKeywords.trim(),
          flowForm.keywordMatchType || 'contains',
          flowForm.keywordCaseSensitive ? 1 : 0,
          flowForm.isActive ? 1 : 0,
          flowForm.cooldownMinutes || 0
        ]
      );

      console.log('🔧 Flow creation result:', flowResult);

      if (flowResult.success) {
        const flowId = flowResult.insertId;

        // Create nodes with proper ID mapping
        const nodeIdMap = new Map(); // Map from array index to actual database ID

        // First pass: Create all nodes without next_node_id
        for (let i = 0; i < flowForm.nodes.length; i++) {
          const node = flowForm.nodes[i];
          const nodeResult = await window.electronAPI.database.query(
            `INSERT INTO chatbot_nodes (
              flow_id, name, message, node_type, options, next_node_id,
              position, template_id, attachment_data, attachment_type, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)`,
            [
              flowId,
              node.name,
              node.message,
              node.nodeType,
              JSON.stringify(node.options || []),
              null, // Set to null initially
              i + 1,
              node.templateId || null,
              node.attachmentData || null,
              node.attachmentType || null
            ]
          );

          if (nodeResult.success) {
            nodeIdMap.set(i + 1, nodeResult.insertId); // Map position to actual DB ID
          }
        }

        // Second pass: Update next_node_id with correct database IDs
        for (let i = 0; i < flowForm.nodes.length; i++) {
          const node = flowForm.nodes[i];
          if (node.nextNodeId && !isNaN(node.nextNodeId) && node.nextNodeId <= flowForm.nodes.length) {
            const actualNextNodeId = nodeIdMap.get(parseInt(node.nextNodeId));
            if (actualNextNodeId) {
              const currentNodeId = nodeIdMap.get(i + 1);
              await window.electronAPI.database.query(
                'UPDATE chatbot_nodes SET next_node_id = ? WHERE id = ?',
                [actualNextNodeId, currentNodeId]
              );
            }
          }
        }

        setShowCreateModal(false);
        resetFlowForm();
        await loadFlows();
        showSuccess('Flow Created', 'Chatbot flow created successfully!');
      } else {
        showError('Creation Failed', 'Failed to create chatbot flow: ' + flowResult.error);
      }
    } catch (error) {
      console.error('Error creating chatbot flow:', error);
      showError('Creation Failed', 'Error creating chatbot flow. Please try again.');
    }
  };

  const handleDeleteFlow = async (flowId) => {
    const confirmed = await confirm('Are you sure you want to delete this chatbot flow and all its nodes?', 'Delete Chatbot Flow');
    if (!confirmed) {
      return;
    }

    try {
      console.log(`🗑️ Deleting chatbot flow with ID: ${flowId}`);

      // 1. End all active conversations for this flow
      const endConversationsResult = await window.electronAPI.database.query(
        'UPDATE chatbot_conversations SET is_active = 0, completed_at = CURRENT_TIMESTAMP WHERE flow_id = ? AND is_active = 1',
        [flowId]
      );
      console.log(`🗑️ Ended ${endConversationsResult.changes || 0} active conversations for flow ${flowId}`);

      // 2. Clear any cooldown records for this flow
      await window.electronAPI.database.query(
        'DELETE FROM chatbot_cooldowns WHERE flow_id = ?',
        [flowId]
      );

      // 3. Delete nodes first
      const deleteNodesResult = await window.electronAPI.database.query(
        'DELETE FROM chatbot_nodes WHERE flow_id = ?',
        [flowId]
      );
      console.log(`🗑️ Deleted ${deleteNodesResult.changes || 0} nodes for flow ${flowId}`);

      // 4. Delete flow
      const result = await window.electronAPI.database.query(
        'DELETE FROM chatbot_flows WHERE id = ?',
        [flowId]
      );

      if (result.success) {
        console.log(`✅ Successfully deleted chatbot flow ${flowId} and cleaned up related data`);
        await loadFlows();
        showSuccess('Flow Deleted', 'Chatbot flow deleted successfully and all active conversations ended');
      } else {
        showError('Delete Failed', 'Failed to delete chatbot flow: ' + result.error);
      }
    } catch (error) {
      console.error('Error deleting chatbot flow:', error);
      showError('Delete Failed', 'Error deleting chatbot flow. Please try again.');
    }
  };

  const toggleFlowStatus = async (flowId, currentStatus) => {
    try {
      // If disabling the flow, end all active conversations first
      if (currentStatus) {
        console.log(`🔄 Disabling flow ${flowId}, ending active conversations...`);
        const endConversationsResult = await window.electronAPI.database.query(
          'UPDATE chatbot_conversations SET is_active = 0, completed_at = CURRENT_TIMESTAMP WHERE flow_id = ? AND is_active = 1',
          [flowId]
        );
        console.log(`🔄 Ended ${endConversationsResult.changes || 0} active conversations for flow ${flowId}`);
      }

      const result = await window.electronAPI.database.query(
        'UPDATE chatbot_flows SET is_active = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        [currentStatus ? 0 : 1, flowId]
      );

      if (result.success) {
        await loadFlows();
        const statusText = currentStatus ? 'disabled' : 'enabled';
        showSuccess('Status Updated', `Flow ${statusText} successfully${currentStatus ? ' and active conversations ended' : ''}`);
      } else {
        showError('Update Failed', 'Failed to update flow status: ' + result.error);
      }
    } catch (error) {
      console.error('Error updating flow status:', error);
      showError('Update Failed', 'Error updating flow status. Please try again.');
    }
  };

  const handleEditFlow = async (flow) => {
    try {
      // Load the flow nodes
      const nodesResult = await window.electronAPI.database.query(
        'SELECT * FROM chatbot_nodes WHERE flow_id = ? ORDER BY position',
        [flow.id]
      );

      if (nodesResult.success) {
        // Create a map of database ID to position for proper conversion
        const idToPositionMap = new Map();
        nodesResult.data.forEach(node => {
          idToPositionMap.set(node.id, node.position);
        });

        const nodes = nodesResult.data.map((node, index) => {
          // Convert next_node_id from database ID to position-based reference
          let nextNodeId = null;
          if (node.next_node_id) {
            // Find the position of the referenced node by its database ID
            const referencedPosition = idToPositionMap.get(node.next_node_id);
            if (referencedPosition) {
              nextNodeId = referencedPosition;
            }
          }

          return {
            id: node.id,
            name: node.name,
            message: node.message,
            nodeType: node.node_type,
            options: node.options ? JSON.parse(node.options) : [],
            nextNodeId: nextNodeId,
            templateId: node.template_id,
            attachmentData: node.attachment_data,
            attachmentType: node.attachment_type,
            extractVariable: node.options && typeof JSON.parse(node.options) === 'object' && !Array.isArray(JSON.parse(node.options))
              ? JSON.parse(node.options).extract_variable || ''
              : '',
            conditions: node.node_type === 'condition' && node.options ? JSON.parse(node.options) : []
          };
        });

        // Populate the form with existing flow data
        setFlowForm({
          id: flow.id, // Add ID for editing
          name: flow.name,
          description: flow.description || '',
          sessionId: flow.session_id,
          triggerKeywords: flow.trigger_keywords,
          keywordMatchType: flow.keyword_match_type || 'contains',
          keywordCaseSensitive: flow.keyword_case_sensitive || false,
          isActive: flow.is_active,
          cooldownMinutes: flow.cooldown_minutes || 0,
          nodes: nodes
        });

        setShowEditModal(true);
      } else {
        showError('Load Failed', 'Failed to load flow nodes: ' + nodesResult.error);
      }
    } catch (error) {
      console.error('Error loading flow for editing:', error);
      showError('Load Failed', 'Error loading flow for editing. Please try again.');
    }
  };

  const handleUpdateFlow = async () => {
    if (!flowForm.name.trim() || !flowForm.triggerKeywords.trim() || !flowForm.sessionId) {
      showError('Validation Error', 'Please fill in flow name, trigger keywords, and select a WhatsApp session');
      return;
    }

    if (flowForm.nodes.length === 0) {
      showError('Validation Error', 'Please add at least one node to the flow');
      return;
    }

    try {
      // Update the flow
      const flowResult = await window.electronAPI.database.query(
        `UPDATE chatbot_flows SET
          name = ?, description = ?, session_id = ?, trigger_keywords = ?,
          keyword_match_type = ?, keyword_case_sensitive = ?,
          is_active = ?, cooldown_minutes = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?`,
        [
          flowForm.name.trim(),
          flowForm.description.trim() || null,
          flowForm.sessionId,
          flowForm.triggerKeywords.trim(),
          flowForm.keywordMatchType || 'contains',
          flowForm.keywordCaseSensitive ? 1 : 0,
          flowForm.isActive ? 1 : 0,
          flowForm.cooldownMinutes || 0,
          flowForm.id
        ]
      );

      if (flowResult.success) {
        // End any active conversations for this flow before updating nodes
        await window.electronAPI.database.query(
          'UPDATE chatbot_conversations SET is_active = 0, completed_at = CURRENT_TIMESTAMP WHERE flow_id = ? AND is_active = 1',
          [flowForm.id]
        );

        // Delete existing nodes
        await window.electronAPI.database.query(
          'DELETE FROM chatbot_nodes WHERE flow_id = ?',
          [flowForm.id]
        );

        // Create updated nodes with proper ID mapping
        const nodeIdMap = new Map(); // Map from array index to actual database ID

        // First pass: Create all nodes without next_node_id
        for (let i = 0; i < flowForm.nodes.length; i++) {
          const node = flowForm.nodes[i];
          const nodeResult = await window.electronAPI.database.query(
            `INSERT INTO chatbot_nodes (
              flow_id, name, message, node_type, options, next_node_id,
              position, template_id, attachment_data, attachment_type, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)`,
            [
              flowForm.id,
              node.name,
              node.message,
              node.nodeType,
              JSON.stringify(node.options || []),
              null, // Set to null initially
              i + 1,
              node.templateId || null,
              node.attachmentData || null,
              node.attachmentType || null
            ]
          );

          if (nodeResult.success) {
            nodeIdMap.set(i + 1, nodeResult.insertId); // Map position to actual DB ID
          }
        }

        // Second pass: Update next_node_id with correct database IDs
        for (let i = 0; i < flowForm.nodes.length; i++) {
          const node = flowForm.nodes[i];
          if (node.nextNodeId && !isNaN(node.nextNodeId) && node.nextNodeId <= flowForm.nodes.length) {
            const actualNextNodeId = nodeIdMap.get(parseInt(node.nextNodeId));
            if (actualNextNodeId) {
              const currentNodeId = nodeIdMap.get(i + 1);
              await window.electronAPI.database.query(
                'UPDATE chatbot_nodes SET next_node_id = ? WHERE id = ?',
                [actualNextNodeId, currentNodeId]
              );
            }
          }
        }

        showSuccess('Flow Updated', 'Chatbot flow updated successfully!');
        setShowEditModal(false);
        resetFlowForm();
        await loadFlows();
      } else {
        showError('Update Failed', 'Failed to update chatbot flow: ' + flowResult.error);
      }
    } catch (error) {
      console.error('Error updating chatbot flow:', error);
      showError('Update Failed', 'Error updating chatbot flow. Please try again.');
    }
  };

  const handleAddNode = async () => {
    if (!nodeForm.name.trim() || !nodeForm.message.trim()) {
      showError('Validation Error', 'Please fill in node name and message');
      return;
    }

    let attachmentData = null;
    let attachmentType = null;

    // Process attachment if present
    if (nodeForm.attachmentFile) {
      try {
        // Convert file to base64 for storage
        const reader = new FileReader();
        const fileData = await new Promise((resolve, reject) => {
          reader.onload = () => resolve(reader.result);
          reader.onerror = reject;
          reader.readAsDataURL(nodeForm.attachmentFile);
        });

        attachmentData = fileData;
        attachmentType = nodeForm.attachmentType;
      } catch (error) {
        showError('Attachment Error', 'Error processing attachment: ' + error.message);
        return;
      }
    }

    // Prepare options based on node type
    let nodeOptions = {};
    if (nodeForm.nodeType === 'question') {
      // For question nodes, convert string options to Baileys interactive format
      const filteredOptions = nodeForm.options.filter(opt => opt.trim());
      const interactiveOptions = filteredOptions.map((option, index) => ({
        display_text: option.trim(),
        id: `option_${index + 1}`,
        title: option.trim()
      }));

      nodeOptions = {
        options: interactiveOptions,
        extract_variable: nodeForm.extractVariable || null,
        interaction_type: nodeForm.interactionType || 'buttons'
      };
    } else if (nodeForm.nodeType === 'action') {
      // For action nodes, store action configuration
      nodeOptions = {
        action_type: nodeForm.actionType,
        webhook_url: nodeForm.webhookUrl || null,
        delay_seconds: nodeForm.delaySeconds || 5,
        email_recipients: nodeForm.emailRecipients || null,
        email_subject: nodeForm.emailSubject || null,
        email_body: nodeForm.emailBody || null,
        email_template: nodeForm.emailTemplate || null,
        save_data_fields: nodeForm.saveDataFields || [],
        api_endpoint: nodeForm.apiEndpoint || null,
        api_method: nodeForm.apiMethod || 'POST',
        api_headers: nodeForm.apiHeaders || null,
        api_body: nodeForm.apiBody || null
      };
    } else if (nodeForm.nodeType === 'condition') {
      // For condition nodes, store condition configuration
      nodeOptions = {
        condition_type: nodeForm.conditionType,
        response_conditions: nodeForm.responseConditions || [],
        condition_variable: nodeForm.conditionVariable || null,
        condition_operator: nodeForm.conditionOperator || 'equals',
        condition_value: nodeForm.conditionValue || null,
        random_paths: nodeForm.randomPaths || [],
        true_path: nodeForm.truePath || null,
        false_path: nodeForm.falsePath || null
      };
    } else {
      nodeOptions = nodeForm.options.filter(opt => opt.trim());
    }

    const newNode = {
      id: Date.now(), // Temporary ID for frontend
      name: nodeForm.name.trim(),
      message: nodeForm.message.trim(),
      nodeType: nodeForm.nodeType,
      options: nodeOptions,
      nextNodeId: nodeForm.nextNodeId,
      templateId: nodeForm.templateId || null,
      attachmentData: attachmentData,
      attachmentType: attachmentType,
      extractVariable: nodeForm.extractVariable || null,
      conditions: nodeForm.conditions || []
    };

    if (editingNodeIndex >= 0) {
      // Edit existing node
      const updatedNodes = [...flowForm.nodes];
      updatedNodes[editingNodeIndex] = newNode;
      setFlowForm(prev => ({ ...prev, nodes: updatedNodes }));
      setEditingNodeIndex(-1);
    } else {
      // Add new node
      setFlowForm(prev => ({ ...prev, nodes: [...prev.nodes, newNode] }));
    }

    setShowNodeModal(false);
    resetNodeForm();
  };

  const handleEditNode = (index) => {
    const node = flowForm.nodes[index];

    // Handle different node types and their configurations
    let nodeOptions = [];
    let extractVariable = '';
    let interactionType = 'buttons';
    let actionConfig = {};
    let conditionConfig = {};

    if (node.nodeType === 'question') {
      if (Array.isArray(node.options)) {
        // Old format: just an array of strings
        nodeOptions = node.options.map(option =>
          typeof option === 'string' ? option : (typeof option === 'object' && option ? (option.display_text || option.title || option.name || option.text || '') : '')
        );
      } else if (typeof node.options === 'object' && node.options) {
        // New format: object with options array and extract_variable
        const optionsArray = node.options.options || [];
        nodeOptions = optionsArray.map(option =>
          typeof option === 'string' ? option : (typeof option === 'object' && option ? (option.display_text || option.title || option.name || option.text || '') : '')
        );
        extractVariable = node.options.extract_variable || '';
        interactionType = node.options.interaction_type || 'buttons';
      }
    } else if (node.nodeType === 'action') {
      // Load action configuration
      if (typeof node.options === 'object' && node.options) {
        actionConfig = {
          actionType: node.options.action_type || 'webhook',
          webhookUrl: node.options.webhook_url || '',
          delaySeconds: node.options.delay_seconds || 5,
          emailRecipients: node.options.email_recipients || '',
          emailSubject: node.options.email_subject || '',
          emailBody: node.options.email_body || '',
          emailTemplate: node.options.email_template || '',
          saveDataFields: node.options.save_data_fields || [],
          apiEndpoint: node.options.api_endpoint || '',
          apiMethod: node.options.api_method || 'POST',
          apiHeaders: node.options.api_headers || '',
          apiBody: node.options.api_body || ''
        };
      }
    } else if (node.nodeType === 'condition') {
      // Load condition configuration
      if (typeof node.options === 'object' && node.options) {
        conditionConfig = {
          conditionType: node.options.condition_type || 'user_response',
          responseConditions: node.options.response_conditions || [],
          conditionVariable: node.options.condition_variable || '',
          conditionOperator: node.options.condition_operator || 'equals',
          conditionValue: node.options.condition_value || '',
          randomPaths: node.options.random_paths || [],
          truePath: node.options.true_path || null,
          falsePath: node.options.false_path || null
        };
      }
    } else {
      // Message node or other types
      if (Array.isArray(node.options)) {
        nodeOptions = node.options;
      }
    }

    setNodeForm({
      name: node.name,
      message: node.message,
      nodeType: node.nodeType,
      options: nodeOptions,
      nextNodeId: node.nextNodeId,
      templateId: node.templateId || '',
      extractVariable: extractVariable,
      interactionType: interactionType,
      messageType: 'text',
      variables: {},
      attachmentFile: null,
      attachmentType: 'image',
      conditions: [],
      // Action node fields
      actionType: actionConfig.actionType || 'webhook',
      webhookUrl: actionConfig.webhookUrl || '',
      delaySeconds: actionConfig.delaySeconds || 5,
      emailRecipients: actionConfig.emailRecipients || '',
      emailSubject: actionConfig.emailSubject || '',
      emailBody: actionConfig.emailBody || '',
      emailTemplate: actionConfig.emailTemplate || '',
      saveDataFields: actionConfig.saveDataFields || [],
      apiEndpoint: actionConfig.apiEndpoint || '',
      apiMethod: actionConfig.apiMethod || 'POST',
      apiHeaders: actionConfig.apiHeaders || '',
      apiBody: actionConfig.apiBody || '',
      // Condition node fields
      conditionType: conditionConfig.conditionType || 'user_response',
      responseConditions: conditionConfig.responseConditions || [],
      conditionVariable: conditionConfig.conditionVariable || '',
      conditionOperator: conditionConfig.conditionOperator || 'equals',
      conditionValue: conditionConfig.conditionValue || '',
      randomPaths: conditionConfig.randomPaths || [],
      truePath: conditionConfig.truePath || null,
      falsePath: conditionConfig.falsePath || null
    });
    setEditingNodeIndex(index);
    setShowNodeModal(true);
  };

  const handleDeleteNode = async (index) => {
    const confirmed = await confirm('Are you sure you want to delete this node?', 'Delete Node');
    if (confirmed) {
      const updatedNodes = flowForm.nodes.filter((_, i) => i !== index);
      setFlowForm(prev => ({ ...prev, nodes: updatedNodes }));
    }
  };

  const handleTemplateSelect = (templateId) => {
    const safeTemplates = Array.isArray(templates) ? templates : [];
    const template = safeTemplates.find(t => t.id === parseInt(templateId));
    if (template) {
      // Parse template variables
      let variables = [];
      try {
        variables = JSON.parse(template.variables || '[]');
        if (!Array.isArray(variables)) {
          variables = [];
        }
      } catch (error) {
        console.error('Error parsing template variables:', error);
        variables = [];
      }

      const variablesObj = {};
      variables.forEach(variable => {
        // Ensure variable is a string
        const varName = typeof variable === 'string' ? variable : String(variable || '');
        if (varName) {
          variablesObj[varName] = `{{${varName}}}`;
        }
      });

      setNodeForm(prev => ({
        ...prev,
        templateId: templateId,
        message: template.content || '',
        variables: variablesObj
      }));
    } else {
      setNodeForm(prev => ({
        ...prev,
        templateId: '',
        message: '',
        variables: {}
      }));
    }
  };

  const resetFlowForm = () => {
    setFlowForm({
      id: null, // Add ID field for editing
      name: '',
      description: '',
      sessionId: '',
      triggerKeywords: '',
      keywordMatchType: 'contains',
      keywordCaseSensitive: false,
      isActive: true,
      cooldownMinutes: 0,
      nodes: []
    });
  };

  const resetNodeForm = () => {
    setNodeForm({
      name: '',
      message: '',
      nodeType: 'message',
      messageType: 'text',
      options: [],
      nextNodeId: null,
      templateId: '',
      variables: {},
      attachmentFile: null,
      attachmentType: 'image',
      extractVariable: '',
      interactionType: 'buttons', // 'buttons' or 'text'
      conditions: [],
      // Action node fields
      actionType: 'webhook',
      webhookUrl: '',
      delaySeconds: 5,
      emailRecipients: '',
      emailSubject: '',
      emailBody: '',
      emailTemplate: '',
      saveDataFields: [],
      apiEndpoint: '',
      apiMethod: 'POST',
      apiHeaders: '',
      apiBody: '',
      // Condition node fields
      conditionType: 'user_response',
      responseConditions: [],
      conditionVariable: '',
      conditionOperator: 'equals',
      conditionValue: '',
      randomPaths: [],
      truePath: null,
      falsePath: null
    });
  };

  const addOption = () => {
    const currentOptions = Array.isArray(nodeForm.options) ? nodeForm.options : [];
    setNodeForm(prev => ({ ...prev, options: [...currentOptions, ''] }));
  };

  const updateOption = (index, value) => {
    const updatedOptions = [...(Array.isArray(nodeForm.options) ? nodeForm.options : [])];
    updatedOptions[index] = String(value || ''); // Ensure it's always a string
    setNodeForm(prev => ({ ...prev, options: updatedOptions }));
  };

  const removeOption = (index) => {
    const currentOptions = Array.isArray(nodeForm.options) ? nodeForm.options : [];
    const updatedOptions = currentOptions.filter((_, i) => i !== index);
    setNodeForm(prev => ({ ...prev, options: updatedOptions }));
  };

  // Optimized onChange handlers to prevent re-renders
  const handleSearchTermChange = useCallback((e) => {
    setSearchTerm(e.target.value);
  }, []);

  const handleStatusFilterChange = useCallback((e) => {
    setStatusFilter(e.target.value);
  }, []);

  const handleFlowFormNameChange = useCallback((e) => {
    setFlowForm(prev => ({ ...prev, name: e.target.value }));
  }, []);

  const handleFlowFormDescriptionChange = useCallback((e) => {
    setFlowForm(prev => ({ ...prev, description: e.target.value }));
  }, []);

  const handleFlowFormSessionChange = useCallback((e) => {
    setFlowForm(prev => ({ ...prev, sessionId: e.target.value }));
  }, []);

  const handleFlowFormKeywordsChange = useCallback((e) => {
    setFlowForm(prev => ({ ...prev, triggerKeywords: e.target.value }));
  }, []);

  const handleFlowFormCooldownChange = useCallback((e) => {
    setFlowForm(prev => ({ ...prev, cooldownMinutes: parseInt(e.target.value) || 0 }));
  }, []);

  const handleFlowFormActiveChange = useCallback((e) => {
    setFlowForm(prev => ({ ...prev, isActive: e.target.checked }));
  }, []);

  const handleFlowFormMatchTypeChange = useCallback((e) => {
    setFlowForm(prev => ({ ...prev, keywordMatchType: e.target.value }));
  }, []);

  const handleFlowFormCaseSensitiveChange = useCallback((e) => {
    setFlowForm(prev => ({ ...prev, keywordCaseSensitive: e.target.checked }));
  }, []);

  const handleNodeFormNameChange = useCallback((e) => {
    setNodeForm(prev => ({ ...prev, name: e.target.value }));
  }, []);

  const handleNodeFormMessageChange = useCallback((e) => {
    setNodeForm(prev => ({ ...prev, message: e.target.value }));
  }, []);

  const handleNodeFormAttachmentTypeChange = useCallback((e) => {
    setNodeForm(prev => ({ ...prev, attachmentType: e.target.value }));
  }, []);

  const handleNodeFormAttachmentFileChange = useCallback((e) => {
    setNodeForm(prev => ({ ...prev, attachmentFile: e.target.files[0] }));
  }, []);

  const handleNodeFormExtractVariableChange = useCallback((e) => {
    setNodeForm(prev => ({ ...prev, extractVariable: e.target.value }));
  }, []);

  const handleNodeFormNextNodeChange = useCallback((e) => {
    setNodeForm(prev => ({ ...prev, nextNodeId: e.target.value ? parseInt(e.target.value) : null }));
  }, []);

  // Export chatbots functionality
  const handleExportChatbots = async () => {
    try {
      // Get all chatbot flows with their nodes
      const flowsResponse = await window.electronAPI.database.query(
        'SELECT * FROM chatbot_flows ORDER BY created_at DESC'
      );

      if (!flowsResponse.success) {
        showError('Export Failed', 'Failed to fetch chatbot flows: ' + flowsResponse.error);
        return;
      }

      const flows = flowsResponse.data || [];

      // Get AI chatbots
      const aiChatbotsResponse = await window.electronAPI.database.query(
        'SELECT * FROM ai_chatbots ORDER BY created_at DESC'
      );

      const aiChatbots = aiChatbotsResponse.success ? (aiChatbotsResponse.data || []) : [];

      const exportData = {
        version: '1.0',
        exportDate: new Date().toISOString(),
        type: 'chatbot_complete_export',
        flows: [],
        aiChatbots: aiChatbots.map(bot => ({
          ...bot,
          // Remove sensitive data
          api_key: '[REDACTED]'
        }))
      };

      // Get nodes for each flow
      for (const flow of flows) {
        const nodesResponse = await window.electronAPI.database.query(
          'SELECT * FROM chatbot_nodes WHERE flow_id = ? ORDER BY position ASC',
          [flow.id]
        );

        const nodes = nodesResponse.success ? (nodesResponse.data || []) : [];

        exportData.flows.push({
          ...flow,
          nodes: nodes
        });
      }

      // Create and download the file
      const dataStr = JSON.stringify(exportData, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });

      const link = document.createElement('a');
      link.href = URL.createObjectURL(dataBlob);
      link.download = `chatbots-complete-${new Date().toISOString().split('T')[0]}.json`;
      link.click();

      showSuccess(
        'Export Complete',
        `Successfully exported ${flows.length} chatbot flows and ${aiChatbots.length} AI chatbots!`
      );
    } catch (error) {
      console.error('Error exporting chatbots:', error);
      showError('Export Failed', 'Error exporting chatbots. Please try again.');
    }
  };

  // Clean up orphaned conversations
  const handleCleanupOrphanedConversations = async () => {
    try {
      const shouldProceed = await confirm(
        'This will end all active conversations for disabled or deleted chatbot flows. This can help fix issues with stuck conversations. Continue?',
        'Cleanup Orphaned Conversations'
      );

      if (!shouldProceed) {
        return;
      }

      const result = await window.electronAPI.invoke('chatbot:cleanup-orphaned-conversations');

      if (result.success) {
        showSuccess(
          'Cleanup Complete',
          `Successfully cleaned up ${result.cleaned} orphaned conversations (${result.inactiveFlows} from inactive flows, ${result.deletedFlows} from deleted flows)`
        );
      } else {
        showError('Cleanup Failed', 'Failed to cleanup orphaned conversations: ' + result.error);
      }
    } catch (error) {
      console.error('Error cleaning up orphaned conversations:', error);
      showError('Cleanup Failed', 'Error cleaning up orphaned conversations. Please try again.');
    }
  };

  // Import chatbots functionality
  const handleImportChatbots = async () => {
    try {
      // Show in-app confirmation dialog
      const shouldProceed = await confirm(
        'This will import chatbot flows and AI chatbots from a JSON file. Existing data will not be affected. Continue?',
        'Import Chatbot Data'
      );

      if (!shouldProceed) {
        return;
      }

      const result = await window.electronAPI.showOpenDialog({
        title: 'Import Chatbot Data',
        filters: [
          { name: 'JSON Files', extensions: ['json'] },
          { name: 'All Files', extensions: ['*'] }
        ],
        properties: ['openFile']
      });

      if (result.canceled || !result.filePaths || result.filePaths.length === 0) {
        return;
      }

      const filePath = result.filePaths[0];

      // Read file content using the exposed fs API
      const fileResult = await window.electronAPI.fs.readFile(filePath);
      if (!fileResult.success) {
        showError('Import Failed', 'Failed to read file: ' + fileResult.error);
        return;
      }

      let importData;
      try {
        importData = JSON.parse(fileResult.data);
      } catch (parseError) {
        showError('Import Failed', 'Invalid JSON file format. Please check the file and try again.');
        return;
      }

      // Validate import data
      if (!importData.flows && !importData.aiChatbots) {
        showError('Import Failed', 'Invalid file format. Expected chatbot data.');
        return;
      }

      // Show preview of what will be imported
      const flowCount = importData.flows ? importData.flows.length : 0;
      const aiChatbotCount = importData.aiChatbots ? importData.aiChatbots.length : 0;

      let previewMessage = 'Import Preview:\n\n';
      if (flowCount > 0) {
        previewMessage += `• ${flowCount} chatbot flow(s)\n`;
      }
      if (aiChatbotCount > 0) {
        previewMessage += `• ${aiChatbotCount} AI chatbot(s)\n`;
      }
      previewMessage += '\nProceed with import?';

      const shouldImport = await confirm(previewMessage, 'Confirm Import');

      if (!shouldImport) {
        return;
      }

      let flowSuccessCount = 0;
      let flowErrorCount = 0;
      let aiChatbotSuccessCount = 0;
      let aiChatbotErrorCount = 0;

      // Import chatbot flows
      if (importData.flows && Array.isArray(importData.flows)) {
        for (const flowData of importData.flows) {
          try {
            // Create the flow (excluding id, created_at, updated_at)
            const flowResult = await window.electronAPI.database.query(
              `INSERT INTO chatbot_flows (
                session_id, name, description, trigger_keywords, is_active,
                welcome_message, fallback_message, cooldown_minutes,
                conversation_count, last_triggered, created_at, updated_at
              ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)`,
              [
                flowData.session_id || '',
                flowData.name || 'Imported Flow',
                flowData.description || null,
                flowData.trigger_keywords || '',
                flowData.is_active !== undefined ? flowData.is_active : 1,
                flowData.welcome_message || null,
                flowData.fallback_message || null,
                flowData.cooldown_minutes || 0,
                0, // Reset conversation count
                null // Reset last triggered
              ]
            );

            if (flowResult.success && flowData.nodes && Array.isArray(flowData.nodes)) {
              const newFlowId = flowResult.insertId;

              // Import nodes for this flow
              for (const nodeData of flowData.nodes) {
                await window.electronAPI.database.query(
                  `INSERT INTO chatbot_nodes (
                    flow_id, name, message, node_type, options, next_node_id,
                    position, template_id, attachment_data, attachment_type,
                    created_at, updated_at
                  ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)`,
                  [
                    newFlowId,
                    nodeData.name || 'Imported Node',
                    nodeData.message || '',
                    nodeData.node_type || 'message',
                    nodeData.options || null,
                    nodeData.next_node_id || null,
                    nodeData.position || 0,
                    nodeData.template_id || null,
                    nodeData.attachment_data || null,
                    nodeData.attachment_type || null
                  ]
                );
              }
            }

            flowSuccessCount++;
          } catch (error) {
            console.error('Error importing flow:', error);
            flowErrorCount++;
          }
        }
      }

      // Import AI chatbots (note: API keys will need to be manually configured)
      if (importData.aiChatbots && Array.isArray(importData.aiChatbots)) {
        for (const aiChatbotData of importData.aiChatbots) {
          try {
            // Skip if API key is redacted and prompt user
            if (aiChatbotData.api_key === '[REDACTED]') {
              console.warn('Skipping AI chatbot import - API key was redacted for security');
              continue;
            }

            await window.electronAPI.database.query(
              `INSERT INTO ai_chatbots (
                name, description, provider, api_key, model, temperature, max_tokens,
                system_prompt, language, is_active, session_ids, features,
                personality, industry, response_delay, fallback_message,
                max_conversation_length, enable_learning, confidence_threshold,
                created_at, updated_at
              ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)`,
              [
                (aiChatbotData.name || 'Imported AI Chatbot') + ' (Imported)',
                aiChatbotData.description || null,
                aiChatbotData.provider || 'openai',
                aiChatbotData.api_key || '',
                aiChatbotData.model || 'gpt-3.5-turbo',
                aiChatbotData.temperature || 0.7,
                aiChatbotData.max_tokens || 1000,
                aiChatbotData.system_prompt || null,
                aiChatbotData.language || 'en',
                0, // Set as inactive by default for security
                aiChatbotData.session_ids || null,
                aiChatbotData.features || null,
                aiChatbotData.personality || 'professional',
                aiChatbotData.industry || 'general',
                aiChatbotData.response_delay || 1000,
                aiChatbotData.fallback_message || null,
                aiChatbotData.max_conversation_length || 50,
                aiChatbotData.enable_learning !== undefined ? aiChatbotData.enable_learning : 1,
                aiChatbotData.confidence_threshold || 0.7
              ]
            );

            aiChatbotSuccessCount++;
          } catch (error) {
            console.error('Error importing AI chatbot:', error);
            aiChatbotErrorCount++;
          }
        }
      }

      // Reload flows
      await loadFlows();

      const totalSuccess = flowSuccessCount + aiChatbotSuccessCount;
      const totalErrors = flowErrorCount + aiChatbotErrorCount;

      let message = 'Import completed!\n';
      if (flowSuccessCount > 0) {
        message += `Chatbot flows imported: ${flowSuccessCount}\n`;
      }
      if (aiChatbotSuccessCount > 0) {
        message += `AI chatbots imported: ${aiChatbotSuccessCount}\n`;
      }
      if (totalErrors > 0) {
        message += `Errors: ${totalErrors}\n`;
      }
      if (importData.aiChatbots && importData.aiChatbots.some(bot => bot.api_key === '[REDACTED]')) {
        message += '\nNote: AI chatbots with redacted API keys were skipped. Please configure API keys manually.';
      }

      showSuccess('Import Complete', message);

    } catch (error) {
      console.error('Error importing chatbots:', error);
      showError('Import Failed', 'Error importing chatbots. Please check the file format and try again.');
    }
  };

  // Filter flows - ensure flows is always an array
  const safeFlows = Array.isArray(flows) ? flows : [];
  const filteredFlows = safeFlows.filter(flow => {
    const flowName = typeof flow.name === 'string' ? flow.name : String(flow.name || '');
    const triggerKeywords = typeof flow.trigger_keywords === 'string' ? flow.trigger_keywords : String(flow.trigger_keywords || '');
    const matchesSearch = flowName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         triggerKeywords.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' ||
                         (statusFilter === 'active' && flow.is_active) ||
                         (statusFilter === 'inactive' && !flow.is_active);
    return matchesSearch && matchesStatus;
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="flex items-center space-x-2">
          <div className="w-6 h-6 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
          <span className="text-gray-600">Loading chatbot flows...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Chatbot</h1>
          <p className="text-gray-600">Create intelligent conversational flows</p>
        </div>
        <div className="flex items-center space-x-2">
          <button
            onClick={handleExportChatbots}
            className="btn-secondary flex items-center space-x-2"
            title="Export Chatbot Flows"
          >
            <ArrowDownTrayIcon className="w-5 h-5" />
            <span>Export</span>
          </button>
          <button
            onClick={handleImportChatbots}
            className="btn-secondary flex items-center space-x-2"
            title="Import Chatbot Flows"
          >
            <ArrowUpTrayIcon className="w-5 h-5" />
            <span>Import</span>
          </button>
          <button
            onClick={handleCleanupOrphanedConversations}
            className="btn-secondary flex items-center space-x-2"
            title="Clean up orphaned conversations from disabled/deleted flows"
          >
            <TrashIcon className="w-5 h-5" />
            <span>Cleanup</span>
          </button>
          <button
            onClick={loadSessions}
            className="btn-secondary flex items-center space-x-2"
            title="Refresh Sessions"
          >
            <span>🔄</span>
            <span>Refresh</span>
          </button>
          <button
            onClick={() => setShowCreateModal(true)}
            className="btn-primary flex items-center space-x-2"
            disabled={sessions.length === 0}
          >
            <PlusIcon className="w-5 h-5" />
            <span>Create Flow</span>
          </button>
        </div>
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        {/* Search */}
        <div className="relative flex-1">
          <MagnifyingGlassIcon className="w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          <input
            type="text"
            placeholder="Search flows..."
            value={searchTerm}
            onChange={handleSearchTermChange}
            className="input-field pl-10"
          />
        </div>

        {/* Status Filter */}
        <select
          value={statusFilter}
          onChange={handleStatusFilterChange}
          className="input-field sm:w-48"
        >
          <option value="all">All Flows</option>
          <option value="active">Active</option>
          <option value="inactive">Inactive</option>
        </select>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="card">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <ChatBubbleOvalLeftEllipsisIcon className="h-8 w-8 text-blue-500" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Total Flows</p>
              <p className="text-2xl font-bold text-gray-900">{flows.length}</p>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <PlayIcon className="h-8 w-8 text-green-500" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Active Flows</p>
              <p className="text-2xl font-bold text-gray-900">
                {safeFlows.filter(f => f.is_active).length}
              </p>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <BoltIcon className="h-8 w-8 text-purple-500" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Total Nodes</p>
              <p className="text-2xl font-bold text-gray-900">
                {safeFlows.reduce((total, f) => total + (f.node_count || 0), 0)}
              </p>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <CheckCircleIcon className="h-8 w-8 text-yellow-500" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Conversations</p>
              <p className="text-2xl font-bold text-gray-900">
                {safeFlows.reduce((total, f) => total + (f.conversation_count || 0), 0)}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Prerequisites Check */}
      {sessions.length === 0 && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex items-center space-x-2">
            <ExclamationTriangleIcon className="w-5 h-5 text-yellow-500" />
            <div>
              <h3 className="text-sm font-medium text-yellow-800">WhatsApp Connection Required</h3>
              <p className="text-sm text-yellow-700 mt-1">
                Connect a WhatsApp device first to create chatbot flows.
              </p>
              <p className="text-xs text-yellow-600 mt-2">
                Debug: Sessions loaded: {sessions.length} | Check browser console for details
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Flows List */}
      {filteredFlows.length === 0 ? (
        <div className="text-center py-12">
          <ChatBubbleOvalLeftEllipsisIcon className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            {searchTerm || statusFilter !== 'all' ? 'No flows found' : 'No chatbot flows yet'}
          </h3>
          <p className="text-gray-600 mb-6">
            {searchTerm || statusFilter !== 'all'
              ? 'Try adjusting your search or filter criteria'
              : 'Create your first chatbot flow to start automated conversations'
            }
          </p>
          {!searchTerm && statusFilter === 'all' && sessions.length > 0 && (
            <button
              onClick={() => setShowCreateModal(true)}
              className="btn-primary flex items-center space-x-2 mx-auto"
            >
              <PlusIcon className="w-5 h-5" />
              <span>Create Flow</span>
            </button>
          )}
        </div>
      ) : (
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
          {/* Professional List View */}
          {/* Table Header */}
          <div className="bg-gray-50 px-6 py-4 border-b border-gray-200">
            <div className="grid grid-cols-12 gap-4 items-center text-sm font-medium text-gray-700">
              <div className="col-span-4">Flow</div>
              <div className="col-span-2">Status</div>
              <div className="col-span-2">Device</div>
              <div className="col-span-2">Nodes</div>
              <div className="col-span-1">Conversations</div>
              <div className="col-span-1 text-right">Actions</div>
            </div>
          </div>

          {/* Flows List */}
          <div className="divide-y divide-gray-200">
            {filteredFlows.map((flow) => (
              <div key={flow.id} className="px-6 py-4 hover:bg-gray-50 transition-colors duration-150">
                <div className="grid grid-cols-12 gap-4 items-center">
                  {/* Flow Name & Description */}
                  <div className="col-span-4">
                    <div className="flex items-start space-x-3">
                      <div className={`w-10 h-10 rounded-lg flex items-center justify-center flex-shrink-0 ${
                        flow.is_active ? 'bg-purple-100' : 'bg-gray-100'
                      }`}>
                        {flow.is_active ? (
                          <ChatBubbleOvalLeftEllipsisIcon className="w-5 h-5 text-purple-600" />
                        ) : (
                          <ChatBubbleOvalLeftEllipsisIcon className="w-5 h-5 text-gray-600" />
                        )}
                      </div>
                      <div className="flex-1 min-w-0">
                        <h3 className="font-semibold text-gray-900 truncate">
                          {typeof flow.name === 'string' ? flow.name : String(flow.name || 'Unnamed Flow')}
                        </h3>
                        {flow.description && (
                          <p className="text-sm text-gray-500 line-clamp-2 mt-1">
                            {flow.description}
                          </p>
                        )}
                        {/* Trigger Keywords */}
                        <div className="flex flex-wrap gap-1 mt-2">
                          {(typeof flow.trigger_keywords === 'string' ? flow.trigger_keywords : String(flow.trigger_keywords || '')).split(',').slice(0, 3).map((keyword, index) => (
                            <span
                              key={index}
                              className="inline-flex items-center px-2 py-1 rounded text-xs bg-blue-100 text-blue-800"
                            >
                              {keyword.trim()}
                            </span>
                          ))}
                          {(typeof flow.trigger_keywords === 'string' ? flow.trigger_keywords : String(flow.trigger_keywords || '')).split(',').length > 3 && (
                            <span className="text-xs text-gray-500">
                              +{(typeof flow.trigger_keywords === 'string' ? flow.trigger_keywords : String(flow.trigger_keywords || '')).split(',').length - 3} more
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Status */}
                  <div className="col-span-2">
                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                      flow.is_active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                    }`}>
                      {flow.is_active ? <PlayIcon className="w-3 h-3 mr-1" /> : <PauseIcon className="w-3 h-3 mr-1" />}
                      {flow.is_active ? 'Active' : 'Inactive'}
                    </span>
                  </div>

                  {/* Device */}
                  <div className="col-span-2">
                    <div className="text-sm text-gray-900 font-medium">
                      {flow.device_name ?
                        (typeof flow.device_name === 'string' ? flow.device_name : String(flow.device_name)) :
                        'No Device'
                      }
                    </div>
                  </div>

                  {/* Nodes Count */}
                  <div className="col-span-2">
                    <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-purple-100 text-purple-800">
                      {flow.node_count || 0} nodes
                    </span>
                  </div>

                  {/* Conversations Count */}
                  <div className="col-span-1">
                    <div className="text-sm text-gray-900 font-medium">
                      {flow.conversation_count || 0}
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="col-span-1">
                    <div className="flex items-center justify-end space-x-1">
                      <button
                        onClick={() => {
                          setSelectedFlow(flow);
                          setShowFlowModal(true);
                        }}
                        className="p-2 text-gray-400 hover:text-blue-500 hover:bg-blue-50 rounded-lg transition-colors"
                        title="View Flow"
                      >
                        <EyeIcon className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => handleEditFlow(flow)}
                        className="p-2 text-gray-400 hover:text-purple-500 hover:bg-purple-50 rounded-lg transition-colors"
                        title="Edit Flow"
                      >
                        <PencilIcon className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => toggleFlowStatus(flow.id, flow.is_active)}
                        className={`p-2 text-gray-400 hover:bg-opacity-10 rounded-lg transition-colors ${
                          flow.is_active
                            ? 'hover:text-yellow-500 hover:bg-yellow-50'
                            : 'hover:text-green-500 hover:bg-green-50'
                        }`}
                        title={flow.is_active ? 'Pause Flow' : 'Activate Flow'}
                      >
                        {flow.is_active ? <PauseIcon className="w-4 h-4" /> : <PlayIcon className="w-4 h-4" />}
                      </button>
                      <button
                        onClick={() => handleDeleteFlow(flow.id)}
                        className="p-2 text-gray-400 hover:text-red-500 hover:bg-red-50 rounded-lg transition-colors"
                        title="Delete Flow"
                      >
                        <TrashIcon className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Create Flow Modal */}
      {showCreateModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-gray-900">Create Chatbot Flow</h3>
                <button
                  onClick={() => {
                    setShowCreateModal(false);
                    resetFlowForm();
                  }}
                  className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg"
                >
                  <XMarkIcon className="w-5 h-5" />
                </button>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Left Column - Flow Settings */}
                <div className="space-y-4">
                  <h4 className="font-medium text-gray-900">Flow Settings</h4>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Flow Name *
                    </label>
                    <input
                      type="text"
                      value={flowForm.name}
                      onChange={handleFlowFormNameChange}
                      placeholder="e.g., Customer Support Bot"
                      className="input-field"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Description
                    </label>
                    <textarea
                      value={flowForm.description}
                      onChange={handleFlowFormDescriptionChange}
                      placeholder="Describe what this chatbot flow does..."
                      rows={3}
                      className="input-field resize-none"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      WhatsApp Session *
                    </label>
                    <select
                      value={flowForm.sessionId}
                      onChange={handleFlowFormSessionChange}
                      className="input-field"
                    >
                      <option value="">Select a session</option>
                      {(Array.isArray(sessions) ? sessions : []).map(session => {
                        // Ensure session is an object and has required properties
                        if (!session || typeof session !== 'object') {
                          return null;
                        }

                        const sessionId = session.session_id || `session_${Math.random()}`;
                        const deviceName = typeof session.device_name === 'string' ? session.device_name : String(session.device_name || 'Unknown Device');
                        const phoneNumber = typeof session.phone_number === 'string' || typeof session.phone_number === 'number' ? session.phone_number : String(session.phone_number || 'Unknown');

                        return (
                          <option key={sessionId} value={sessionId}>
                            {deviceName} (+{phoneNumber})
                          </option>
                        );
                      }).filter(Boolean)}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Trigger Keywords *
                    </label>
                    <input
                      type="text"
                      value={flowForm.triggerKeywords}
                      onChange={handleFlowFormKeywordsChange}
                      placeholder="support, help, bot (comma separated)"
                      className="input-field"
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      Separate multiple keywords with commas
                    </p>
                  </div>

                  {/* Keyword Matching Options */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Match Type
                      </label>
                      <select
                        value={flowForm.keywordMatchType}
                        onChange={handleFlowFormMatchTypeChange}
                        className="input-field"
                      >
                        <option value="contains">Contains (default)</option>
                        <option value="exact">Exact Match</option>
                        <option value="starts_with">Starts With</option>
                        <option value="ends_with">Ends With</option>
                      </select>
                      <p className="text-xs text-gray-500 mt-1">
                        How keywords should match the message
                      </p>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Case Sensitivity
                      </label>
                      <div className="flex items-center mt-3">
                        <input
                          type="checkbox"
                          id="keywordCaseSensitive"
                          checked={flowForm.keywordCaseSensitive}
                          onChange={handleFlowFormCaseSensitiveChange}
                          className="text-blue-600"
                        />
                        <label htmlFor="keywordCaseSensitive" className="ml-2 text-sm text-gray-700">
                          Match case
                        </label>
                      </div>
                      <p className="text-xs text-gray-500 mt-1">
                        Enable for case-sensitive keyword matching
                      </p>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      <ClockIcon className="w-4 h-4 inline mr-1" />
                      Cooldown Period (minutes)
                    </label>
                    <input
                      type="number"
                      min="0"
                      value={flowForm.cooldownMinutes}
                      onChange={handleFlowFormCooldownChange}
                      placeholder="0"
                      className="input-field"
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      Minimum time between flow triggers for the same user (0 = no cooldown)
                    </p>
                  </div>

                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="flowIsActive"
                      checked={flowForm.isActive}
                      onChange={handleFlowFormActiveChange}
                      className="text-blue-600"
                    />
                    <label htmlFor="flowIsActive" className="ml-2 text-sm text-gray-700">
                      Flow is active
                    </label>
                  </div>
                </div>

                {/* Right Column - Flow Nodes */}
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h4 className="font-medium text-gray-900">Flow Nodes ({flowForm.nodes.length})</h4>
                    <button
                      onClick={() => setShowNodeModal(true)}
                      className="btn-secondary flex items-center space-x-2 text-sm"
                    >
                      <PlusIcon className="w-4 h-4" />
                      <span>Add Node</span>
                    </button>
                  </div>

                  <div className="space-y-2 max-h-64 overflow-y-auto">
                    {flowForm.nodes.map((node, index) => {
                      // Ensure node is an object and has required properties
                      if (!node || typeof node !== 'object') {
                        return null;
                      }

                      const nodeName = typeof node.name === 'string' ? node.name : 'Unnamed Node';
                      const nodeType = typeof node.nodeType === 'string' ? node.nodeType : 'unknown';
                      const nodeMessage = typeof node.message === 'string' ? node.message : 'No message';

                      return (
                        <div key={index} className="p-3 border border-gray-200 rounded-lg">
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <div className="flex items-center space-x-2">
                                <span className="text-sm font-medium text-gray-900">{index + 1}. {nodeName}</span>
                                <span className="inline-flex items-center px-2 py-1 rounded text-xs bg-blue-100 text-blue-800">
                                  {nodeType}
                                </span>
                              </div>
                              <p className="text-xs text-gray-600 mt-1 line-clamp-2">{nodeMessage}</p>
                            {(() => {
                              // Handle both old array format and new Baileys interactive format
                              let optionsToDisplay = [];
                              if (Array.isArray(node.options)) {
                                optionsToDisplay = node.options;
                              } else if (typeof node.options === 'object' && node.options && node.options.options) {
                                optionsToDisplay = node.options.options;
                              }

                              if (optionsToDisplay.length > 0) {
                                return (
                                  <div className="flex flex-wrap gap-1 mt-2">
                                    {optionsToDisplay.map((option, optIndex) => {
                                      // Ensure we always render a string
                                      let displayText = 'Option';
                                      if (typeof option === 'string') {
                                        displayText = option;
                                      } else if (typeof option === 'object' && option) {
                                        // Handle Baileys interactive format
                                        displayText = String(option.display_text || option.title || option.name || option.text || option.value || 'Option');
                                      } else {
                                        displayText = String(option || 'Option');
                                      }

                                      return (
                                        <span key={optIndex} className="text-xs px-1 py-0.5 bg-gray-100 rounded">
                                          {displayText}
                                        </span>
                                      );
                                    })}
                                  </div>
                                );
                              }
                              return null;
                            })()}
                          </div>
                          <div className="flex items-center space-x-1 ml-2">
                            <button
                              onClick={() => handleEditNode(index)}
                              className="p-1 text-gray-400 hover:text-blue-500 rounded"
                            >
                              <PencilIcon className="w-3 h-3" />
                            </button>
                            <button
                              onClick={() => handleDeleteNode(index)}
                              className="p-1 text-gray-400 hover:text-red-500 rounded"
                            >
                              <TrashIcon className="w-3 h-3" />
                            </button>
                          </div>
                        </div>
                      </div>
                      );
                    }).filter(Boolean)}
                    
                    {flowForm.nodes.length === 0 && (
                      <div className="text-center py-8 text-gray-500">
                        <ChatBubbleOvalLeftEllipsisIcon className="w-8 h-8 mx-auto mb-2 text-gray-400" />
                        <p className="text-sm">No nodes added yet</p>
                        <p className="text-xs">Add your first node to get started</p>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              <div className="flex space-x-3 mt-6">
                <button
                  onClick={() => {
                    setShowCreateModal(false);
                    resetFlowForm();
                  }}
                  className="btn-secondary flex-1"
                >
                  Cancel
                </button>
                <button
                  onClick={handleCreateFlow}
                  className="btn-primary flex-1"
                  disabled={
                    !flowForm.name.trim() || 
                    !flowForm.triggerKeywords.trim() || 
                    !flowForm.sessionId ||
                    flowForm.nodes.length === 0
                  }
                >
                  Create Flow
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Edit Flow Modal */}
      {showEditModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-gray-900">Edit Chatbot Flow</h3>
                <button
                  onClick={() => {
                    setShowEditModal(false);
                    resetFlowForm();
                  }}
                  className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg"
                >
                  <XMarkIcon className="w-5 h-5" />
                </button>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Left Column - Flow Settings */}
                <div className="space-y-4">
                  <h4 className="font-medium text-gray-900">Flow Settings</h4>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Flow Name *
                    </label>
                    <input
                      type="text"
                      value={flowForm.name}
                      onChange={handleFlowFormNameChange}
                      placeholder="e.g., Customer Support Bot"
                      className="input-field"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Description
                    </label>
                    <textarea
                      value={flowForm.description}
                      onChange={handleFlowFormDescriptionChange}
                      placeholder="Describe what this chatbot flow does..."
                      rows={3}
                      className="input-field resize-none"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      WhatsApp Session *
                    </label>
                    <select
                      value={flowForm.sessionId}
                      onChange={handleFlowFormSessionChange}
                      className="input-field"
                    >
                      <option value="">Select a session</option>
                      {(Array.isArray(sessions) ? sessions : []).map(session => {
                        // Ensure session is an object and has required properties
                        if (!session || typeof session !== 'object') {
                          return null;
                        }

                        const sessionId = session.session_id || `session_${Math.random()}`;
                        const deviceName = typeof session.device_name === 'string' ? session.device_name : String(session.device_name || 'Unknown Device');
                        const phoneNumber = typeof session.phone_number === 'string' || typeof session.phone_number === 'number' ? session.phone_number : String(session.phone_number || 'Unknown');

                        return (
                          <option key={sessionId} value={sessionId}>
                            {deviceName} (+{phoneNumber})
                          </option>
                        );
                      })}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Trigger Keywords *
                    </label>
                    <input
                      type="text"
                      value={flowForm.triggerKeywords}
                      onChange={handleFlowFormKeywordsChange}
                      placeholder="e.g., support, help, info (comma separated)"
                      className="input-field"
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      Separate multiple keywords with commas
                    </p>
                  </div>

                  {/* Keyword Matching Options */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Match Type
                      </label>
                      <select
                        value={flowForm.keywordMatchType}
                        onChange={handleFlowFormMatchTypeChange}
                        className="input-field"
                      >
                        <option value="contains">Contains (default)</option>
                        <option value="exact">Exact Match</option>
                        <option value="starts_with">Starts With</option>
                        <option value="ends_with">Ends With</option>
                      </select>
                      <p className="text-xs text-gray-500 mt-1">
                        How keywords should match the message
                      </p>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Case Sensitivity
                      </label>
                      <div className="flex items-center mt-3">
                        <input
                          type="checkbox"
                          id="editKeywordCaseSensitive"
                          checked={flowForm.keywordCaseSensitive}
                          onChange={handleFlowFormCaseSensitiveChange}
                          className="text-blue-600"
                        />
                        <label htmlFor="editKeywordCaseSensitive" className="ml-2 text-sm text-gray-700">
                          Match case
                        </label>
                      </div>
                      <p className="text-xs text-gray-500 mt-1">
                        Enable for case-sensitive keyword matching
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-4">
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={flowForm.isActive}
                        onChange={handleFlowFormActiveChange}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <span className="ml-2 text-sm text-gray-700">Active</span>
                    </label>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Cooldown (minutes)
                    </label>
                    <input
                      type="number"
                      value={flowForm.cooldownMinutes}
                      onChange={handleFlowFormCooldownChange}
                      placeholder="0"
                      min="0"
                      className="input-field"
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      Minimum time between flow triggers for the same user
                    </p>
                  </div>
                </div>

                {/* Right Column - Flow Nodes */}
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h4 className="font-medium text-gray-900">Flow Nodes ({flowForm.nodes.length})</h4>
                    <button
                      onClick={() => setShowNodeModal(true)}
                      className="btn-secondary text-sm"
                    >
                      <PlusIcon className="w-4 h-4 mr-1" />
                      Add Node
                    </button>
                  </div>

                  <div className="space-y-3 max-h-96 overflow-y-auto">
                    {flowForm.nodes.map((node, index) => (
                      <div key={index} className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center space-x-2 mb-2">
                              <span className="text-xs font-medium text-gray-500">#{index + 1}</span>
                              <span className="text-sm font-medium text-gray-900">{node.name}</span>
                              <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                                node.nodeType === 'message' ? 'bg-blue-100 text-blue-800' :
                                node.nodeType === 'question' ? 'bg-green-100 text-green-800' :
                                'bg-purple-100 text-purple-800'
                              }`}>
                                {node.nodeType}
                              </span>
                            </div>
                            <p className="text-sm text-gray-600 mb-2">{node.message}</p>
                            {(() => {
                              if (node.nodeType === 'question' && node.options) {
                                let optionsToShow = [];
                                if (Array.isArray(node.options)) {
                                  optionsToShow = node.options;
                                } else if (typeof node.options === 'object' && node.options.options) {
                                  optionsToShow = node.options.options.map(opt =>
                                    typeof opt === 'string' ? opt : (opt.display_text || opt.title || opt.name || '')
                                  );
                                }

                                if (optionsToShow.length > 0) {
                                  return (
                                    <div className="flex flex-wrap gap-1">
                                      {optionsToShow.map((option, optIndex) => (
                                        <span key={optIndex} className="inline-flex items-center px-2 py-1 rounded text-xs bg-gray-200 text-gray-700">
                                          {typeof option === 'string' ? option : String(option || '')}
                                        </span>
                                      ))}
                                    </div>
                                  );
                                }
                              }
                              return null;
                            })()}
                          </div>
                          <div className="flex items-center space-x-1 ml-2">
                            <button
                              onClick={() => handleEditNode(index)}
                              className="p-1 text-gray-400 hover:text-blue-500 rounded"
                            >
                              <PencilIcon className="w-3 h-3" />
                            </button>
                            <button
                              onClick={() => handleDeleteNode(index)}
                              className="p-1 text-gray-400 hover:text-red-500 rounded"
                            >
                              <TrashIcon className="w-3 h-3" />
                            </button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              <div className="flex justify-end space-x-3 mt-6 pt-6 border-t border-gray-200">
                <button
                  onClick={() => {
                    setShowEditModal(false);
                    resetFlowForm();
                  }}
                  className="btn-secondary"
                >
                  Cancel
                </button>
                <button
                  onClick={handleUpdateFlow}
                  className="btn-primary"
                  disabled={
                    !flowForm.name.trim() ||
                    !flowForm.triggerKeywords.trim() ||
                    !flowForm.sessionId ||
                    flowForm.nodes.length === 0
                  }
                >
                  Update Flow
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Add/Edit Node Modal */}
      {showNodeModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-xl shadow-xl max-w-lg w-full max-h-[90vh] flex flex-col">
            <div className="p-6 flex-shrink-0">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-gray-900">
                  {editingNodeIndex >= 0 ? 'Edit Node' : 'Add Node'}
                </h3>
                <button
                  onClick={() => {
                    setShowNodeModal(false);
                    resetNodeForm();
                    setEditingNodeIndex(-1);
                  }}
                  className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg"
                >
                  <XMarkIcon className="w-5 h-5" />
                </button>
              </div>
            </div>

            <div className="flex-1 overflow-y-auto px-6">
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Node Name *
                  </label>
                  <input
                    type="text"
                    value={nodeForm.name}
                    onChange={handleNodeFormNameChange}
                    placeholder="e.g., Welcome Message"
                    className="input-field"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Node Type
                  </label>
                  <select
                    value={nodeForm.nodeType}
                    onChange={(e) => {
                      // Reset options when changing node type to prevent object/string conflicts
                      setNodeForm(prev => ({
                        ...prev,
                        nodeType: e.target.value,
                        options: [], // Reset options to empty array
                        extractVariable: '', // Reset extract variable
                        conditions: [] // Reset conditions
                      }));
                    }}
                    className="input-field"
                  >
                    <option value="message">Message</option>
                    <option value="question">Question</option>
                    <option value="action">Action</option>
                    <option value="condition">Condition</option>
                  </select>
                </div>

                {/* Message Type Selection */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-3">
                    Message Type
                  </label>
                  <div className="grid grid-cols-2 gap-3">
                    <button
                      type="button"
                      onClick={() => setNodeForm(prev => ({ ...prev, messageType: 'text', templateId: '' }))}
                      className={`p-3 border-2 rounded-lg text-left transition-colors ${
                        nodeForm.messageType === 'text'
                          ? 'border-blue-500 bg-blue-50 text-blue-700'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                    >
                      <ChatBubbleLeftRightIcon className="w-5 h-5 mb-2" />
                      <div className="font-medium">Text Message</div>
                      <div className="text-xs text-gray-500">Custom text with attachments</div>
                    </button>
                    <button
                      type="button"
                      onClick={() => setNodeForm(prev => ({ ...prev, messageType: 'template' }))}
                      className={`p-3 border-2 rounded-lg text-left transition-colors ${
                        nodeForm.messageType === 'template'
                          ? 'border-blue-500 bg-blue-50 text-blue-700'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                    >
                      <DocumentTextIcon className="w-5 h-5 mb-2" />
                      <div className="font-medium">Template</div>
                      <div className="text-xs text-gray-500">Use predefined template</div>
                    </button>
                  </div>
                </div>

                {/* Template Selection (only if template type is selected) */}
                {nodeForm.messageType === 'template' && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Select Template *
                    </label>
                    <select
                      value={nodeForm.templateId}
                      onChange={(e) => handleTemplateSelect(e.target.value)}
                      className="input-field"
                    >
                      <option value="">Choose a template...</option>
                      {(Array.isArray(templates) ? templates : []).map(template => {
                        // Ensure template is an object and has required properties
                        if (!template || typeof template !== 'object') {
                          return null;
                        }

                        const templateType = TEMPLATE_TYPES[template.type || 'text'];
                        const templateName = typeof template.name === 'string' ? template.name : String(template.name || 'Unnamed Template');
                        const typeName = typeof templateType?.name === 'string' ? templateType.name : String(template.type || 'Text');
                        const templateId = template.id || `template_${Math.random()}`;

                        return (
                          <option key={templateId} value={templateId}>
                            {templateName} ({typeName})
                          </option>
                        );
                      }).filter(Boolean)}
                    </select>
                  </div>
                )}

                {/* Message Content */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {nodeForm.messageType === 'template' ? 'Message Preview' : 'Message Content'} *
                  </label>
                  <textarea
                    value={nodeForm.message}
                    onChange={handleNodeFormMessageChange}
                    placeholder={nodeForm.messageType === 'template' ? 'Select a template to see preview...' : 'Enter the message for this node...'}
                    rows={4}
                    className="input-field resize-none"
                    readOnly={nodeForm.messageType === 'template'}
                  />
                </div>

                {/* Template Variables (only if template is selected and has variables) */}
                {nodeForm.messageType === 'template' && nodeForm.templateId && Object.keys(nodeForm.variables).length > 0 && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Template Variables
                    </label>
                    <div className="space-y-3">
                      {Object.keys(nodeForm.variables).map(variable => {
                        // Ensure variable is always a string
                        const safeVariable = typeof variable === 'string' ? variable : String(variable || '');
                        return (
                          <div key={safeVariable}>
                            <label className="block text-xs font-medium text-gray-600 mb-1">
                              {safeVariable}
                            </label>
                            <input
                              type="text"
                              value={typeof nodeForm.variables[safeVariable] === 'string' ? nodeForm.variables[safeVariable] : String(nodeForm.variables[safeVariable] || '')}
                              onChange={(e) => setNodeForm(prev => ({
                                ...prev,
                                variables: {
                                  ...prev.variables,
                                  [safeVariable]: e.target.value
                                }
                              }))}
                              placeholder={`Enter value for {{${safeVariable}}}`}
                              className="input-field"
                            />
                          </div>
                        );
                      })}
                    </div>
                  </div>
                )}

                {/* File Attachment (only for text messages) */}
                {nodeForm.messageType === 'text' && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Attachment (Optional)
                    </label>
                    <div className="space-y-3">
                      <div className="flex items-center space-x-4">
                        <select
                          value={nodeForm.attachmentType}
                          onChange={handleNodeFormAttachmentTypeChange}
                          className="input-field w-32"
                        >
                          <option value="image">Image</option>
                          <option value="video">Video</option>
                          <option value="audio">Audio</option>
                          <option value="document">Document</option>
                        </select>
                        <input
                          type="file"
                          onChange={handleNodeFormAttachmentFileChange}
                          accept={
                            nodeForm.attachmentType === 'image' ? 'image/*' :
                            nodeForm.attachmentType === 'video' ? 'video/*' :
                            nodeForm.attachmentType === 'audio' ? 'audio/*' :
                            '*/*'
                          }
                          className="input-field flex-1"
                        />
                      </div>
                      {nodeForm.attachmentFile && (
                        <div className="space-y-2">
                          <div className="flex items-center space-x-2 text-sm text-gray-600">
                            <PaperClipIcon className="w-4 h-4" />
                            <span>{typeof nodeForm.attachmentFile.name === 'string' ? nodeForm.attachmentFile.name : String(nodeForm.attachmentFile.name || 'Unknown file')}</span>
                            <button
                              onClick={() => setNodeForm(prev => ({ ...prev, attachmentFile: null }))}
                              className="text-red-500 hover:text-red-700"
                            >
                              <XMarkIcon className="w-4 h-4" />
                            </button>
                          </div>

                          {/* Warning for attachments with interactive buttons - Show for Question nodes */}
                          {nodeForm.nodeType === 'question' && (
                            <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                              <div className="flex items-start space-x-2">
                                <svg className="w-5 h-5 text-yellow-600 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                  <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                                </svg>
                                <div>
                                  <p className="text-sm font-medium text-yellow-800">
                                    Attachment + Interactive Buttons Limitation
                                  </p>
                                  <p className="text-xs text-yellow-700 mt-1">
                                    WhatsApp doesn't support interactive buttons with media attachments. The attachment will be sent, but buttons won't work. Consider using "Text List" instead of "Interactive Buttons" in the options below.
                                  </p>
                                </div>
                              </div>
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {nodeForm.nodeType === 'question' && (
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Options
                      </label>
                      <div className="space-y-2">
                        {(Array.isArray(nodeForm.options) ? nodeForm.options : []).map((option, index) => {
                          // Ensure option is always a string
                          let optionValue = '';
                          if (typeof option === 'string') {
                            optionValue = option;
                          } else if (typeof option === 'object' && option !== null) {
                            optionValue = String(option.display_text || option.title || option.name || option.text || option.value || '');
                          } else {
                            optionValue = String(option || '');
                          }

                          return (
                            <div key={index} className="flex items-center space-x-2">
                              <input
                                type="text"
                                value={optionValue}
                                onChange={(e) => updateOption(index, e.target.value)}
                                placeholder={`Option ${index + 1}`}
                                className="input-field flex-1"
                              />
                              <button
                                onClick={() => removeOption(index)}
                                className="p-2 text-red-500 hover:bg-red-50 rounded-lg"
                              >
                                <TrashIcon className="w-4 h-4" />
                              </button>
                            </div>
                          );
                        })}
                        <button
                          onClick={addOption}
                          className="btn-secondary w-full text-sm"
                        >
                          Add Option
                        </button>
                      </div>
                    </div>

                    {/* Interaction Type */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Display Options As
                      </label>
                      <select
                        value={nodeForm.interactionType || 'buttons'}
                        onChange={(e) => setNodeForm(prev => ({ ...prev, interactionType: e.target.value }))}
                        className="input-field"
                      >
                        <option value="buttons">Interactive Buttons (≤3 options)</option>
                        <option value="text">Text List</option>
                      </select>
                      <p className="text-xs text-gray-500 mt-1">
                        Interactive buttons provide better user experience but are limited to 3 options. Text list works for any number of options.
                      </p>

                      {/* Warning for buttons with attachments */}
                      {nodeForm.interactionType === 'buttons' && nodeForm.attachmentFile && (
                        <div className="mt-2 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                          <div className="flex items-start space-x-2">
                            <svg className="w-5 h-5 text-yellow-600 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                            </svg>
                            <div>
                              <p className="text-sm font-medium text-yellow-800">
                                Interactive Buttons + Attachments Limitation
                              </p>
                              <p className="text-xs text-yellow-700 mt-1">
                                WhatsApp doesn't support interactive buttons with media attachments. Consider using "Text List" instead, or remove the attachment to use interactive buttons.
                              </p>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>

                    {/* Variable Extraction */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Extract Variable (Optional)
                      </label>
                      <input
                        type="text"
                        value={nodeForm.extractVariable}
                        onChange={handleNodeFormExtractVariableChange}
                        placeholder="e.g., name, email, phone"
                        className="input-field"
                      />
                      <p className="text-xs text-gray-500 mt-1">
                        Variable name to store user's response. Use this variable in future messages to reference it.
                      </p>
                    </div>
                  </div>
                )}

                {/* Action Node Configuration */}
                {nodeForm.nodeType === 'action' && (
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Action Type
                      </label>
                      <select
                        value={nodeForm.actionType || 'webhook'}
                        onChange={(e) => setNodeForm(prev => ({ ...prev, actionType: e.target.value }))}
                        className="input-field"
                      >
                        <option value="webhook">Send Webhook</option>
                        <option value="email">Send Email</option>
                        <option value="save_data">Save Data</option>
                        <option value="api_call">API Call</option>
                        <option value="delay">Add Delay</option>
                      </select>
                    </div>

                    {nodeForm.actionType === 'webhook' && (
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Webhook URL
                        </label>
                        <input
                          type="url"
                          value={nodeForm.webhookUrl || ''}
                          onChange={(e) => setNodeForm(prev => ({ ...prev, webhookUrl: e.target.value }))}
                          placeholder="https://your-webhook-url.com/endpoint"
                          className="input-field"
                        />
                        <p className="text-xs text-gray-500 mt-1">
                          URL to send conversation data to when this action is triggered.
                        </p>
                      </div>
                    )}

                    {nodeForm.actionType === 'delay' && (
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Delay Duration (seconds)
                        </label>
                        <input
                          type="number"
                          min="1"
                          max="300"
                          value={nodeForm.delaySeconds || 5}
                          onChange={(e) => setNodeForm(prev => ({ ...prev, delaySeconds: parseInt(e.target.value) || 5 }))}
                          className="input-field"
                        />
                        <p className="text-xs text-gray-500 mt-1">
                          How long to wait before proceeding to the next node (1-300 seconds).
                        </p>
                      </div>
                    )}

                    {nodeForm.actionType === 'email' && (
                      <div className="space-y-3">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Email Recipients
                          </label>
                          <input
                            type="email"
                            value={nodeForm.emailRecipients || ''}
                            onChange={(e) => setNodeForm(prev => ({ ...prev, emailRecipients: e.target.value }))}
                            placeholder="<EMAIL>, <EMAIL>"
                            className="input-field"
                          />
                          <p className="text-xs text-gray-500 mt-1">
                            Comma-separated email addresses to send notification to.
                          </p>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Email Subject
                          </label>
                          <input
                            type="text"
                            value={nodeForm.emailSubject || ''}
                            onChange={(e) => setNodeForm(prev => ({ ...prev, emailSubject: e.target.value }))}
                            placeholder="New chatbot conversation data"
                            className="input-field"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Email Body
                          </label>
                          <textarea
                            value={nodeForm.emailBody || ''}
                            onChange={(e) => setNodeForm(prev => ({ ...prev, emailBody: e.target.value }))}
                            placeholder="Hello {{user_name}}, thank you for your interest in {{selected_product}}..."
                            className="input-field h-32"
                            rows={4}
                          />
                          <p className="text-xs text-gray-500 mt-1">
                            Use {`{{variable_name}}`} to insert dynamic data from conversation.
                          </p>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Email Template (Optional)
                          </label>
                          <select
                            value={nodeForm.emailTemplate || ''}
                            onChange={(e) => setNodeForm(prev => ({ ...prev, emailTemplate: e.target.value }))}
                            className="input-field"
                          >
                            <option value="">No Template</option>
                            <option value="welcome">Welcome Email</option>
                            <option value="followup">Follow-up Email</option>
                            <option value="notification">Notification Email</option>
                          </select>
                        </div>
                      </div>
                    )}

                    {nodeForm.actionType === 'api_call' && (
                      <div className="space-y-3">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            API Endpoint
                          </label>
                          <input
                            type="url"
                            value={nodeForm.apiEndpoint || ''}
                            onChange={(e) => setNodeForm(prev => ({ ...prev, apiEndpoint: e.target.value }))}
                            placeholder="https://api.example.com/endpoint"
                            className="input-field"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            HTTP Method
                          </label>
                          <select
                            value={nodeForm.apiMethod || 'POST'}
                            onChange={(e) => setNodeForm(prev => ({ ...prev, apiMethod: e.target.value }))}
                            className="input-field"
                          >
                            <option value="GET">GET</option>
                            <option value="POST">POST</option>
                            <option value="PUT">PUT</option>
                            <option value="PATCH">PATCH</option>
                            <option value="DELETE">DELETE</option>
                          </select>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Headers (JSON)
                          </label>
                          <textarea
                            value={nodeForm.apiHeaders || ''}
                            onChange={(e) => setNodeForm(prev => ({ ...prev, apiHeaders: e.target.value }))}
                            placeholder='{"Authorization": "Bearer token", "Content-Type": "application/json"}'
                            className="input-field h-20"
                            rows={2}
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Request Body (JSON)
                          </label>
                          <textarea
                            value={nodeForm.apiBody || ''}
                            onChange={(e) => setNodeForm(prev => ({ ...prev, apiBody: e.target.value }))}
                            placeholder='{"user_name": "{{user_name}}", "selected_product": "{{selected_product}}"}'
                            className="input-field h-24"
                            rows={3}
                          />
                          <p className="text-xs text-gray-500 mt-1">
                            Use {`{{variable_name}}`} to insert dynamic data from conversation.
                          </p>
                        </div>
                      </div>
                    )}

                    {nodeForm.actionType === 'save_data' && (
                      <div className="space-y-3">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Data Fields to Save
                          </label>
                          <textarea
                            value={Array.isArray(nodeForm.saveDataFields) ? nodeForm.saveDataFields.join('\n') : ''}
                            onChange={(e) => setNodeForm(prev => ({
                              ...prev,
                              saveDataFields: e.target.value.split('\n').filter(field => field.trim())
                            }))}
                            placeholder="user_name&#10;user_email&#10;selected_product&#10;phone_number"
                            className="input-field h-24"
                            rows={4}
                          />
                          <p className="text-xs text-gray-500 mt-1">
                            Enter one field name per line. These will be saved to the database.
                          </p>
                        </div>
                      </div>
                    )}

                    {/* True/False Path Configuration for all condition types except random */}
                    {nodeForm.conditionType !== 'random' && (
                      <div className="space-y-3 border-t pt-4">
                        <h4 className="text-sm font-medium text-gray-700">Path Configuration</h4>
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <label className="block text-sm font-medium text-green-700 mb-2">
                              ✅ True Path (Condition Met)
                            </label>
                            <select
                              value={nodeForm.truePath || ''}
                              onChange={(e) => setNodeForm(prev => ({ ...prev, truePath: e.target.value || null }))}
                              className="input-field border-green-300 focus:border-green-500"
                            >
                              <option value="">Select Next Node</option>
                              {flowForm.nodes.map((node, nodeIndex) => (
                                <option key={nodeIndex} value={nodeIndex + 1}>
                                  {nodeIndex + 1}. {node.name}
                                </option>
                              ))}
                            </select>
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-red-700 mb-2">
                              ❌ False Path (Condition Not Met)
                            </label>
                            <select
                              value={nodeForm.falsePath || ''}
                              onChange={(e) => setNodeForm(prev => ({ ...prev, falsePath: e.target.value || null }))}
                              className="input-field border-red-300 focus:border-red-500"
                            >
                              <option value="">Select Next Node</option>
                              {flowForm.nodes.map((node, nodeIndex) => (
                                <option key={nodeIndex} value={nodeIndex + 1}>
                                  {nodeIndex + 1}. {node.name}
                                </option>
                              ))}
                            </select>
                          </div>
                        </div>
                        <p className="text-xs text-gray-500">
                          Choose different paths based on whether the condition is true or false.
                        </p>
                      </div>
                    )}
                  </div>
                )}

                {/* Condition Node Configuration */}
                {nodeForm.nodeType === 'condition' && (
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Condition Type
                      </label>
                      <select
                        value={nodeForm.conditionType || 'user_response'}
                        onChange={(e) => setNodeForm(prev => ({ ...prev, conditionType: e.target.value }))}
                        className="input-field"
                      >
                        <option value="user_response">Based on User Response</option>
                        <option value="variable_value">Based on Variable Value</option>
                        <option value="time_based">Time-based Condition</option>
                        <option value="random">Random Selection</option>
                      </select>
                    </div>

                    {nodeForm.conditionType === 'user_response' && (
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Response Conditions
                        </label>
                        <div className="space-y-2">
                          {(nodeForm.responseConditions || []).map((condition, index) => (
                            <div key={index} className="flex space-x-2">
                              <input
                                type="text"
                                value={condition.keyword || ''}
                                onChange={(e) => {
                                  const updated = [...(nodeForm.responseConditions || [])];
                                  updated[index] = { ...updated[index], keyword: e.target.value };
                                  setNodeForm(prev => ({ ...prev, responseConditions: updated }));
                                }}
                                placeholder="Keyword to match"
                                className="input-field flex-1"
                              />
                              <select
                                value={condition.nextNode || ''}
                                onChange={(e) => {
                                  const updated = [...(nodeForm.responseConditions || [])];
                                  updated[index] = { ...updated[index], nextNode: e.target.value };
                                  setNodeForm(prev => ({ ...prev, responseConditions: updated }));
                                }}
                                className="input-field w-40"
                              >
                                <option value="">Select Next Node</option>
                                {flowForm.nodes.map((node, nodeIndex) => (
                                  <option key={nodeIndex} value={nodeIndex + 1}>
                                    {nodeIndex + 1}. {node.name}
                                  </option>
                                ))}
                              </select>
                              <button
                                type="button"
                                onClick={() => {
                                  const updated = (nodeForm.responseConditions || []).filter((_, i) => i !== index);
                                  setNodeForm(prev => ({ ...prev, responseConditions: updated }));
                                }}
                                className="px-3 py-2 text-red-600 hover:text-red-800"
                              >
                                ×
                              </button>
                            </div>
                          ))}
                          <button
                            type="button"
                            onClick={() => {
                              const updated = [...(nodeForm.responseConditions || []), { keyword: '', nextNode: '' }];
                              setNodeForm(prev => ({ ...prev, responseConditions: updated }));
                            }}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                          >
                            Add Condition
                          </button>
                        </div>
                        <p className="text-xs text-gray-500 mt-1">
                          Define keywords and which node to go to when user response contains that keyword.
                        </p>
                      </div>
                    )}

                    {nodeForm.conditionType === 'variable_value' && (
                      <div className="space-y-3">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Variable Name
                          </label>
                          <input
                            type="text"
                            value={nodeForm.conditionVariable || ''}
                            onChange={(e) => setNodeForm(prev => ({ ...prev, conditionVariable: e.target.value }))}
                            placeholder="e.g., user_name, email"
                            className="input-field"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Condition
                          </label>
                          <select
                            value={nodeForm.conditionOperator || 'equals'}
                            onChange={(e) => setNodeForm(prev => ({ ...prev, conditionOperator: e.target.value }))}
                            className="input-field"
                          >
                            <option value="equals">Equals</option>
                            <option value="contains">Contains</option>
                            <option value="not_equals">Not Equals</option>
                            <option value="is_empty">Is Empty</option>
                            <option value="is_not_empty">Is Not Empty</option>
                          </select>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Expected Value
                          </label>
                          <input
                            type="text"
                            value={nodeForm.conditionValue || ''}
                            onChange={(e) => setNodeForm(prev => ({ ...prev, conditionValue: e.target.value }))}
                            placeholder="Value to compare against"
                            className="input-field"
                          />
                        </div>
                      </div>
                    )}

                    {nodeForm.conditionType === 'random' && (
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Random Paths
                        </label>
                        <div className="space-y-2">
                          {(nodeForm.randomPaths || []).map((path, index) => (
                            <div key={index} className="flex space-x-2">
                              <input
                                type="number"
                                min="1"
                                max="100"
                                value={path.weight || 50}
                                onChange={(e) => {
                                  const updated = [...(nodeForm.randomPaths || [])];
                                  updated[index] = { ...updated[index], weight: parseInt(e.target.value) || 50 };
                                  setNodeForm(prev => ({ ...prev, randomPaths: updated }));
                                }}
                                placeholder="Weight %"
                                className="input-field w-20"
                              />
                              <select
                                value={path.nextNode || ''}
                                onChange={(e) => {
                                  const updated = [...(nodeForm.randomPaths || [])];
                                  updated[index] = { ...updated[index], nextNode: e.target.value };
                                  setNodeForm(prev => ({ ...prev, randomPaths: updated }));
                                }}
                                className="input-field flex-1"
                              >
                                <option value="">Select Next Node</option>
                                {flowForm.nodes.map((node, nodeIndex) => (
                                  <option key={nodeIndex} value={nodeIndex + 1}>
                                    {nodeIndex + 1}. {node.name}
                                  </option>
                                ))}
                              </select>
                              <button
                                type="button"
                                onClick={() => {
                                  const updated = (nodeForm.randomPaths || []).filter((_, i) => i !== index);
                                  setNodeForm(prev => ({ ...prev, randomPaths: updated }));
                                }}
                                className="px-3 py-2 text-red-600 hover:text-red-800"
                              >
                                ×
                              </button>
                            </div>
                          ))}
                          <button
                            type="button"
                            onClick={() => {
                              const updated = [...(nodeForm.randomPaths || []), { weight: 50, nextNode: '' }];
                              setNodeForm(prev => ({ ...prev, randomPaths: updated }));
                            }}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                          >
                            Add Random Path
                          </button>
                        </div>
                        <p className="text-xs text-gray-500 mt-1">
                          Define multiple paths with weights for random selection.
                        </p>
                      </div>
                    )}
                  </div>
                )}

                {/* Next Node Selection */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Next Node (Optional)
                  </label>
                  <select
                    value={nodeForm.nextNodeId || ''}
                    onChange={handleNodeFormNextNodeChange}
                    className="input-field"
                  >
                    <option value="">Auto (Next in sequence)</option>
                    {flowForm.nodes.map((node, index) => {
                      // Use position-based indexing (index + 1) for next node selection
                      const nodePosition = index + 1;
                      return (
                        <option key={index} value={nodePosition}>
                          {nodePosition}. {typeof node.name === 'string' ? node.name : 'Unnamed Node'}
                        </option>
                      );
                    })}
                  </select>
                  <p className="text-xs text-gray-500 mt-1">
                    Specify which node to go to next, or leave empty for sequential flow.
                  </p>
                </div>


              </div>
            </div>

            <div className="p-6 border-t border-gray-200 flex-shrink-0">
              <div className="flex space-x-3">
                <button
                  onClick={() => {
                    setShowNodeModal(false);
                    resetNodeForm();
                    setEditingNodeIndex(-1);
                  }}
                  className="btn-secondary flex-1"
                >
                  Cancel
                </button>
                <button
                  onClick={handleAddNode}
                  className="btn-primary flex-1"
                  disabled={!nodeForm.name.trim() || !nodeForm.message.trim()}
                >
                  {editingNodeIndex >= 0 ? 'Update Node' : 'Add Node'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* View Flow Modal */}
      {showFlowModal && selectedFlow && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-gray-900">Flow Details</h3>
                <button
                  onClick={() => setShowFlowModal(false)}
                  className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg"
                >
                  <XMarkIcon className="w-5 h-5" />
                </button>
              </div>

              <div className="space-y-6">
                {/* Flow Info */}
                <div>
                  <h4 className="font-medium text-gray-900 mb-3">{typeof selectedFlow.name === 'string' ? selectedFlow.name : String(selectedFlow.name || 'Unnamed Flow')}</h4>
                  {selectedFlow.description && (
                    <p className="text-sm text-gray-600 mb-4">{selectedFlow.description}</p>
                  )}
                  
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-gray-600">Status:</span>
                      <span className={`ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                        selectedFlow.is_active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                      }`}>
                        {selectedFlow.is_active ? 'Active' : 'Inactive'}
                      </span>
                    </div>
                    <div>
                      <span className="text-gray-600">Device:</span>
                      <span className="ml-2 text-gray-900">{typeof selectedFlow.device_name === 'string' ? selectedFlow.device_name : String(selectedFlow.device_name || 'Unknown Device')}</span>
                    </div>
                    <div>
                      <span className="text-gray-600">Nodes:</span>
                      <span className="ml-2 text-gray-900">{selectedFlow.node_count || 0}</span>
                    </div>
                    <div>
                      <span className="text-gray-600">Conversations:</span>
                      <span className="ml-2 text-gray-900">{selectedFlow.conversation_count || 0}</span>
                    </div>
                  </div>
                </div>

                {/* Trigger Keywords */}
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Trigger Keywords</h4>
                  <div className="flex flex-wrap gap-2">
                    {(typeof selectedFlow.trigger_keywords === 'string' ? selectedFlow.trigger_keywords : String(selectedFlow.trigger_keywords || '')).split(',').map((keyword, index) => (
                      <span
                        key={index}
                        className="inline-flex items-center px-2 py-1 rounded text-xs bg-blue-100 text-blue-800"
                      >
                        {keyword.trim()}
                      </span>
                    ))}
                  </div>
                </div>

                {/* Timestamps */}
                <div className="text-sm text-gray-500">
                  <p>Created: {new Date(selectedFlow.created_at).toLocaleString()}</p>
                  <p>Updated: {new Date(selectedFlow.updated_at).toLocaleString()}</p>
                  {selectedFlow.last_triggered && (
                    <p>Last triggered: {new Date(selectedFlow.last_triggered).toLocaleString()}</p>
                  )}
                </div>
              </div>

              <div className="flex justify-end mt-6">
                <button
                  onClick={() => setShowFlowModal(false)}
                  className="btn-secondary"
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Chatbot; 