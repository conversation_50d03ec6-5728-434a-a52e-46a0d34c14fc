const initSqlJs = require('sql.js');
const fs = require('fs');
const path = require('path');

async function verifyFix() {
  console.log('🧪 Verifying the chatbot fix...');
  
  const dbPath = path.join(process.env.APPDATA, 'Electron', 'leadwave.db');
  const SQL = await initSqlJs();
  const filebuffer = fs.readFileSync(dbPath);
  const db = new SQL.Database(filebuffer);
  
  try {
    // Test the status column
    console.log('\n✅ Testing status column...');
    const statusTest = db.exec('SELECT id, status FROM chatbot_conversations LIMIT 3');
    
    if (statusTest.length > 0 && statusTest[0].values.length > 0) {
      console.log('✅ Status column works!');
      statusTest[0].values.forEach((row, index) => {
        console.log(`   Conversation ${row[0]}: status = "${row[1]}"`);
      });
    } else {
      console.log('📭 No conversations found (table is empty)');
    }
    
    // Test active conversation query
    console.log('\n✅ Testing active conversation query...');
    const activeTest = db.exec('SELECT COUNT(*) as count FROM chatbot_conversations WHERE status = "active"');
    
    if (activeTest.length > 0) {
      const activeCount = activeTest[0].values[0][0];
      console.log(`✅ Active conversations query works! Found ${activeCount} active conversations`);
    }
    
    // Show the flow and nodes are ready
    console.log('\n🤖 Verifying flow setup...');
    const flowTest = db.exec('SELECT id, name, trigger_keywords FROM chatbot_flows WHERE is_active = 1');
    
    if (flowTest.length > 0 && flowTest[0].values.length > 0) {
      flowTest[0].values.forEach(row => {
        const [id, name, keywords] = row;
        console.log(`✅ Flow "${name}" (ID: ${id}) with keywords: "${keywords}"`);
        
        // Check nodes for this flow
        const nodeTest = db.exec('SELECT COUNT(*) as count FROM chatbot_nodes WHERE flow_id = ?', [id]);
        if (nodeTest.length > 0) {
          const nodeCount = nodeTest[0].values[0][0];
          console.log(`   Has ${nodeCount} node(s) ✅`);
        }
      });
    }
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    db.close();
  }
  
  console.log('\n🎉 VERIFICATION COMPLETE!');
  console.log('\n📱 Next steps:');
  console.log('   1. Make sure your LeadWave application is running');
  console.log('   2. Send "PHP" from WhatsApp');
  console.log('   3. The chatbot flow should now start properly!');
  console.log('\n🔍 If it still doesn\'t work, check the application logs for any other errors.');
}

verifyFix().catch(console.error);
