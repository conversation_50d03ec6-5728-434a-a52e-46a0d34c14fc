const DatabaseService = require('./src/services/database.service');
const MessageProcessor = require('./src/services/message-processor.service');

async function debugPhpMessages() {
  console.log('🔍 Debugging PHP message processing...');
  
  const databaseService = new DatabaseService();
  const messageProcessor = new MessageProcessor();
  
  // Initialize services
  await databaseService.initialize();
  
  // Get the session ID from recent messages
  const recentMessage = await databaseService.get(`
    SELECT session_id FROM message_history 
    WHERE content = 'PHP' AND direction = 'incoming'
    ORDER BY id DESC LIMIT 1
  `);
  
  if (!recentMessage.success || !recentMessage.data) {
    console.log('❌ No recent PHP message found');
    return;
  }
  
  const sessionId = recentMessage.data.session_id;
  console.log(`📱 Session ID: ${sessionId}`);
  
  // Check active conversations
  console.log('\n🔍 Checking active conversations...');
  const activeConversations = await databaseService.all(`
    SELECT * FROM chatbot_conversations 
    WHERE session_id = ? AND status = 'active'
    ORDER BY id DESC
  `, [sessionId]);
  
  if (activeConversations.success && activeConversations.data.length > 0) {
    console.log(`⚠️  Found ${activeConversations.data.length} active conversation(s):`);
    activeConversations.data.forEach((conv, index) => {
      console.log(`   ${index + 1}. ID: ${conv.id}, Flow: ${conv.flow_id}, Started: ${conv.created_at}`);
    });
    console.log('   🚨 This might be blocking new flow triggers!');
  } else {
    console.log('✅ No active conversations found');
  }
  
  // Check flows
  console.log('\n🔍 Checking chatbot flows...');
  const flows = await databaseService.all(`
    SELECT * FROM chatbot_flows 
    WHERE session_id = ? AND is_active = 1
  `, [sessionId]);
  
  if (flows.success && flows.data.length > 0) {
    console.log(`✅ Found ${flows.data.length} active flow(s):`);
    flows.data.forEach((flow, index) => {
      const keywords = flow.trigger_keywords.toLowerCase().split(',').map(k => k.trim());
      console.log(`   ${index + 1}. "${flow.name}": keywords [${keywords.join(', ')}]`);
      
      // Test keyword matching
      const testMessage = 'php';
      const hasMatch = keywords.some(keyword => 
        messageProcessor.matchesKeyword(testMessage, keyword, 'contains')
      );
      console.log(`      Test "${testMessage}" matches: ${hasMatch ? '✅ YES' : '❌ NO'}`);
    });
  } else {
    console.log('❌ No active flows found for this session');
  }
  
  // Simulate message processing
  console.log('\n🧪 Simulating "PHP" message processing...');
  
  const mockMessage = {
    key: {
      id: 'test_' + Date.now(),
      remoteJid: '<EMAIL>', // Your phone number
      fromMe: false
    },
    messageTimestamp: Math.floor(Date.now() / 1000),
    message: {
      conversation: 'PHP'
    }
  };
  
  try {
    // Parse the message
    const parsed = messageProcessor.parseMessage(mockMessage);
    console.log(`📝 Parsed message:`, {
      text: parsed.text,
      type: parsed.type,
      from: parsed.from
    });
    
    // Check if it would trigger chatbot
    console.log('\n🤖 Testing chatbot trigger logic...');
    
    // This is the exact logic from event.service.js
    const messageText = parsed.text.toLowerCase().trim();
    console.log(`📝 Message text for matching: "${messageText}"`);
    
    if (flows.success && flows.data.length > 0) {
      for (const flow of flows.data) {
        const keywords = flow.trigger_keywords.toLowerCase().split(',').map(k => k.trim());
        console.log(`\n🔍 Testing flow "${flow.name}":`);
        console.log(`   Keywords: [${keywords.join(', ')}]`);
        
        const hasMatch = keywords.some(keyword => {
          const match = messageProcessor.matchesKeyword(messageText, keyword, 'contains');
          console.log(`   "${keyword}" vs "${messageText}": ${match ? '✅ MATCH' : '❌ NO MATCH'}`);
          return match;
        });
        
        console.log(`   Overall result: ${hasMatch ? '🎯 WOULD TRIGGER' : '❌ WOULD NOT TRIGGER'}`);
        
        if (hasMatch) {
          console.log(`   🚀 This should start the chatbot flow!`);
        }
      }
    }
    
  } catch (error) {
    console.error('❌ Error during simulation:', error);
  }
  
  // Check cooldowns
  console.log('\n🔍 Checking flow cooldowns...');
  const cooldowns = await databaseService.all(`
    SELECT * FROM chatbot_flow_cooldowns 
    WHERE user_phone = ?
  `, ['917261902348']);
  
  if (cooldowns.success && cooldowns.data.length > 0) {
    console.log(`⏰ Found ${cooldowns.data.length} cooldown record(s):`);
    cooldowns.data.forEach((cooldown, index) => {
      const lastTriggered = new Date(cooldown.last_triggered_at);
      const now = new Date();
      const minutesAgo = Math.floor((now - lastTriggered) / (1000 * 60));
      console.log(`   ${index + 1}. Flow ${cooldown.flow_id}: last triggered ${minutesAgo} minutes ago`);
    });
  } else {
    console.log('✅ No cooldown records found');
  }
  
  console.log('\n💡 Summary:');
  console.log('   - If keyword matching shows ✅ MATCH but flow doesn\'t trigger:');
  console.log('     1. Check for active conversations blocking new triggers');
  console.log('     2. Check application logs for errors');
  console.log('     3. Verify the application is actually processing messages');
  console.log('     4. Check if session IDs match between messages and flows');
}

debugPhpMessages().catch(console.error);
