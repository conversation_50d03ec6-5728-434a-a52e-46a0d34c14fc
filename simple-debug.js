const initSqlJs = require('sql.js');
const fs = require('fs');
const path = require('path');

async function simpleDebug() {
  console.log('🔍 Simple database debug...');
  
  const dbPath = path.join(process.env.APPDATA, 'Electron', 'leadwave.db');
  
  if (!fs.existsSync(dbPath)) {
    console.log('❌ Database not found');
    return;
  }
  
  const SQL = await initSqlJs();
  const filebuffer = fs.readFileSync(dbPath);
  const db = new SQL.Database(filebuffer);
  
  try {
    // 1. Check the session details
    console.log('\n📱 WHATSAPP SESSIONS:');
    const sessions = db.exec('SELECT * FROM whatsapp_sessions WHERE status = "connected"');
    if (sessions.length > 0) {
      const columns = sessions[0].columns;
      sessions[0].values.forEach((row, index) => {
        const sessionData = {};
        row.forEach((value, colIndex) => {
          sessionData[columns[colIndex]] = value;
        });
        console.log(`  Session ${index + 1}: ID=${sessionData.id}, Name="${sessionData.name}", Phone=${sessionData.phone_number}`);
      });
    }
    
    // 2. Check the chatbot flows
    console.log('\n🤖 CHATBOT FLOWS:');
    const flows = db.exec('SELECT * FROM chatbot_flows WHERE is_active = 1');
    if (flows.length > 0) {
      const columns = flows[0].columns;
      flows[0].values.forEach((row, index) => {
        const flowData = {};
        row.forEach((value, colIndex) => {
          flowData[columns[colIndex]] = value;
        });
        console.log(`  Flow ${index + 1}: ID=${flowData.id}, Name="${flowData.name}", Session=${flowData.session_id}, Keywords="${flowData.trigger_keywords}"`);
      });
    }
    
    // 3. Check recent messages
    console.log('\n📨 RECENT MESSAGES (last 5):');
    const messages = db.exec('SELECT * FROM message_history ORDER BY id DESC LIMIT 5');
    if (messages.length > 0) {
      const columns = messages[0].columns;
      messages[0].values.forEach((row, index) => {
        const msgData = {};
        row.forEach((value, colIndex) => {
          msgData[columns[colIndex]] = value;
        });
        console.log(`  Message ${index + 1}: ID=${msgData.id}, Session=${msgData.session_id}, Type=${msgData.message_type}, Direction=${msgData.direction}, Content="${msgData.content}"`);
      });
    }
    
    // 4. Check chatbot conversations
    console.log('\n💬 CHATBOT CONVERSATIONS:');
    const conversations = db.exec('SELECT * FROM chatbot_conversations ORDER BY id DESC LIMIT 5');
    if (conversations.length > 0) {
      const columns = conversations[0].columns;
      conversations[0].values.forEach((row, index) => {
        const convData = {};
        row.forEach((value, colIndex) => {
          convData[columns[colIndex]] = value;
        });
        console.log(`  Conversation ${index + 1}: ID=${convData.id}, Session=${convData.session_id}, Flow=${convData.flow_id}, User=${convData.user_phone}, Active=${convData.is_active}`);
      });
    } else {
      console.log('  No chatbot conversations found');
    }
    
    // 5. Check chatbot nodes
    console.log('\n🔗 CHATBOT NODES:');
    const nodes = db.exec('SELECT * FROM chatbot_nodes ORDER BY flow_id, position');
    if (nodes.length > 0) {
      const columns = nodes[0].columns;
      nodes[0].values.forEach((row, index) => {
        const nodeData = {};
        row.forEach((value, colIndex) => {
          nodeData[columns[colIndex]] = value;
        });
        console.log(`  Node ${index + 1}: ID=${nodeData.id}, Flow=${nodeData.flow_id}, Position=${nodeData.position}, Type=${nodeData.node_type}, Content="${nodeData.content}"`);
      });
    } else {
      console.log('  No chatbot nodes found');
    }
    
    // 6. Check if session IDs match
    console.log('\n🔍 SESSION ID ANALYSIS:');
    const sessionIds = new Set();
    const flowSessionIds = new Set();
    const messageSessionIds = new Set();
    
    if (sessions.length > 0) {
      sessions[0].values.forEach(row => {
        sessionIds.add(row[0]); // Assuming ID is first column
      });
    }
    
    if (flows.length > 0) {
      flows[0].values.forEach(row => {
        const sessionIdIndex = flows[0].columns.indexOf('session_id');
        if (sessionIdIndex >= 0) {
          flowSessionIds.add(row[sessionIdIndex]);
        }
      });
    }
    
    if (messages.length > 0) {
      messages[0].values.forEach(row => {
        const sessionIdIndex = messages[0].columns.indexOf('session_id');
        if (sessionIdIndex >= 0) {
          messageSessionIds.add(row[sessionIdIndex]);
        }
      });
    }
    
    console.log(`  Active session IDs: [${Array.from(sessionIds).join(', ')}]`);
    console.log(`  Flow session IDs: [${Array.from(flowSessionIds).join(', ')}]`);
    console.log(`  Message session IDs: [${Array.from(messageSessionIds).join(', ')}]`);
    
    // Check for mismatches
    const sessionIdArray = Array.from(sessionIds);
    const flowSessionIdArray = Array.from(flowSessionIds);
    
    if (sessionIdArray.length > 0 && flowSessionIdArray.length > 0) {
      const mismatch = !sessionIdArray.some(id => flowSessionIdArray.includes(id));
      if (mismatch) {
        console.log('  ⚠️  SESSION ID MISMATCH DETECTED!');
        console.log('  This is likely why the chatbot flow is not triggering.');
        console.log('  The flow is configured for a different session ID than the active WhatsApp session.');
      } else {
        console.log('  ✅ Session IDs match correctly');
      }
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    db.close();
  }
}

simpleDebug().catch(console.error);
