const DatabaseService = require('./src/services/database.service');

async function debugListSelection() {
  console.log('🔍 Starting List Selection Debug...');
  
  const db = new DatabaseService();
  await db.initialize();
  
  console.log('📋 Checking existing chatbot flows...');
  const flows = await db.all('SELECT * FROM chatbot_flows WHERE is_active = 1');
  if (flows.success && flows.data) {
    console.log('Available flows:');
    flows.data.forEach(flow => {
      const keywords = flow.trigger_keywords ? flow.trigger_keywords.split(',').map(k => k.trim().toLowerCase()) : [];
      console.log(`  - "${flow.name}": keywords [${keywords.join(', ')}]`);
    });
  } else {
    console.log('No active flows found');
  }
  
  console.log('\n📱 Checking recent messages...');
  const recentMessages = await db.all(`
    SELECT * FROM messages 
    WHERE created_at > datetime('now', '-10 minutes')
    ORDER BY created_at DESC 
    LIMIT 10
  `);
  
  if (recentMessages.success && recentMessages.data) {
    console.log('Recent messages:');
    recentMessages.data.forEach(msg => {
      console.log(`  - ${msg.created_at}: ${msg.sender_phone} -> "${msg.content}" (type: ${msg.message_type})`);
    });
  } else {
    console.log('No recent messages found');
  }
  
  console.log('\n🤖 Checking recent chatbot conversations...');
  const recentConversations = await db.all(`
    SELECT * FROM chatbot_conversations 
    WHERE created_at > datetime('now', '-10 minutes')
    ORDER BY created_at DESC 
    LIMIT 5
  `);
  
  if (recentConversations.success && recentConversations.data) {
    console.log('Recent chatbot conversations:');
    recentConversations.data.forEach(conv => {
      console.log(`  - ${conv.created_at}: User ${conv.user_phone}, Flow ${conv.flow_id}, Active: ${conv.is_active}`);
    });
  } else {
    console.log('No recent chatbot conversations found');
  }
  
  console.log('\n⏰ Checking cooldown records...');
  const cooldowns = await db.all(`
    SELECT * FROM chatbot_cooldowns 
    WHERE created_at > datetime('now', '-1 hour')
    ORDER BY created_at DESC 
    LIMIT 5
  `);
  
  if (cooldowns.success && cooldowns.data) {
    console.log('Recent cooldown records:');
    cooldowns.data.forEach(cd => {
      console.log(`  - ${cd.created_at}: User ${cd.user_phone}, Flow ${cd.flow_id}`);
    });
  } else {
    console.log('No recent cooldown records found');
  }
  
  console.log('\n📊 Now monitoring for new messages...');
  console.log('🔄 Checking every 2 seconds for new activity...');
  console.log('🛑 Press Ctrl+C to stop monitoring');
  
  let lastMessageId = 0;
  let lastConversationId = 0;
  
  const monitor = setInterval(async () => {
    // Check for new messages
    const newMessages = await db.all(`
      SELECT * FROM messages 
      WHERE id > ? 
      ORDER BY id ASC
    `, [lastMessageId]);
    
    if (newMessages.success && newMessages.data && newMessages.data.length > 0) {
      console.log('\n🆕 NEW MESSAGES DETECTED:');
      newMessages.data.forEach(msg => {
        console.log(`  📨 ${msg.created_at}: ${msg.sender_phone} -> "${msg.content}" (type: ${msg.message_type})`);
        lastMessageId = Math.max(lastMessageId, msg.id);
      });
    }
    
    // Check for new conversations
    const newConversations = await db.all(`
      SELECT * FROM chatbot_conversations 
      WHERE id > ? 
      ORDER BY id ASC
    `, [lastConversationId]);
    
    if (newConversations.success && newConversations.data && newConversations.data.length > 0) {
      console.log('\n🤖 NEW CHATBOT CONVERSATIONS:');
      newConversations.data.forEach(conv => {
        console.log(`  🗣️ ${conv.created_at}: User ${conv.user_phone}, Flow ${conv.flow_id}, Active: ${conv.is_active}`);
        lastConversationId = Math.max(lastConversationId, conv.id);
      });
    }
  }, 2000);
  
  // Handle Ctrl+C
  process.on('SIGINT', () => {
    console.log('\n\n🛑 Stopping monitor...');
    clearInterval(monitor);
    process.exit(0);
  });
}

debugListSelection().catch(console.error);
