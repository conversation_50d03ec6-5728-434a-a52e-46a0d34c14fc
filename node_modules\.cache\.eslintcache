[{"D:\\My Projects\\LeadWave\\src\\index.js": "1", "D:\\My Projects\\LeadWave\\src\\App.js": "2", "D:\\My Projects\\LeadWave\\src\\contexts\\NotificationContext.js": "3", "D:\\My Projects\\LeadWave\\src\\contexts\\ThemeContext.js": "4", "D:\\My Projects\\LeadWave\\src\\contexts\\LicenseContext.js": "5", "D:\\My Projects\\LeadWave\\src\\contexts\\I18nContext.js": "6", "D:\\My Projects\\LeadWave\\src\\components\\modules\\Dashboard.js": "7", "D:\\My Projects\\LeadWave\\src\\components\\modules\\Devices.js": "8", "D:\\My Projects\\LeadWave\\src\\components\\modules\\AutoReply.js": "9", "D:\\My Projects\\LeadWave\\src\\components\\modules\\SingleMessage.js": "10", "D:\\My Projects\\LeadWave\\src\\components\\modules\\BulkMessages.js": "11", "D:\\My Projects\\LeadWave\\src\\components\\modules\\AIChatbot.js": "12", "D:\\My Projects\\LeadWave\\src\\components\\modules\\LiveChat.js": "13", "D:\\My Projects\\LeadWave\\src\\components\\modules\\Templates.js": "14", "D:\\My Projects\\LeadWave\\src\\components\\modules\\Chatbot.js": "15", "D:\\My Projects\\LeadWave\\src\\components\\modules\\Settings.js": "16", "D:\\My Projects\\LeadWave\\src\\components\\modules\\GroupGrabber.js": "17", "D:\\My Projects\\LeadWave\\src\\components\\modules\\Contacts.js": "18", "D:\\My Projects\\LeadWave\\src\\components\\layout\\Sidebar.js": "19", "D:\\My Projects\\LeadWave\\src\\components\\layout\\Header.js": "20", "D:\\My Projects\\LeadWave\\src\\components\\modules\\CallResponder.js": "21", "D:\\My Projects\\LeadWave\\src\\components\\modules\\Reports.js": "22", "D:\\My Projects\\LeadWave\\src\\components\\common\\NotificationToast.js": "23", "D:\\My Projects\\LeadWave\\src\\components\\ui\\LoadingScreen.js": "24", "D:\\My Projects\\LeadWave\\src\\components\\common\\ConfirmationModal.js": "25", "D:\\My Projects\\LeadWave\\src\\components\\license\\LicenseRegistrationForm.js": "26", "D:\\My Projects\\LeadWave\\src\\locales\\en.js": "27", "D:\\My Projects\\LeadWave\\src\\services\\notification-sound.service.js": "28", "D:\\My Projects\\LeadWave\\src\\utils\\logger.js": "29", "D:\\My Projects\\LeadWave\\src\\components\\common\\LicenseNotification.js": "30", "D:\\My Projects\\LeadWave\\src\\components\\ui\\MessageTester.js": "31", "D:\\My Projects\\LeadWave\\src\\components\\modules\\BackupRestore.js": "32", "D:\\My Projects\\LeadWave\\src\\locales\\pt.js": "33", "D:\\My Projects\\LeadWave\\src\\locales\\he.js": "34", "D:\\My Projects\\LeadWave\\src\\locales\\fr.js": "35", "D:\\My Projects\\LeadWave\\src\\components\\common\\NotificationDropdown.js": "36", "D:\\My Projects\\LeadWave\\src\\locales\\es.js": "37", "D:\\My Projects\\LeadWave\\src\\locales\\ar.js": "38", "D:\\My Projects\\LeadWave\\src\\components\\license\\LicenseInputForm.js": "39"}, {"size": 264, "mtime": 1750754453159, "results": "40", "hashOfConfig": "41"}, {"size": 11861, "mtime": 1753740901232, "results": "42", "hashOfConfig": "41"}, {"size": 10243, "mtime": 1752952861631, "results": "43", "hashOfConfig": "41"}, {"size": 3222, "mtime": 1751126876610, "results": "44", "hashOfConfig": "41"}, {"size": 22639, "mtime": 1753742643944, "results": "45", "hashOfConfig": "41"}, {"size": 5917, "mtime": 1751556075785, "results": "46", "hashOfConfig": "41"}, {"size": 43522, "mtime": 1753900581929, "results": "47", "hashOfConfig": "41"}, {"size": 52211, "mtime": 1753975967055, "results": "48", "hashOfConfig": "41"}, {"size": 46264, "mtime": 1753879801288, "results": "49", "hashOfConfig": "41"}, {"size": 32020, "mtime": 1753984470162, "results": "50", "hashOfConfig": "41"}, {"size": 113584, "mtime": 1753984470159, "results": "51", "hashOfConfig": "41"}, {"size": 50179, "mtime": 1753884494728, "results": "52", "hashOfConfig": "41"}, {"size": 129050, "mtime": 1753900581929, "results": "53", "hashOfConfig": "41"}, {"size": 145161, "mtime": 1753880288219, "results": "54", "hashOfConfig": "41"}, {"size": 142040, "mtime": 1754036423235, "results": "55", "hashOfConfig": "41"}, {"size": 13021, "mtime": 1753369668028, "results": "56", "hashOfConfig": "41"}, {"size": 63434, "mtime": 1751374544770, "results": "57", "hashOfConfig": "41"}, {"size": 145391, "mtime": 1753867813334, "results": "58", "hashOfConfig": "41"}, {"size": 8162, "mtime": 1753909778220, "results": "59", "hashOfConfig": "41"}, {"size": 17481, "mtime": 1753743875270, "results": "60", "hashOfConfig": "41"}, {"size": 61445, "mtime": 1752921307404, "results": "61", "hashOfConfig": "41"}, {"size": 61958, "mtime": 1752905472326, "results": "62", "hashOfConfig": "41"}, {"size": 6882, "mtime": 1751451503745, "results": "63", "hashOfConfig": "41"}, {"size": 6353, "mtime": 1753909745775, "results": "64", "hashOfConfig": "41"}, {"size": 4220, "mtime": 1751451594624, "results": "65", "hashOfConfig": "41"}, {"size": 15041, "mtime": 1753909853159, "results": "66", "hashOfConfig": "41"}, {"size": 34977, "mtime": 1753304440937, "results": "67", "hashOfConfig": "41"}, {"size": 3969, "mtime": 1751280652683, "results": "68", "hashOfConfig": "41"}, {"size": 3407, "mtime": 1753900581881, "results": "69", "hashOfConfig": "41"}, {"size": 2351, "mtime": 1753909817125, "results": "70", "hashOfConfig": "41"}, {"size": 12075, "mtime": 1750748624319, "results": "71", "hashOfConfig": "41"}, {"size": 11244, "mtime": 1753870983370, "results": "72", "hashOfConfig": "41"}, {"size": 28084, "mtime": 1753304743999, "results": "73", "hashOfConfig": "41"}, {"size": 30746, "mtime": 1753304718924, "results": "74", "hashOfConfig": "41"}, {"size": 35209, "mtime": 1753304682891, "results": "75", "hashOfConfig": "41"}, {"size": 8090, "mtime": 1751280519003, "results": "76", "hashOfConfig": "41"}, {"size": 37158, "mtime": 1753304606043, "results": "77", "hashOfConfig": "41"}, {"size": 41396, "mtime": 1753304529418, "results": "78", "hashOfConfig": "41"}, {"size": 8555, "mtime": 1753378623235, "results": "79", "hashOfConfig": "41"}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1kdnwl6", {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\My Projects\\LeadWave\\src\\index.js", [], [], "D:\\My Projects\\LeadWave\\src\\App.js", [], [], "D:\\My Projects\\LeadWave\\src\\contexts\\NotificationContext.js", [], [], "D:\\My Projects\\LeadWave\\src\\contexts\\ThemeContext.js", [], [], "D:\\My Projects\\LeadWave\\src\\contexts\\LicenseContext.js", [], [], "D:\\My Projects\\LeadWave\\src\\contexts\\I18nContext.js", [], [], "D:\\My Projects\\LeadWave\\src\\components\\modules\\Dashboard.js", [], [], "D:\\My Projects\\LeadWave\\src\\components\\modules\\Devices.js", [], [], "D:\\My Projects\\LeadWave\\src\\components\\modules\\AutoReply.js", [], [], "D:\\My Projects\\LeadWave\\src\\components\\modules\\SingleMessage.js", [], [], "D:\\My Projects\\LeadWave\\src\\components\\modules\\BulkMessages.js", [], [], "D:\\My Projects\\LeadWave\\src\\components\\modules\\AIChatbot.js", [], [], "D:\\My Projects\\LeadWave\\src\\components\\modules\\LiveChat.js", [], [], "D:\\My Projects\\LeadWave\\src\\components\\modules\\Templates.js", [], [], "D:\\My Projects\\LeadWave\\src\\components\\modules\\Chatbot.js", [], [], "D:\\My Projects\\LeadWave\\src\\components\\modules\\Settings.js", [], [], "D:\\My Projects\\LeadWave\\src\\components\\modules\\GroupGrabber.js", [], [], "D:\\My Projects\\LeadWave\\src\\components\\modules\\Contacts.js", [], [], "D:\\My Projects\\LeadWave\\src\\components\\layout\\Sidebar.js", [], [], "D:\\My Projects\\LeadWave\\src\\components\\layout\\Header.js", [], [], "D:\\My Projects\\LeadWave\\src\\components\\modules\\CallResponder.js", [], [], "D:\\My Projects\\LeadWave\\src\\components\\modules\\Reports.js", [], [], "D:\\My Projects\\LeadWave\\src\\components\\common\\NotificationToast.js", [], [], "D:\\My Projects\\LeadWave\\src\\components\\ui\\LoadingScreen.js", [], [], "D:\\My Projects\\LeadWave\\src\\components\\common\\ConfirmationModal.js", [], [], "D:\\My Projects\\LeadWave\\src\\components\\license\\LicenseRegistrationForm.js", [], [], "D:\\My Projects\\LeadWave\\src\\locales\\en.js", [], [], "D:\\My Projects\\LeadWave\\src\\services\\notification-sound.service.js", [], [], "D:\\My Projects\\LeadWave\\src\\utils\\logger.js", [], [], "D:\\My Projects\\LeadWave\\src\\components\\common\\LicenseNotification.js", [], [], "D:\\My Projects\\LeadWave\\src\\components\\ui\\MessageTester.js", [], [], "D:\\My Projects\\LeadWave\\src\\components\\modules\\BackupRestore.js", [], [], "D:\\My Projects\\LeadWave\\src\\locales\\pt.js", [], [], "D:\\My Projects\\LeadWave\\src\\locales\\he.js", [], [], "D:\\My Projects\\LeadWave\\src\\locales\\fr.js", [], [], "D:\\My Projects\\LeadWave\\src\\components\\common\\NotificationDropdown.js", [], [], "D:\\My Projects\\LeadWave\\src\\locales\\es.js", [], [], "D:\\My Projects\\LeadWave\\src\\locales\\ar.js", [], [], "D:\\My Projects\\LeadWave\\src\\components\\license\\LicenseInputForm.js", [], []]