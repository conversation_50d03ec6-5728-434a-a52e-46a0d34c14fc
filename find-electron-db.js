const path = require('path');
const fs = require('fs');
const os = require('os');

async function findElectronDatabase() {
  console.log('🔍 Finding Electron Database...');
  
  // Possible Electron database locations
  const possiblePaths = [
    // Standard Electron userData paths
    path.join(os.homedir(), 'AppData', 'Roaming', 'Electron', 'leadwave.db'),
    path.join(os.homedir(), 'AppData', 'Roaming', 'leadwave-whatsapp-desktop', 'leadwave.db'),
    path.join(os.homedir(), 'AppData', 'Roaming', 'Lead Wave', 'leadwave.db'),
    path.join(os.homedir(), 'AppData', 'Local', 'Electron', 'leadwave.db'),
    path.join(os.homedir(), 'AppData', 'Local', 'leadwave-whatsapp-desktop', 'leadwave.db'),
    path.join(os.homedir(), 'AppData', 'Local', 'Lead Wave', 'leadwave.db'),
    
    // Alternative paths
    path.join(os.homedir(), 'Lead Wave', 'leadwave.db'),
    path.join(os.homedir(), 'LeadWave', 'leadwave.db'),
    
    // Development paths
    path.join(process.cwd(), 'data', 'leadwave.db'),
    path.join(process.cwd(), 'leadwave.db'),
  ];
  
  console.log('\n📂 Checking possible database locations:');
  
  const foundDatabases = [];
  
  for (const dbPath of possiblePaths) {
    try {
      if (fs.existsSync(dbPath)) {
        const stats = fs.statSync(dbPath);
        console.log(`  ✅ FOUND: ${dbPath}`);
        console.log(`     Size: ${(stats.size / 1024).toFixed(2)} KB`);
        console.log(`     Modified: ${stats.mtime.toISOString()}`);
        foundDatabases.push({
          path: dbPath,
          size: stats.size,
          modified: stats.mtime
        });
      } else {
        console.log(`  ❌ Not found: ${dbPath}`);
      }
    } catch (error) {
      console.log(`  ❌ Error checking: ${dbPath} - ${error.message}`);
    }
  }
  
  if (foundDatabases.length === 0) {
    console.log('\n❌ No database files found!');
    return null;
  }
  
  // Sort by modification time (most recent first)
  foundDatabases.sort((a, b) => b.modified - a.modified);
  
  console.log('\n🎯 Most likely database (most recently modified):');
  const mostLikely = foundDatabases[0];
  console.log(`   Path: ${mostLikely.path}`);
  console.log(`   Size: ${(mostLikely.size / 1024).toFixed(2)} KB`);
  console.log(`   Modified: ${mostLikely.modified.toISOString()}`);
  
  return mostLikely.path;
}

// If run directly, execute the function
if (require.main === module) {
  findElectronDatabase().then(dbPath => {
    if (dbPath) {
      console.log(`\n💡 Use this path for debugging: ${dbPath}`);
      console.log('\n📋 Copy this path to use in other scripts:');
      console.log(`"${dbPath}"`);
    }
    process.exit(0);
  }).catch(console.error);
}

module.exports = findElectronDatabase;
