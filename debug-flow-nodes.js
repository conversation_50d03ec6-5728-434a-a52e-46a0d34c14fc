const initSqlJs = require('sql.js');
const fs = require('fs');
const path = require('path');

async function debugFlowNodes() {
  console.log('🔍 Debugging chatbot flow nodes...');
  
  // Use the actual database path from AppData
  const dbPath = path.join(process.env.APPDATA, 'Electron', 'leadwave.db');
  
  if (!fs.existsSync(dbPath)) {
    console.log('❌ Database not found at:', dbPath);
    return;
  }
  
  const SQL = await initSqlJs();
  const filebuffer = fs.readFileSync(dbPath);
  const db = new SQL.Database(filebuffer);
  
  try {
    // Get the PHP flow
    const flows = db.exec('SELECT * FROM chatbot_flows WHERE trigger_keywords LIKE "%php%" AND is_active = 1');
    
    if (flows.length === 0 || flows[0].values.length === 0) {
      console.log('❌ No PHP flow found');
      return;
    }
    
    const flow = flows[0].values[0];
    const flowColumns = flows[0].columns;
    const flowId = flow[flowColumns.indexOf('id')];
    const flowName = flow[flowColumns.indexOf('name')];
    
    console.log(`\n🤖 Found flow: "${flowName}" (ID: ${flowId})`);
    
    // Check nodes for this flow
    console.log('\n🔍 Checking nodes for this flow...');
    const nodes = db.exec('SELECT * FROM chatbot_nodes WHERE flow_id = ? ORDER BY position ASC', [flowId]);
    
    if (nodes.length === 0 || nodes[0].values.length === 0) {
      console.log('❌ NO NODES FOUND! This is the problem!');
      console.log('   The flow exists but has no nodes, so startChatbotFlow fails');
      console.log('   This explains why it keeps sending the same list message');
      
      // Check if there are any nodes at all
      const allNodes = db.exec('SELECT flow_id, COUNT(*) as node_count FROM chatbot_nodes GROUP BY flow_id');
      if (allNodes.length > 0 && allNodes[0].values.length > 0) {
        console.log('\n📊 Node counts by flow:');
        allNodes[0].values.forEach(row => {
          const [nodeFlowId, count] = row;
          console.log(`   Flow ${nodeFlowId}: ${count} nodes`);
        });
      } else {
        console.log('\n❌ No nodes found in entire database!');
      }
      
    } else {
      console.log(`✅ Found ${nodes[0].values.length} node(s):`);
      
      const nodeColumns = nodes[0].columns;
      nodes[0].values.forEach((node, index) => {
        const nodeId = node[nodeColumns.indexOf('id')];
        const nodeType = node[nodeColumns.indexOf('node_type')];
        const position = node[nodeColumns.indexOf('position')];
        const content = node[nodeColumns.indexOf('content')];
        
        console.log(`   ${index + 1}. Node ${nodeId}: ${nodeType} at position ${position}`);
        if (content) {
          console.log(`      Content: ${content.substring(0, 100)}...`);
        }
      });
      
      // Get the first node specifically
      const firstNode = nodes[0].values[0];
      const firstNodeId = firstNode[nodeColumns.indexOf('id')];
      console.log(`\n🎯 First node ID: ${firstNodeId}`);
    }
    
    // Check conversations for this flow
    console.log('\n🔍 Checking conversations for this flow...');
    const conversations = db.exec('SELECT * FROM chatbot_conversations WHERE flow_id = ? ORDER BY id DESC LIMIT 5', [flowId]);
    
    if (conversations.length > 0 && conversations[0].values.length > 0) {
      console.log(`📞 Found ${conversations[0].values.length} recent conversation(s):`);
      
      const convColumns = conversations[0].columns;
      conversations[0].values.forEach((conv, index) => {
        const convId = conv[convColumns.indexOf('id')];
        const userPhone = conv[convColumns.indexOf('user_phone')];
        const status = conv[convColumns.indexOf('status')];
        const createdAt = conv[convColumns.indexOf('created_at')];
        
        console.log(`   ${index + 1}. Conversation ${convId}: User ${userPhone}, Status: ${status}, Created: ${createdAt}`);
      });
    } else {
      console.log('📞 No conversations found for this flow');
    }
    
    // Check if there are active conversations blocking new ones
    const activeConversations = db.exec('SELECT * FROM chatbot_conversations WHERE status = ? ORDER BY id DESC', ['active']);
    
    if (activeConversations.length > 0 && activeConversations[0].values.length > 0) {
      console.log(`\n⚠️  Found ${activeConversations[0].values.length} active conversation(s):`);
      
      const activeConvColumns = activeConversations[0].columns;
      activeConversations[0].values.forEach((conv, index) => {
        const convId = conv[activeConvColumns.indexOf('id')];
        const flowId = conv[activeConvColumns.indexOf('flow_id')];
        const userPhone = conv[activeConvColumns.indexOf('user_phone')];
        const createdAt = conv[activeConvColumns.indexOf('created_at')];
        
        console.log(`   ${index + 1}. Conversation ${convId}: Flow ${flowId}, User ${userPhone}, Created: ${createdAt}`);
      });
      
      console.log('\n🚨 ACTIVE CONVERSATIONS FOUND!');
      console.log('   This might be preventing new flows from starting');
      console.log('   The system might be trying to continue existing conversations instead');
    } else {
      console.log('\n✅ No active conversations found');
    }
    
  } catch (error) {
    console.error('❌ Database error:', error);
  } finally {
    db.close();
  }
  
  console.log('\n💡 Summary:');
  console.log('   If no nodes found: You need to add nodes to your chatbot flow');
  console.log('   If active conversations found: They might be blocking new flow starts');
  console.log('   The app is detecting keywords correctly but failing to start the flow properly');
}

debugFlowNodes().catch(console.error);
