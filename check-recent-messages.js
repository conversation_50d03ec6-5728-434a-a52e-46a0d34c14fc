const DatabaseService = require('./src/services/database.service');

async function checkRecentMessages() {
  console.log('🔍 Checking recent messages in database...');
  
  const databaseService = new DatabaseService();
  await databaseService.initialize();
  
  // Get all recent messages
  const recentMessages = await databaseService.all(`
    SELECT id, session_id, content, message_type, direction, created_at
    FROM message_history 
    ORDER BY id DESC 
    LIMIT 10
  `);
  
  if (recentMessages.success && recentMessages.data.length > 0) {
    console.log(`\n📨 Found ${recentMessages.data.length} recent messages:`);
    recentMessages.data.forEach((msg, index) => {
      console.log(`  ${index + 1}. [${msg.direction}] "${msg.content}" (${msg.message_type}) - Session: ${msg.session_id}`);
    });
    
    // Look for PHP-related messages
    const phpMessages = recentMessages.data.filter(msg => 
      msg.content.toLowerCase().includes('php') || 
      msg.content.toLowerCase().includes('development')
    );
    
    if (phpMessages.length > 0) {
      console.log(`\n🎯 Found ${phpMessages.length} PHP-related messages:`);
      phpMessages.forEach((msg, index) => {
        console.log(`  ${index + 1}. [${msg.direction}] "${msg.content}" - Session: ${msg.session_id}`);
      });
      
      // Use the most recent PHP message's session
      const sessionId = phpMessages[0].session_id;
      console.log(`\n📱 Using session ID: ${sessionId}`);
      
      // Check flows for this session
      const flows = await databaseService.all(`
        SELECT * FROM chatbot_flows 
        WHERE session_id = ? AND is_active = 1
      `, [sessionId]);
      
      if (flows.success && flows.data.length > 0) {
        console.log(`\n🤖 Found ${flows.data.length} active flow(s) for this session:`);
        flows.data.forEach((flow, index) => {
          console.log(`  ${index + 1}. "${flow.name}" - Keywords: "${flow.trigger_keywords}"`);
        });
      } else {
        console.log(`\n❌ No active flows found for session ${sessionId}`);
        
        // Check if there are flows for other sessions
        const allFlows = await databaseService.all(`
          SELECT * FROM chatbot_flows WHERE is_active = 1
        `);
        
        if (allFlows.success && allFlows.data.length > 0) {
          console.log(`\n⚠️  But found ${allFlows.data.length} active flow(s) for other sessions:`);
          allFlows.data.forEach((flow, index) => {
            console.log(`  ${index + 1}. "${flow.name}" - Session: ${flow.session_id} - Keywords: "${flow.trigger_keywords}"`);
          });
          console.log(`\n🚨 SESSION MISMATCH! Your messages are in session "${sessionId}" but flows are in different sessions!`);
        }
      }
      
      // Check active conversations
      const activeConversations = await databaseService.all(`
        SELECT * FROM chatbot_conversations 
        WHERE session_id = ? AND status = 'active'
      `, [sessionId]);
      
      if (activeConversations.success && activeConversations.data.length > 0) {
        console.log(`\n⚠️  Found ${activeConversations.data.length} active conversation(s) for this session:`);
        activeConversations.data.forEach((conv, index) => {
          console.log(`  ${index + 1}. ID: ${conv.id}, Flow: ${conv.flow_id}, Started: ${conv.created_at}`);
        });
        console.log(`\n🚨 ACTIVE CONVERSATIONS FOUND! This might be blocking new flow triggers!`);
      } else {
        console.log(`\n✅ No active conversations found for session ${sessionId}`);
      }
      
    } else {
      console.log(`\n❌ No PHP-related messages found in recent messages`);
    }
    
  } else {
    console.log('\n❌ No recent messages found in database');
  }
  
  // Also check all sessions
  console.log(`\n📱 All sessions in database:`);
  const sessions = await databaseService.all(`
    SELECT DISTINCT session_id FROM message_history 
    ORDER BY session_id
  `);
  
  if (sessions.success && sessions.data.length > 0) {
    sessions.data.forEach((session, index) => {
      console.log(`  ${index + 1}. ${session.session_id}`);
    });
  }
}

checkRecentMessages().catch(console.error);
