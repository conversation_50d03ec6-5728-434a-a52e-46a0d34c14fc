const { contextBridge, ipcRenderer } = require('electron');

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  // App related APIs
  getVersion: () => ipcRenderer.invoke('app-version'),

  // App close confirmation
  onShowCloseConfirmation: (callback) => {
    ipcRenderer.on('app:show-close-confirmation', (event, data) => callback(data));
  },
  sendCloseConfirmationResponse: (confirmed) => {
    ipcRenderer.send('app:close-confirmation-response', confirmed);
  },
  
  // Dialog APIs
  showMessageBox: (options) => ipcRenderer.invoke('show-message-box', options),
  showOpenDialog: (options) => ipcRenderer.invoke('show-open-dialog', options),
  showSaveDialog: (options) => ipcRenderer.invoke('show-save-dialog', options),
  
  // WhatsApp APIs
  whatsapp: {
    // Session Management
    createSession: (sessionData) => ipcRenderer.invoke('whatsapp:create-session', sessionData),
    disconnectSession: (sessionId) => ipcRenderer.invoke('whatsapp:disconnect-session', sessionId),
    reconnectSession: (sessionId) => ipcRenderer.invoke('whatsapp:reconnect-session', sessionId),
    deleteSession: (sessionId) => ipcRenderer.invoke('whatsapp:delete-session', sessionId),
    getSessions: () => ipcRenderer.invoke('whatsapp:get-sessions'),
    getSessionStatus: (sessionId) => ipcRenderer.invoke('whatsapp:get-session-status', sessionId),
    
    // Authentication
    requestPairingCode: (sessionId, phoneNumber) => ipcRenderer.invoke('whatsapp:request-pairing-code', sessionId, phoneNumber),
    createPairingSession: (phoneNumber) => ipcRenderer.invoke('whatsapp:create-pairing-session', phoneNumber),
    
    // Message Management
    sendMessage: (sessionId, to, message, type, options) => ipcRenderer.invoke('whatsapp:send-message', sessionId, to, message, type, options),
    sendTemplateMessage: (sessionId, to, template, variables) => ipcRenderer.invoke('whatsapp:send-template-message', sessionId, to, template, variables),
    checkNumber: (sessionId, phoneNumber) => ipcRenderer.invoke('whatsapp:check-number', sessionId, phoneNumber),
    verifyNumber: (phoneNumber) => ipcRenderer.invoke('whatsapp:verify-number', phoneNumber),

    // Chat Management
    getChats: (sessionId) => ipcRenderer.invoke('whatsapp:get-chats', sessionId),
    getChatHistory: (sessionId, chatId, limit) => ipcRenderer.invoke('whatsapp:get-chat-history', sessionId, chatId, limit),
    markChatAsRead: (sessionId, chatId) => ipcRenderer.invoke('whatsapp:mark-chat-as-read', sessionId, chatId),

    // Call Responder
    triggerOutgoingCall: (sessionId, contactJid) => ipcRenderer.invoke('whatsapp:trigger-outgoing-call', sessionId, contactJid),

    // Media Management
    downloadMedia: (sessionId, messageKey) => ipcRenderer.invoke('whatsapp:download-media', sessionId, messageKey),
    uploadMedia: (filePath) => ipcRenderer.invoke('whatsapp:upload-media', filePath),

    // Group Management
    fetchAllGroups: (sessionId) => ipcRenderer.invoke('whatsapp:fetch-all-groups', sessionId),
    getGroupMetadata: (sessionId, groupId) => ipcRenderer.invoke('whatsapp:get-group-metadata', sessionId, groupId),
    getGroupInviteCode: (sessionId, groupId) => ipcRenderer.invoke('whatsapp:get-group-invite-code', sessionId, groupId),
    getGroupInfoByInvite: (sessionId, inviteCode) => ipcRenderer.invoke('whatsapp:get-group-info-by-invite', sessionId, inviteCode),

    // Event listeners for WhatsApp events
    on: (event, callback) => {
      const eventKey = `whatsapp:${event.replace(/_/g, '-')}`;
      ipcRenderer.on(eventKey, (event, data) => callback(data));
      return () => ipcRenderer.removeAllListeners(eventKey);
    },
    
    onQRCode: (callback) => {
      ipcRenderer.on('whatsapp:qr-code', (event, data) => callback(data));
      return () => ipcRenderer.removeAllListeners('whatsapp:qr-code');
    },
    
    onSessionConnected: (callback) => {
      ipcRenderer.on('whatsapp:session-connected', (event, data) => callback(data));
      return () => ipcRenderer.removeAllListeners('whatsapp:session-connected');
    },
    
    onSessionDisconnected: (callback) => {
      ipcRenderer.on('whatsapp:session-disconnected', (event, data) => callback(data));
      return () => ipcRenderer.removeAllListeners('whatsapp:session-disconnected');
    },
    
    onSessionStatusUpdate: (callback) => {
      ipcRenderer.on('whatsapp:session-status-update', (event, data) => callback(data));
      return () => ipcRenderer.removeAllListeners('whatsapp:session-status-update');
    },
    
    onMessageReceived: (callback) => {
      ipcRenderer.on('whatsapp:message-received', (event, data) => callback(data));
      return () => ipcRenderer.removeAllListeners('whatsapp:message-received');
    },

    onContactsUpdate: (callback) => {
      ipcRenderer.on('whatsapp:contacts-update', (event, data) => callback(data));
      return () => ipcRenderer.removeAllListeners('whatsapp:contacts-update');
    },

    onPresenceUpdate: (callback) => {
      ipcRenderer.on('whatsapp:presence-update', (event, data) => callback(data));
      return () => ipcRenderer.removeAllListeners('whatsapp:presence-update');
    },
    
    onCallReceived: (callback) => {
      ipcRenderer.on('whatsapp:call-received', (event, data) => callback(data));
      return () => ipcRenderer.removeAllListeners('whatsapp:call-received');
    },
    
    onSessionDeleted: (callback) => {
      ipcRenderer.on('whatsapp:session-deleted', (event, data) => callback(data));
      return () => ipcRenderer.removeAllListeners('whatsapp:session-deleted');
    },
    
    // Remove specific listeners
    removeListener: (event, callback) => ipcRenderer.removeListener(event, callback),
    
    // Remove all listeners
    removeAllListeners: (event) => {
      if (event) {
        const eventKey = `whatsapp:${event.replace(/_/g, '-')}`;
        ipcRenderer.removeAllListeners(eventKey);
      } else {
        // Remove all WhatsApp event listeners
        ipcRenderer.removeAllListeners('whatsapp:qr-code');
        ipcRenderer.removeAllListeners('whatsapp:session-connected');
        ipcRenderer.removeAllListeners('whatsapp:session-disconnected');
        ipcRenderer.removeAllListeners('whatsapp:session-status-update');
        ipcRenderer.removeAllListeners('whatsapp:message-received');
        ipcRenderer.removeAllListeners('whatsapp:call-received');
        ipcRenderer.removeAllListeners('whatsapp:session-deleted');
      }
    }
  },
  
  // Database APIs
  database: {
    query: (query, params) => ipcRenderer.invoke('db-query', query, params)
  },

  // Campaign Scheduler APIs
  campaignScheduler: {
    getStatus: () => ipcRenderer.invoke('campaign-scheduler:get-status'),
    triggerCheck: () => ipcRenderer.invoke('campaign-scheduler:trigger-check'),
    startCampaign: (campaignId) => ipcRenderer.invoke('campaign-scheduler:start-campaign', campaignId)
  },

  // Application APIs
  app: {
    getStats: () => ipcRenderer.invoke('app-stats'),
    getHealth: () => ipcRenderer.invoke('app-health'),
    getRecentActivities: (limit) => ipcRenderer.invoke('app-recent-activities', limit),
    quit: () => ipcRenderer.invoke('app-quit')
  },

  // License Management APIs
  license: {
    getMachineId: () => ipcRenderer.invoke('license:get-machine-id'),
    activate: (licenseKey) => ipcRenderer.invoke('license:activate', licenseKey),
    upgrade: (newLicenseKey) => ipcRenderer.invoke('license:upgrade', newLicenseKey),
    registerTrial: (userData) => ipcRenderer.invoke('license:register-trial', userData),
    validate: () => ipcRenderer.invoke('license:validate'),
    getLocalInfo: () => ipcRenderer.invoke('license:get-local-info'),
    saveLocalInfo: (licenseData) => ipcRenderer.invoke('license:save-local-info', licenseData),
    clearLocalData: () => ipcRenderer.invoke('license:clear-local-data'),
    checkMachine: () => ipcRenderer.invoke('license:check-machine'),
    checkStatus: (licenseKey) => ipcRenderer.invoke('license:check-status', licenseKey),
    forceRefresh: () => ipcRenderer.invoke('license:force-refresh'),
    backgroundStatus: () => ipcRenderer.invoke('license:background-status'),
    debugClear: () => ipcRenderer.invoke('license:debug-clear')
  },

  // Reseller Configuration APIs
  reseller: {
    getConfig: () => ipcRenderer.invoke('reseller:get-config')
  },

  // Window Control APIs
  window: {
    toggleFrame: (showFrame) => ipcRenderer.invoke('window:toggle-frame', showFrame),
    getFrameStatus: () => ipcRenderer.invoke('window:get-frame-status'),
    applySavedPreference: () => ipcRenderer.invoke('window:apply-saved-preference')
  },
  
  // File System APIs
  fs: {
    readFile: (filePath) => ipcRenderer.invoke('fs-read-file', filePath),
    writeFile: (filePath, data) => ipcRenderer.invoke('fs-write-file', filePath, data)
  },

  // Shell APIs
  shell: {
    openExternal: (url) => ipcRenderer.invoke('shell-open-external', url)
  },

  // Backup and Restore APIs
  backup: {
    create: (options) => ipcRenderer.invoke('backup:create', options),
    restore: (filePath, options) => ipcRenderer.invoke('backup:restore', filePath, options),

    schedule: (schedulePattern, options) => ipcRenderer.invoke('backup:schedule', schedulePattern, options),
    cancelSchedule: (jobId) => ipcRenderer.invoke('backup:cancel-schedule', jobId),
    getHistory: () => ipcRenderer.invoke('backup:get-history'),
    selectFile: () => ipcRenderer.invoke('backup:select-file'),
    selectSaveLocation: (defaultName) => ipcRenderer.invoke('backup:select-save-location', defaultName),
    downloadToLocal: (filePath) => ipcRenderer.invoke('backup:download-to-local', filePath),
    validateFile: (filePath) => ipcRenderer.invoke('backup:validate-file', filePath),
    getFileInfo: (filePath) => ipcRenderer.invoke('backup:get-file-info', filePath),
    cleanOld: (retentionDays) => ipcRenderer.invoke('backup:clean-old', retentionDays)
  },

  // App APIs
  app: {
    restart: () => ipcRenderer.invoke('app:restart')
  },

  // Utility APIs
  utils: {
    isElectron: true,
    platform: process.platform,
    isDevelopment: () => ipcRenderer.invoke('app:is-development')
  },

  // Events API for generic event handling
  events: {
    on: (event, callback) => {
      ipcRenderer.on(event, (ipcEvent, data) => callback(data));
      return () => ipcRenderer.removeAllListeners(event);
    },

    removeListener: (event, callback) => {
      ipcRenderer.removeListener(event, callback);
    },

    removeAllListeners: (event) => {
      ipcRenderer.removeAllListeners(event);
    }
  },

  // Notification APIs
  notifications: {
    getNotifications: () => ipcRenderer.invoke('notifications:get-notifications'),
    getLatestNotifications: (lastCheck) => ipcRenderer.invoke('notifications:get-latest', lastCheck),
    markAsRead: (notificationId) => ipcRenderer.invoke('notifications:mark-as-read', notificationId),
    getStats: () => ipcRenderer.invoke('notifications:get-stats'),
    onNewNotification: (callback) => {
      ipcRenderer.on('notifications:new-notification', (event, data) => callback(data));
      return () => ipcRenderer.removeAllListeners('notifications:new-notification');
    },
    onNotificationUpdate: (callback) => {
      ipcRenderer.on('notifications:update', (event, data) => callback(data));
      return () => ipcRenderer.removeAllListeners('notifications:update');
    },
    onShowToast: (callback) => {
      ipcRenderer.on('notifications:show-toast', (event, data) => callback(data));
      return () => ipcRenderer.removeAllListeners('notifications:show-toast');
    }
  },

  // AI Chatbot APIs
  ai: {
    forceMigration: () => ipcRenderer.invoke('ai-schema:force-migration'),

    // AI Chatbot Management
    chatbots: {
      getAll: () => ipcRenderer.invoke('ai-chatbots:get-all'),
      create: (chatbotData) => ipcRenderer.invoke('ai-chatbots:create', chatbotData),
      update: (id, chatbotData) => ipcRenderer.invoke('ai-chatbots:update', id, chatbotData),
      delete: (id) => ipcRenderer.invoke('ai-chatbots:delete', id)
    },

    // AI Provider Management
    providers: {
      getAll: () => ipcRenderer.invoke('ai-providers:get-all'),
      create: (providerData) => ipcRenderer.invoke('ai-providers:create', providerData),
      update: (id, providerData) => ipcRenderer.invoke('ai-providers:update', id, providerData),
      delete: (id) => ipcRenderer.invoke('ai-providers:delete', id)
    }
  }
});

// Preload script loaded successfully - removed console.log for production