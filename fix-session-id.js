const initSqlJs = require('sql.js');
const fs = require('fs');
const path = require('path');

async function fixSessionId() {
  console.log('🔧 Fixing chatbot flow session ID...');
  
  const dbPath = path.join(process.env.APPDATA, 'Electron', 'leadwave.db');
  
  if (!fs.existsSync(dbPath)) {
    console.log('❌ Database not found');
    return;
  }
  
  const SQL = await initSqlJs();
  const filebuffer = fs.readFileSync(dbPath);
  const db = new SQL.Database(filebuffer);
  
  try {
    // 1. Get the current active session ID
    const sessions = db.exec('SELECT id FROM whatsapp_sessions WHERE status = "connected" LIMIT 1');
    if (sessions.length === 0 || sessions[0].values.length === 0) {
      console.log('❌ No active WhatsApp session found');
      return;
    }
    
    const activeSessionId = sessions[0].values[0][0];
    console.log(`✅ Active session ID: ${activeSessionId}`);
    
    // 2. Get current chatbot flows with wrong session ID
    const flows = db.exec('SELECT id, name, session_id FROM chatbot_flows WHERE is_active = 1');
    if (flows.length === 0 || flows[0].values.length === 0) {
      console.log('❌ No active chatbot flows found');
      return;
    }
    
    console.log('\n📋 Current chatbot flows:');
    flows[0].values.forEach((row, index) => {
      const [id, name, sessionId] = row;
      console.log(`  Flow ${index + 1}: ID=${id}, Name="${name}", Session="${sessionId}"`);
    });
    
    // 3. Update all active flows to use the correct session ID
    console.log(`\n🔧 Updating flows to use session ID: ${activeSessionId}`);
    
    const updateResult = db.exec(`
      UPDATE chatbot_flows 
      SET session_id = '${activeSessionId}' 
      WHERE is_active = 1
    `);
    
    console.log('✅ Updated chatbot flows session ID');
    
    // 4. Also update any active conversations to use the correct session ID
    const conversationUpdateResult = db.exec(`
      UPDATE chatbot_conversations 
      SET session_id = '${activeSessionId}' 
      WHERE is_active = 1
    `);
    
    console.log('✅ Updated active conversations session ID');
    
    // 5. Save the changes back to the database file
    const data = db.export();
    fs.writeFileSync(dbPath, data);
    console.log('✅ Changes saved to database');
    
    // 6. Verify the changes
    console.log('\n🔍 Verifying changes...');
    const updatedFlows = db.exec('SELECT id, name, session_id FROM chatbot_flows WHERE is_active = 1');
    if (updatedFlows.length > 0) {
      console.log('Updated flows:');
      updatedFlows[0].values.forEach((row, index) => {
        const [id, name, sessionId] = row;
        console.log(`  Flow ${index + 1}: ID=${id}, Name="${name}", Session="${sessionId}"`);
      });
    }
    
    console.log('\n🎉 Session ID fix completed! Your chatbot flow should now work.');
    console.log('💡 Try sending "PHP" or "development" to test the flow.');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    db.close();
  }
}

fixSessionId().catch(console.error);
