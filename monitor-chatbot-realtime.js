const DatabaseService = require('./src/services/database.service');
const MessageProcessor = require('./src/services/message-processor.service');

async function monitorChatbotRealtime() {
  console.log('🔍 Starting real-time chatbot monitoring...');
  console.log('📱 Now send "PHP" from WhatsApp and watch what happens!\n');
  
  const databaseService = new DatabaseService();
  const messageProcessor = new MessageProcessor();
  
  await databaseService.initialize();
  
  // Get initial message count
  const initialCount = await databaseService.get('SELECT COUNT(*) as count FROM message_history');
  let lastMessageCount = initialCount.success ? initialCount.data.count : 0;
  
  console.log(`📊 Starting with ${lastMessageCount} messages in database`);
  console.log('🔄 Monitoring for new messages...\n');
  
  // Monitor for new messages every 2 seconds
  const monitorInterval = setInterval(async () => {
    try {
      // Check for new messages
      const currentCount = await databaseService.get('SELECT COUNT(*) as count FROM message_history');
      const newMessageCount = currentCount.success ? currentCount.data.count : 0;
      
      if (newMessageCount > lastMessageCount) {
        console.log(`\n🆕 NEW MESSAGE(S) DETECTED! (${newMessageCount - lastMessageCount} new)`);
        
        // Get the latest messages
        const newMessages = await databaseService.all(`
          SELECT id, session_id, content, message_type, direction, created_at
          FROM message_history 
          ORDER BY id DESC 
          LIMIT ${newMessageCount - lastMessageCount}
        `);
        
        if (newMessages.success && newMessages.data.length > 0) {
          newMessages.data.reverse().forEach((msg, index) => {
            console.log(`   ${index + 1}. [${msg.direction}] "${msg.content}" (${msg.message_type})`);
            console.log(`      Session: ${msg.session_id}`);
            
            // Check if this is a PHP message
            if (msg.content.toLowerCase().includes('php') || msg.content.toLowerCase().includes('development')) {
              console.log(`      🎯 PHP-RELATED MESSAGE DETECTED!`);
              
              // Test keyword matching
              const keywords = ['php', 'development'];
              const messageText = msg.content.toLowerCase().trim();
              
              keywords.forEach(keyword => {
                const match = messageProcessor.matchesKeyword(messageText, keyword, 'contains');
                console.log(`         "${keyword}" matches "${messageText}": ${match ? '✅ YES' : '❌ NO'}`);
              });
              
              // Check if there's a flow for this session
              checkFlowForSession(msg.session_id);
            }
          });
        }
        
        lastMessageCount = newMessageCount;
      }
      
    } catch (error) {
      console.error('❌ Error monitoring messages:', error.message);
    }
  }, 2000);
  
  // Function to check flows for a session
  async function checkFlowForSession(sessionId) {
    try {
      const flows = await databaseService.all(`
        SELECT * FROM chatbot_flows 
        WHERE session_id = ? AND is_active = 1
      `, [sessionId]);
      
      if (flows.success && flows.data.length > 0) {
        console.log(`         ✅ Found ${flows.data.length} active flow(s) for this session:`);
        flows.data.forEach(flow => {
          console.log(`            - "${flow.name}" with keywords: "${flow.trigger_keywords}"`);
        });
        
        // Check for active conversations
        const activeConversations = await databaseService.all(`
          SELECT * FROM chatbot_conversations 
          WHERE session_id = ? AND status = 'active'
        `, [sessionId]);
        
        if (activeConversations.success && activeConversations.data.length > 0) {
          console.log(`         ⚠️  ${activeConversations.data.length} active conversation(s) found - might block new triggers`);
        } else {
          console.log(`         ✅ No active conversations - should allow new triggers`);
        }
        
      } else {
        console.log(`         ❌ No active flows found for session ${sessionId}`);
        
        // Check if flows exist for other sessions
        const allFlows = await databaseService.all('SELECT session_id, name FROM chatbot_flows WHERE is_active = 1');
        if (allFlows.success && allFlows.data.length > 0) {
          console.log(`         ⚠️  But found flows for other sessions:`);
          allFlows.data.forEach(flow => {
            console.log(`            - "${flow.name}" in session ${flow.session_id}`);
          });
          console.log(`         🚨 SESSION MISMATCH! Message session ≠ Flow session`);
        }
      }
    } catch (error) {
      console.error('         ❌ Error checking flows:', error.message);
    }
  }
  
  // Stop monitoring after 2 minutes
  setTimeout(() => {
    clearInterval(monitorInterval);
    console.log('\n⏰ Monitoring stopped after 2 minutes');
    console.log('💡 If no messages were detected:');
    console.log('   1. Check if WhatsApp is connected in the app');
    console.log('   2. Verify you\'re sending to the correct number');
    console.log('   3. Check if the app is actually receiving messages');
    process.exit(0);
  }, 120000); // 2 minutes
  
  console.log('⏰ Monitoring will stop automatically in 2 minutes...');
  console.log('📱 Send "PHP" from WhatsApp now!');
}

monitorChatbotRealtime().catch(console.error);
