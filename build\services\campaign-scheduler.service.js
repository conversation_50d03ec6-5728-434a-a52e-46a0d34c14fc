const { EventEmitter } = require('events');

class CampaignSchedulerService extends EventEmitter {
  constructor() {
    super();
    this.schedulerInterval = null;
    this.isRunning = false;
    this.checkInterval = 30000; // Check every 30 seconds
    this.activeProcesses = new Map(); // Track active campaign processes
    this.databaseService = null;
    this.whatsappService = null;
    this.messageProcessor = null;
  }

  /**
   * Initialize the scheduler service
   */
  async initialize(databaseService, whatsappService, messageProcessor) {
    try {
      this.databaseService = databaseService;
      this.whatsappService = whatsappService;
      this.messageProcessor = messageProcessor;

      console.log('📅 Campaign Scheduler Service initialized');
      return { success: true };
    } catch (error) {
      console.error('❌ Failed to initialize Campaign Scheduler Service:', error);
      throw error;
    }
  }

  /**
   * Start the campaign scheduler
   */
  start() {
    if (this.isRunning) {
      console.log('📅 Campaign scheduler is already running');
      return;
    }

    this.isRunning = true;
    console.log(`🚀 Starting campaign scheduler with ${this.checkInterval}ms interval...`);

    // Start the scheduler interval
    this.schedulerInterval = setInterval(() => {
      console.log('⏰ Scheduler interval triggered');
      this.checkScheduledCampaigns();
    }, this.checkInterval);

    // Run initial check
    console.log('🔍 Running initial scheduler check...');
    this.checkScheduledCampaigns();

    this.emit('scheduler-started');
    console.log('✅ Campaign scheduler started successfully');
  }

  /**
   * Stop the campaign scheduler
   */
  stop() {
    if (!this.isRunning) {
      return;
    }

    this.isRunning = false;

    if (this.schedulerInterval) {
      clearInterval(this.schedulerInterval);
      this.schedulerInterval = null;
    }

    console.log('⏹️ Campaign scheduler stopped');
    this.emit('scheduler-stopped');
  }

  /**
   * Manually trigger a scheduler check (for testing)
   */
  async triggerCheck() {
    console.log('🔄 Manual scheduler check triggered');
    await this.checkScheduledCampaigns();
  }

  /**
   * Get scheduler status
   */
  getStatus() {
    return {
      isRunning: this.isRunning,
      checkInterval: this.checkInterval,
      activeProcesses: this.activeProcesses.size
    };
  }

  /**
   * Check for scheduled campaigns that should be started
   */
  async checkScheduledCampaigns() {
    if (!this.databaseService || !this.isRunning) {
      console.log('⚠️ Scheduler check skipped - database not available or scheduler not running');
      return;
    }

    try {
      const now = new Date().toISOString();
      console.log(`🔍 Checking for scheduled campaigns at ${now}`);

      // First, let's see all scheduled campaigns for debugging
      const allScheduledResponse = await this.databaseService.query(`
        SELECT id, name, status, scheduled_at FROM bulk_campaigns
        WHERE status = 'scheduled'
        AND scheduled_at IS NOT NULL
        ORDER BY scheduled_at ASC
      `);

      console.log(`📊 All scheduled campaigns:`, allScheduledResponse);

      // Get campaigns that are scheduled and should be started now
      const response = await this.databaseService.query(`
        SELECT * FROM bulk_campaigns
        WHERE status = 'scheduled'
        AND scheduled_at <= ?
        AND scheduled_at IS NOT NULL
        ORDER BY scheduled_at ASC
      `, [now]);

      console.log(`📊 Scheduler query result:`, response);

      if (response.success) {
        if (response.data.length > 0) {
          console.log(`📅 Found ${response.data.length} campaigns ready to start`);

          for (const campaign of response.data) {
            console.log(`🚀 Processing campaign: ${campaign.name} (ID: ${campaign.id}) scheduled for ${campaign.scheduled_at}`);
            await this.startScheduledCampaign(campaign);
          }
        } else {
          console.log('📅 No campaigns ready to start at this time');
        }
      } else {
        console.error('❌ Database query failed:', response.error);
      }
    } catch (error) {
      console.error('❌ Error checking scheduled campaigns:', error);
    }
  }

  /**
   * Start a scheduled campaign
   */
  async startScheduledCampaign(campaign) {
    try {
      console.log(`🚀 Starting scheduled campaign: ${campaign.name} (ID: ${campaign.id})`);

      // Update campaign status to running
      const updateResponse = await this.databaseService.query(
        'UPDATE bulk_campaigns SET status = ?, started_at = CURRENT_TIMESTAMP WHERE id = ?',
        ['running', campaign.id]
      );

      if (!updateResponse.success) {
        console.error(`❌ Failed to update campaign status for ID ${campaign.id}:`, updateResponse.error);
        return;
      }

      // Start processing the campaign
      this.processCampaign(campaign.id);

      this.emit('campaign-started', { campaignId: campaign.id, campaignName: campaign.name });
      
    } catch (error) {
      console.error(`❌ Error starting scheduled campaign ${campaign.id}:`, error);
      
      // Mark campaign as failed
      await this.databaseService.query(
        'UPDATE bulk_campaigns SET status = ? WHERE id = ?',
        ['failed', campaign.id]
      );
    }
  }

  /**
   * Process a campaign (send messages)
   */
  async processCampaign(campaignId) {
    console.log(`🚀 *** VARIABLE FIX VERSION *** Processing campaign ${campaignId}`);

    if (this.activeProcesses.has(campaignId)) {
      console.log(`⚠️ Campaign ${campaignId} is already being processed`);
      return;
    }

    this.activeProcesses.set(campaignId, true);

    try {
      console.log(`📤 Processing campaign messages for ID: ${campaignId}`);

      // Get campaign details
      const campaignResponse = await this.databaseService.query(
        'SELECT * FROM bulk_campaigns WHERE id = ?',
        [campaignId]
      );

      if (!campaignResponse.success || campaignResponse.data.length === 0) {
        console.error(`❌ Campaign not found: ${campaignId}`);
        return;
      }

      const campaign = campaignResponse.data[0];
      const sessionIds = JSON.parse(campaign.session_ids || '[]');

      if (sessionIds.length === 0) {
        console.error(`❌ No sessions configured for campaign ${campaignId}`);
        await this.databaseService.query(
          'UPDATE bulk_campaigns SET status = ? WHERE id = ?',
          ['failed', campaignId]
        );
        return;
      }

      // Get pending recipients with contact information including all variables
      const recipientsResponse = await this.databaseService.query(`
        SELECT bcr.*, c.phone_number, c.name, c.email, c.company, c.position,
               c.var1, c.var2, c.var3, c.var4, c.var5,
               c.var6, c.var7, c.var8, c.var9, c.var10
        FROM bulk_campaign_recipients bcr
        INNER JOIN contacts c ON bcr.contact_id = c.id
        WHERE bcr.campaign_id = ? AND bcr.status = ?
        ORDER BY bcr.id ASC
      `, [campaignId, 'pending']);

      if (!recipientsResponse.success || recipientsResponse.data.length === 0) {
        console.log(`✅ No pending recipients found for campaign ${campaignId}, marking as completed`);
        await this.databaseService.query(
          'UPDATE bulk_campaigns SET status = ?, completed_at = CURRENT_TIMESTAMP WHERE id = ?',
          ['completed', campaignId]
        );
        return;
      }

      const recipients = recipientsResponse.data;
      console.log(`📧 Found ${recipients.length} pending recipients for campaign ${campaignId}`);

      // Debug: Log first recipient data to verify variables are loaded
      if (recipients.length > 0) {
        console.log('🔍 SCHEDULER - First recipient data:', {
          id: recipients[0].id,
          name: recipients[0].name,
          phone: recipients[0].phone_number,
          var1: recipients[0].var1,
          var2: recipients[0].var2,
          var3: recipients[0].var3
        });
      }

      let currentSessionIndex = 0;

      // Process each recipient
      for (let i = 0; i < recipients.length; i++) {
        // Check if campaign is still running
        const statusCheck = await this.databaseService.query(
          'SELECT status FROM bulk_campaigns WHERE id = ?',
          [campaignId]
        );

        if (!statusCheck.success || statusCheck.data[0]?.status !== 'running') {
          console.log(`⏹️ Campaign ${campaignId} stopped or paused, exiting message processing`);
          break;
        }

        const recipient = recipients[i];

        // Select session (rotate if enabled)
        const sessionId = campaign.device_rotation && sessionIds.length > 1
          ? sessionIds[currentSessionIndex % sessionIds.length]
          : sessionIds[0];

        if (campaign.device_rotation && sessionIds.length > 1) {
          currentSessionIndex++;
        }

        // Check if this is a video template and add extra stability measures
        let isVideoTemplate = false;
        if (campaign.template_id) {
          const templateResponse = await this.databaseService.query(
            'SELECT type FROM message_templates WHERE id = ?',
            [campaign.template_id]
          );

          if (templateResponse.success && templateResponse.data.length > 0) {
            isVideoTemplate = templateResponse.data[0].type === 'video';
          }
        }

        // For video messages, check connection stability before sending
        if (isVideoTemplate) {
          console.log('📹 Video template detected - checking connection stability...');

          // Check if session is still connected
          const sessionStatus = await this.whatsappService.getSessionStatus(sessionId);
          if (!sessionStatus || sessionStatus.status !== 'connected') {
            console.log(`⚠️ Session ${sessionId} not connected, skipping video message to prevent disconnection`);

            // Mark recipient as failed
            await this.databaseService.query(
              'UPDATE bulk_campaign_recipients SET status = ?, error_message = ? WHERE id = ?',
              ['failed', 'Session disconnected before video send', recipient.id]
            );
            continue;
          }
        }

        await this.sendMessageToRecipient(campaign, recipient, sessionId);

        // Delay between messages (longer delay for video messages to prevent disconnection)
        if (i < recipients.length - 1) {
          let delayTime = campaign.delivery_delay * 1000;

          // For video templates, use minimum 15 seconds delay to prevent device disconnection
          if (isVideoTemplate) {
            delayTime = Math.max(delayTime, 15000); // Minimum 15 seconds for video
            console.log(`📹 Using extended delay of ${delayTime/1000}s for video template`);
          }

          await new Promise(resolve => setTimeout(resolve, delayTime));
        }
      }

      // Check if all messages are processed
      const remainingResponse = await this.databaseService.query(
        'SELECT COUNT(*) as count FROM bulk_campaign_recipients WHERE campaign_id = ? AND status = ?',
        [campaignId, 'pending']
      );

      if (remainingResponse.success && remainingResponse.data[0].count === 0) {
        // Mark campaign as completed
        await this.databaseService.query(
          'UPDATE bulk_campaigns SET status = ?, completed_at = CURRENT_TIMESTAMP WHERE id = ?',
          ['completed', campaignId]
        );
        
        console.log(`✅ Campaign ${campaignId} completed successfully`);
        this.emit('campaign-completed', { campaignId });
      }

    } catch (error) {
      console.error(`❌ Error processing campaign ${campaignId}:`, error);
      console.error(`❌ Error stack:`, error.stack);
      console.error(`❌ Error details:`, {
        message: error.message,
        name: error.name,
        campaignId: campaignId
      });

      // Mark campaign as failed
      await this.databaseService.query(
        'UPDATE bulk_campaigns SET status = ? WHERE id = ?',
        ['failed', campaignId]
      );

      this.emit('campaign-failed', { campaignId, error: error.message });
    } finally {
      this.activeProcesses.delete(campaignId);
      console.log(`🏁 Finished processing campaign ${campaignId}`);
    }
  }

  /**
   * Send message to a specific recipient
   */
  async sendMessageToRecipient(campaign, recipient, sessionId) {
    try {
      let phoneNumber = recipient.phone_number;

      // Format phone number
      if (phoneNumber) {
        phoneNumber = phoneNumber.replace(/\D/g, '');
        if (!phoneNumber.startsWith('91') && phoneNumber.length === 10) {
          phoneNumber = '91' + phoneNumber;
        }
        phoneNumber = phoneNumber + '@s.whatsapp.net';
      }

      console.log(`📤 Sending message to ${phoneNumber} via session ${sessionId}`);

      let result;

      // Send message based on type
      if (campaign.template_id) {
        console.log(`📄 Processing template ID: ${campaign.template_id} for campaign: ${campaign.name}`);

        // Use message processor service with contact variables
        const templateVariables = {
          user_phone: phoneNumber.replace('@s.whatsapp.net', ''),
          campaign_name: campaign.name,
          name: recipient.name || '',
          phone: recipient.phone_number || '',
          email: recipient.email || '',
          company: recipient.company || '',
          position: recipient.position || '',
          var1: recipient.var1 || '',
          var2: recipient.var2 || '',
          var3: recipient.var3 || '',
          var4: recipient.var4 || '',
          var5: recipient.var5 || '',
          var6: recipient.var6 || '',
          var7: recipient.var7 || '',
          var8: recipient.var8 || '',
          var9: recipient.var9 || '',
          var10: recipient.var10 || ''
        };

        console.log('🔍 SCHEDULER - Template variables being passed:', templateVariables);

        const templateResult = await this.messageProcessor.processTemplate(campaign.template_id, templateVariables);

        if (templateResult.success) {
          console.log(`✅ Template processed successfully: ${templateResult.type}`);

          // Format message content using message processor (this handles mixed_buttons correctly)
          const formattedContent = this.messageProcessor.formatMessageContent(
            templateResult.content,
            templateResult.type,
            templateResult.metadata || {}
          );

          console.log(`📤 Sending formatted content with type: ${templateResult.type}`);
          console.log(`🔍 Content keys: ${Object.keys(formattedContent)}`);

          // Send message using the same approach as Auto Reply and Chatbot
          result = await this.whatsappService.sendMessage(
            sessionId,
            phoneNumber,
            formattedContent,
            templateResult.type
          );
        } else {
          console.log(`❌ Template processing failed: ${templateResult.error}`);
          result = { success: false, error: `Template processing failed: ${templateResult.error}` };
        }
      } else {
        // Handle text message with optional attachment and variable replacement
        let messageContent = campaign.message_content || '';

        console.log('🔍 SCHEDULER - Message content before replacement:', messageContent);
        console.log('🔍 SCHEDULER - Recipient data for replacement:', {
          name: recipient.name,
          var1: recipient.var1,
          var2: recipient.var2,
          var3: recipient.var3
        });

        // Replace contact variables in message content
        for (let i = 1; i <= 10; i++) {
          const varName = `var${i}`;
          const varValue = recipient[varName] || '';
          messageContent = messageContent.replace(new RegExp(`\\{\\{${varName}\\}\\}`, 'g'), varValue);
        }

        // Replace common contact fields
        messageContent = messageContent.replace(/\{\{name\}\}/g, recipient.name || '');
        messageContent = messageContent.replace(/\{\{phone\}\}/g, recipient.phone_number || '');
        messageContent = messageContent.replace(/\{\{email\}\}/g, recipient.email || '');
        messageContent = messageContent.replace(/\{\{company\}\}/g, recipient.company || '');
        messageContent = messageContent.replace(/\{\{position\}\}/g, recipient.position || '');

        console.log('🔍 SCHEDULER - Message content after replacement:', messageContent);

        let attachmentData = null;
        try {
          if (campaign.attachment_data) {
            attachmentData = JSON.parse(campaign.attachment_data);
          }
        } catch (e) {
          console.log('No attachment data found or invalid format');
        }

        if (attachmentData && attachmentData.file && attachmentData.type) {
          console.log(`📎 Sending text message with ${attachmentData.type} attachment`);
          result = await this.whatsappService.sendMessage(
            sessionId,
            phoneNumber,
            {
              [attachmentData.type]: { url: attachmentData.file },
              caption: messageContent
            },
            attachmentData.type
          );
        } else {
          result = await this.whatsappService.sendMessage(
            sessionId,
            phoneNumber,
            messageContent,
            'text'
          );
        }
      }

      // Update recipient status
      if (result.success) {
        await this.databaseService.query(
          'UPDATE bulk_campaign_recipients SET status = ?, sent_at = CURRENT_TIMESTAMP, message_id = ? WHERE id = ?',
          ['sent', result.messageId || null, recipient.id]
        );

        // Log to message history
        await this.databaseService.query(`
          INSERT INTO message_history (
            session_id, contact_phone, message_id, direction, message_type,
            content, timestamp, status, campaign_id, template_id, created_at
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        `, [
          sessionId,
          recipient.phone_number,
          result.messageId || null,
          'outgoing',
          campaign.message_type || 'text',
          campaign.message_content,
          new Date().toISOString(),
          'sent',
          campaign.id,
          campaign.template_id || null
        ]);

        console.log(`✅ Message sent successfully to ${phoneNumber}`);
      } else {
        await this.databaseService.query(
          'UPDATE bulk_campaign_recipients SET status = ?, error_message = ? WHERE id = ?',
          ['failed', result.error || 'Unknown error', recipient.id]
        );
        console.log(`❌ Failed to send message to ${phoneNumber}: ${result.error}`);
      }

    } catch (error) {
      console.error(`❌ Error sending message to recipient ${recipient.id}:`, error);
      await this.databaseService.query(
        'UPDATE bulk_campaign_recipients SET status = ?, error_message = ? WHERE id = ?',
        ['failed', error.message, recipient.id]
      );
    }
  }



  /**
   * Get scheduler status
   */
  getStatus() {
    return {
      isRunning: this.isRunning,
      activeProcesses: this.activeProcesses.size,
      checkInterval: this.checkInterval
    };
  }

  /**
   * Manually trigger a campaign check
   */
  async triggerCheck() {
    console.log('🔄 Manually triggering campaign check...');
    await this.checkScheduledCampaigns();
  }
}

module.exports = CampaignSchedulerService;
