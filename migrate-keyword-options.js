const initSqlJs = require('sql.js');
const fs = require('fs');
const path = require('path');

async function migrateKeywordOptions() {
  console.log('🔄 Migrating chatbot flows to add keyword matching options...');
  
  // Migrate both development and production databases
  const databases = [
    {
      name: 'Development',
      path: path.join(process.cwd(), 'data', 'leadwave.db')
    },
    {
      name: 'Production',
      path: path.join(process.env.APPDATA, 'Electron', 'leadwave.db')
    }
  ];
  
  for (const db of databases) {
    console.log(`\n📁 Migrating ${db.name} database: ${db.path}`);
    
    if (!fs.existsSync(db.path)) {
      console.log(`   ❌ Database not found, skipping...`);
      continue;
    }
    
    try {
      const SQL = await initSqlJs();
      const filebuffer = fs.readFileSync(db.path);
      const database = new SQL.Database(filebuffer);
      
      // Check current table schema
      const tableInfo = database.exec('PRAGMA table_info(chatbot_flows)');
      
      if (tableInfo.length === 0) {
        console.log('   ❌ chatbot_flows table not found, skipping...');
        database.close();
        continue;
      }
      
      const columns = tableInfo[0].values.map(row => row[1]); // column names
      console.log(`   📋 Current columns: ${columns.join(', ')}`);
      
      let needsMigration = false;
      
      // Check if new columns exist
      if (!columns.includes('keyword_match_type')) {
        console.log('   🔧 Adding keyword_match_type column...');
        database.exec("ALTER TABLE chatbot_flows ADD COLUMN keyword_match_type TEXT DEFAULT 'contains'");
        needsMigration = true;
      }
      
      if (!columns.includes('keyword_case_sensitive')) {
        console.log('   🔧 Adding keyword_case_sensitive column...');
        database.exec("ALTER TABLE chatbot_flows ADD COLUMN keyword_case_sensitive BOOLEAN DEFAULT 0");
        needsMigration = true;
      }
      
      if (needsMigration) {
        // Save the updated database
        const data = database.export();
        fs.writeFileSync(db.path, Buffer.from(data));
        console.log('   ✅ Migration completed successfully');
        
        // Verify the migration
        const verifyInfo = database.exec('PRAGMA table_info(chatbot_flows)');
        const newColumns = verifyInfo[0].values.map(row => row[1]);
        console.log(`   📋 Updated columns: ${newColumns.join(', ')}`);
        
        // Show existing flows and their new default values
        const flows = database.exec('SELECT id, name, keyword_match_type, keyword_case_sensitive FROM chatbot_flows');
        if (flows.length > 0 && flows[0].values.length > 0) {
          console.log('   📊 Existing flows updated:');
          flows[0].values.forEach(row => {
            const [id, name, matchType, caseSensitive] = row;
            console.log(`     - "${name}": match_type=${matchType}, case_sensitive=${caseSensitive}`);
          });
        }
        
      } else {
        console.log('   ✅ Database already up to date');
      }
      
      database.close();
      
    } catch (error) {
      console.error(`   ❌ Error migrating ${db.name} database:`, error.message);
    }
  }
  
  console.log('\n🎉 Migration complete!');
  console.log('\n📱 New keyword matching options available:');
  console.log('   • Match Type: Contains, Exact Match, Starts With, Ends With');
  console.log('   • Case Sensitivity: Enable/disable case-sensitive matching');
  console.log('\n💡 These options will help solve your PHP/php keyword matching issue!');
  console.log('   Try setting "Exact Match" and "Match case" for precise keyword matching.');
}

migrateKeywordOptions().catch(console.error);
