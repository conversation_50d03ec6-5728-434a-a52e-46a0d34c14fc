const initSqlJs = require('sql.js');
const fs = require('fs');
const path = require('path');

async function checkTableStructure() {
  console.log('🔍 Checking table structure...');
  
  const dbPath = path.join(process.env.APPDATA, 'Electron', 'leadwave.db');
  
  if (!fs.existsSync(dbPath)) {
    console.log('❌ Database not found');
    return;
  }
  
  const SQL = await initSqlJs();
  const filebuffer = fs.readFileSync(dbPath);
  const db = new SQL.Database(filebuffer);
  
  try {
    // Check chatbot_nodes table structure
    console.log('\n📋 CHATBOT_NODES TABLE STRUCTURE:');
    const tableInfo = db.exec("PRAGMA table_info(chatbot_nodes)");
    if (tableInfo.length > 0) {
      console.log('Columns:');
      tableInfo[0].values.forEach(row => {
        const [cid, name, type, notnull, dflt_value, pk] = row;
        console.log(`  ${name} (${type})`);
      });
    }
    
    // Check actual data
    console.log('\n📋 ACTUAL CHATBOT_NODES DATA:');
    const nodes = db.exec('SELECT * FROM chatbot_nodes LIMIT 3');
    if (nodes.length > 0) {
      console.log('Columns:', nodes[0].columns.join(', '));
      nodes[0].values.forEach((row, index) => {
        console.log(`Row ${index + 1}:`, row.join(' | '));
      });
    }
    
    // Check chatbot_flows table structure
    console.log('\n📋 CHATBOT_FLOWS TABLE STRUCTURE:');
    const flowTableInfo = db.exec("PRAGMA table_info(chatbot_flows)");
    if (flowTableInfo.length > 0) {
      console.log('Columns:');
      flowTableInfo[0].values.forEach(row => {
        const [cid, name, type, notnull, dflt_value, pk] = row;
        console.log(`  ${name} (${type})`);
      });
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    db.close();
  }
}

checkTableStructure().catch(console.error);
