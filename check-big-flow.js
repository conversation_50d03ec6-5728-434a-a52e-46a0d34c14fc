#!/usr/bin/env node

const DatabaseService = require('./src/services/database.service.js');

async function checkBigFlow() {
  console.log('🔍 Checking BIG Flow Configuration...\n');
  
  try {
    const database = new DatabaseService();
    await database.initialize();
    console.log('✅ Database connected');

    // Check all flows
    console.log('\n📋 All Chatbot Flows:');
    const allFlows = await database.query(`
      SELECT * FROM chatbot_flows 
      ORDER BY created_at DESC
    `);

    if (allFlows.success && allFlows.data.length > 0) {
      allFlows.data.forEach((flow, index) => {
        console.log(`\n${index + 1}. Flow: "${flow.name}"`);
        console.log(`   ID: ${flow.id}`);
        console.log(`   Active: ${flow.is_active ? 'YES' : 'NO'}`);
        console.log(`   Session ID: ${flow.session_id}`);
        console.log(`   Keywords: "${flow.trigger_keywords}"`);
        console.log(`   Match Type: ${flow.keyword_match_type || 'contains'}`);
        console.log(`   Case Sensitive: ${flow.keyword_case_sensitive ? 'YES' : 'NO'}`);
        console.log(`   Description: ${flow.description || 'None'}`);
        console.log(`   Cooldown: ${flow.cooldown_minutes || 0} minutes`);
        console.log(`   Created: ${flow.created_at}`);
      });
    } else {
      console.log('❌ No chatbot flows found');
    }

    // Check for BIG flow specifically
    console.log('\n🔍 Searching for BIG Flow:');
    const bigFlows = await database.query(`
      SELECT * FROM chatbot_flows 
      WHERE trigger_keywords LIKE '%BIG%' OR name LIKE '%BIG%'
      ORDER BY created_at DESC
    `);

    if (bigFlows.success && bigFlows.data.length > 0) {
      console.log(`Found ${bigFlows.data.length} flow(s) with BIG keyword:`);
      bigFlows.data.forEach((flow, index) => {
        console.log(`\n${index + 1}. "${flow.name}"`);
        console.log(`   Keywords: "${flow.trigger_keywords}"`);
        console.log(`   Case Sensitive: ${flow.keyword_case_sensitive ? 'YES' : 'NO'}`);
        console.log(`   Active: ${flow.is_active ? 'YES' : 'NO'}`);
        console.log(`   Session: ${flow.session_id}`);
      });
    } else {
      console.log('❌ No flows found with BIG keyword');
    }

    // Check WhatsApp sessions
    console.log('\n📱 WhatsApp Sessions:');
    const sessions = await database.query(`
      SELECT session_id, name, status, is_active 
      FROM whatsapp_sessions 
      ORDER BY created_at DESC
    `);

    if (sessions.success && sessions.data.length > 0) {
      sessions.data.forEach((session, index) => {
        console.log(`${index + 1}. ${session.session_id} - ${session.name} (${session.status}) - Active: ${session.is_active ? 'YES' : 'NO'}`);
      });
    } else {
      console.log('❌ No WhatsApp sessions found');
    }

    // Check for nodes in BIG flow
    if (bigFlows.success && bigFlows.data.length > 0) {
      const bigFlow = bigFlows.data[0];
      console.log(`\n🔗 Checking nodes for flow "${bigFlow.name}" (ID: ${bigFlow.id}):`);
      
      const nodes = await database.query(`
        SELECT * FROM chatbot_nodes 
        WHERE flow_id = ? 
        ORDER BY position ASC
      `, [bigFlow.id]);

      if (nodes.success && nodes.data.length > 0) {
        console.log(`Found ${nodes.data.length} node(s):`);
        nodes.data.forEach((node, index) => {
          console.log(`  ${index + 1}. "${node.name}" (${node.node_type}) - Position: ${node.position}`);
        });
      } else {
        console.log('❌ No nodes found for this flow - THIS COULD BE THE PROBLEM!');
      }
    }

    await database.close();
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

checkBigFlow();
