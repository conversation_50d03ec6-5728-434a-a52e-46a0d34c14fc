const initSqlJs = require('sql.js');
const fs = require('fs');
const path = require('path');

async function cleanupActiveConversations() {
  console.log('🧹 Cleaning up active conversations...');
  
  const dbPath = path.join(process.env.APPDATA, 'Electron', 'leadwave.db');
  const SQL = await initSqlJs();
  const filebuffer = fs.readFileSync(dbPath);
  const db = new SQL.Database(filebuffer);
  
  try {
    // Check active conversations
    const activeConversations = db.exec('SELECT id, flow_id, user_phone, started_at FROM chatbot_conversations WHERE status = "active"');
    
    if (activeConversations.length > 0 && activeConversations[0].values.length > 0) {
      console.log(`\n⚠️  Found ${activeConversations[0].values.length} active conversation(s):`);
      activeConversations[0].values.forEach((row, index) => {
        const [id, flowId, userPhone, startedAt] = row;
        console.log(`   ${index + 1}. Conversation ${id}: Flow ${flowId}, User ${userPhone}, Started: ${startedAt}`);
      });
      
      console.log('\n🔧 Marking them as completed...');
      db.exec('UPDATE chatbot_conversations SET status = "completed", completed_at = CURRENT_TIMESTAMP WHERE status = "active"');
      
      // Save the database
      const data = db.export();
      fs.writeFileSync(dbPath, Buffer.from(data));
      
      console.log('✅ All active conversations marked as completed');
      
    } else {
      console.log('\n✅ No active conversations found');
    }
    
    // Verify cleanup
    const verifyCleanup = db.exec('SELECT COUNT(*) as count FROM chatbot_conversations WHERE status = "active"');
    const activeCount = verifyCleanup[0].values[0][0];
    
    console.log(`\n🧪 Verification: ${activeCount} active conversations remaining`);
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    db.close();
  }
  
  console.log('\n🎉 Cleanup complete! Now try sending "PHP" from WhatsApp.');
}

cleanupActiveConversations().catch(console.error);
