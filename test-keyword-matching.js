const initSqlJs = require('sql.js');
const fs = require('fs');
const path = require('path');

async function testKeywordMatching() {
  console.log('🧪 Testing keyword matching with your exact scenario...');
  
  // Simulate the keyword matching logic from the code
  function matchesKeyword(messageText, keyword, matchType = 'contains') {
    const text = messageText.toLowerCase().trim();
    const key = keyword.toLowerCase().trim();

    switch (matchType) {
      case 'exact':
        return text === key;
      case 'starts_with':
        return text.startsWith(key);
      case 'ends_with':
        return text.endsWith(key);
      case 'contains':
      default:
        return text.includes(key);
    }
  }
  
  // Your exact setup
  const triggerKeywords = "php, development";
  const keywords = triggerKeywords.toLowerCase().split(',').map(k => k.trim());
  
  console.log(`\n📋 Setup:`);
  console.log(`  Trigger keywords: "${triggerKeywords}"`);
  console.log(`  Parsed keywords: [${keywords.map(k => `"${k}"`).join(', ')}]`);
  
  // Test different message formats
  const testMessages = [
    "php",                    // Manual typing (lowercase)
    "PHP",                    // Manual typing (uppercase)
    "development",            // Manual typing (lowercase)
    "Development",            // Manual typing (uppercase)
    "PHP\nDevelopment",       // List selection (what you see in WhatsApp)
    "php\ndevelopment",       // List selection (after lowercasing)
    "PHP Development",        // List selection (space instead of newline)
    "php development",        // List selection (space, lowercase)
  ];
  
  console.log(`\n🧪 Testing keyword matching:`);
  
  testMessages.forEach(message => {
    console.log(`\n  Message: "${message.replace(/\n/g, '\\n')}"`);
    
    const messageText = message.toLowerCase().trim();
    console.log(`  Processed: "${messageText.replace(/\n/g, '\\n')}"`);
    
    const hasMatch = keywords.some(keyword => {
      const match = matchesKeyword(messageText, keyword, 'contains');
      console.log(`    "${keyword}" matches: ${match ? '✅' : '❌'}`);
      return match;
    });
    
    console.log(`  Overall match: ${hasMatch ? '🎯 YES' : '❌ NO'}`);
  });
  
  // Test the exact database content
  console.log(`\n🔍 Checking actual database content...`);
  
  const dbPath = path.join(process.env.APPDATA, 'Electron', 'leadwave.db');
  
  if (fs.existsSync(dbPath)) {
    const SQL = await initSqlJs();
    const filebuffer = fs.readFileSync(dbPath);
    const db = new SQL.Database(filebuffer);
    
    try {
      // Get the actual flow keywords
      const flows = db.exec('SELECT name, trigger_keywords FROM chatbot_flows WHERE is_active = 1');
      if (flows.length > 0) {
        console.log(`\n📋 Actual database flows:`);
        flows[0].values.forEach((row, index) => {
          const [name, triggerKeywords] = row;
          const keywords = triggerKeywords.toLowerCase().split(',').map(k => k.trim());
          console.log(`  Flow "${name}": keywords [${keywords.map(k => `"${k}"`).join(', ')}]`);
        });
      }
      
      // Get recent messages to see the exact format
      console.log(`\n📨 Recent messages from database:`);
      const messages = db.exec('SELECT content, message_type, direction FROM message_history ORDER BY id DESC LIMIT 5');
      if (messages.length > 0) {
        messages[0].values.forEach((row, index) => {
          const [content, type, direction] = row;
          console.log(`  Message ${index + 1}: "${content.replace(/\n/g, '\\n')}" (${type}, ${direction})`);
        });
      }
      
    } catch (error) {
      console.error('❌ Database error:', error.message);
    } finally {
      db.close();
    }
  }
  
  console.log(`\n💡 Analysis:`);
  console.log(`  - Manual "php" or "development" should work ✅`);
  console.log(`  - List selection "PHP\\nDevelopment" should also work ✅`);
  console.log(`  - If it's not working, the issue is likely elsewhere:`);
  console.log(`    1. Session ID mismatch`);
  console.log(`    2. Active conversation blocking new triggers`);
  console.log(`    3. Application not processing messages properly`);
  console.log(`    4. Database changes being overwritten`);
}

testKeywordMatching().catch(console.error);
