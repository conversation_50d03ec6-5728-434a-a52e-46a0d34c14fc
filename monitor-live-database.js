const DatabaseService = require('./src/services/database.service');
const path = require('path');

async function monitorLiveDatabase() {
  console.log('🔍 Monitoring Live Database Operations...');
  
  // Create a database service instance exactly like the app does
  const db = new DatabaseService();
  
  // Log the database path that will be used
  console.log(`📂 Database path that will be used: ${db.dbPath}`);
  
  try {
    await db.initialize();
    console.log('✅ Database initialized successfully');
    
    // Check if the database file actually exists
    const fs = require('fs');
    if (fs.existsSync(db.dbPath)) {
      const stats = fs.statSync(db.dbPath);
      console.log(`📊 Database file size: ${(stats.size / 1024).toFixed(2)} KB`);
      console.log(`📅 Last modified: ${stats.mtime.toISOString()}`);
    } else {
      console.log('❌ Database file does not exist at expected path');
    }
    
    // Test basic database operations
    console.log('\n🧪 Testing database operations...');
    
    // Check tables
    const tables = await db.all(`
      SELECT name FROM sqlite_master 
      WHERE type='table' 
      ORDER BY name
    `);
    
    if (tables.success && tables.data) {
      console.log(`📋 Found ${tables.data.length} tables:`);
      tables.data.forEach(table => {
        console.log(`  - ${table.name}`);
      });
    } else {
      console.log('❌ Failed to get table list');
    }
    
    // Check for sessions
    console.log('\n📱 Checking WhatsApp sessions...');
    const sessions = await db.all('SELECT * FROM whatsapp_sessions');
    if (sessions.success && sessions.data) {
      console.log(`Found ${sessions.data.length} sessions:`);
      sessions.data.forEach(session => {
        console.log(`  - ${session.name}: ${session.status} (Active: ${session.is_active})`);
      });
    } else {
      console.log('❌ Failed to get sessions or no sessions found');
    }
    
    // Check for chatbot flows
    console.log('\n🤖 Checking chatbot flows...');
    const flows = await db.all('SELECT * FROM chatbot_flows');
    if (flows.success && flows.data) {
      console.log(`Found ${flows.data.length} flows:`);
      flows.data.forEach(flow => {
        console.log(`  - "${flow.name}": "${flow.trigger_keywords}" (Active: ${flow.is_active})`);
      });
    } else {
      console.log('❌ Failed to get flows or no flows found');
    }
    
    // Monitor for changes
    console.log('\n🔄 Starting real-time monitoring...');
    console.log('📱 Now try selecting from the list in WhatsApp and watch for activity...');
    console.log('🛑 Press Ctrl+C to stop monitoring');
    
    let lastMessageCount = 0;
    let lastConversationCount = 0;
    
    // Get initial counts
    const initialMessages = await db.all('SELECT COUNT(*) as count FROM messages');
    if (initialMessages.success && initialMessages.data) {
      lastMessageCount = initialMessages.data[0].count;
    }
    
    const initialConversations = await db.all('SELECT COUNT(*) as count FROM chatbot_conversations');
    if (initialConversations.success && initialConversations.data) {
      lastConversationCount = initialConversations.data[0].count;
    }
    
    console.log(`📊 Starting counts - Messages: ${lastMessageCount}, Conversations: ${lastConversationCount}`);
    
    const monitor = setInterval(async () => {
      try {
        // Check for new messages
        const currentMessages = await db.all('SELECT COUNT(*) as count FROM messages');
        if (currentMessages.success && currentMessages.data) {
          const currentMessageCount = currentMessages.data[0].count;
          if (currentMessageCount > lastMessageCount) {
            console.log(`\n🆕 NEW MESSAGES DETECTED! Count: ${lastMessageCount} → ${currentMessageCount}`);
            
            // Get the new messages
            const newMessages = await db.all(`
              SELECT * FROM messages 
              ORDER BY id DESC 
              LIMIT ${currentMessageCount - lastMessageCount}
            `);
            
            if (newMessages.success && newMessages.data) {
              newMessages.data.forEach(msg => {
                console.log(`  📨 ${msg.created_at}: ${msg.sender_phone} → "${msg.content}"`);
                console.log(`     Type: ${msg.message_type}, Direction: ${msg.direction}`);
              });
            }
            
            lastMessageCount = currentMessageCount;
          }
        }
        
        // Check for new conversations
        const currentConversations = await db.all('SELECT COUNT(*) as count FROM chatbot_conversations');
        if (currentConversations.success && currentConversations.data) {
          const currentConversationCount = currentConversations.data[0].count;
          if (currentConversationCount > lastConversationCount) {
            console.log(`\n🤖 NEW CHATBOT CONVERSATION! Count: ${lastConversationCount} → ${currentConversationCount}`);
            lastConversationCount = currentConversationCount;
          }
        }
        
      } catch (error) {
        console.error('❌ Monitor error:', error.message);
      }
    }, 2000);
    
    // Handle Ctrl+C
    process.on('SIGINT', () => {
      console.log('\n\n🛑 Stopping monitor...');
      clearInterval(monitor);
      console.log('\n💡 SUMMARY:');
      console.log(`📂 Database used: ${db.dbPath}`);
      console.log(`📊 Final message count: ${lastMessageCount}`);
      console.log(`🤖 Final conversation count: ${lastConversationCount}`);
      process.exit(0);
    });
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

monitorLiveDatabase().catch(console.error);
