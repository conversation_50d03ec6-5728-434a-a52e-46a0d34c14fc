const {
  default: makeWASocket,
  useMultiFileAuthState,
  DisconnectReason,
  MessageType,
  MessageOptions,
  Browsers,
  delay,
  generateWAMessageFromContent,
  prepareWAMessageMedia,
  proto,
  downloadContentFromMessage,
  getContentType,
  makeInMemoryStore
} = require('@itsukichan/baileys');
const { Boom } = require('@hapi/boom');
const pino = require('pino');
const QRCode = require('qrcode');
const path = require('path');
const fs = require('fs');
const os = require('os');
const { EventEmitter } = require('events');
const NodeCache = require('node-cache'); // Add NodeCache import

// Development-aware logging
const isDev = process.env.NODE_ENV === 'development' || !process.env.NODE_ENV;
const devLog = (...args) => { if (isDev) console.log(...args); };
const devWarn = (...args) => { if (isDev) console.warn(...args); };
const devError = (...args) => { if (isDev) console.error(...args); };

class WhatsAppService extends EventEmitter {
  constructor(databaseService = null) {
    super();
    this.sessions = new Map(); // Map of sessionId -> WhatsApp socket
    this.sessionStates = new Map(); // Map of sessionId -> session state
    this.stores = new Map(); // Map of sessionId -> Baileys store
    this.authDir = path.join(os.homedir(), 'Lead Wave', 'auth_sessions');
    this.logger = pino({ level: 'silent' }); // Disable logging to prevent file descriptor issues
    this.databaseService = databaseService; // Inject database service

    // Add a map to track manual disconnections to prevent conflicts with automatic event handling
    this.manualDisconnections = new Set(); // Track sessions being manually disconnected

    // Add file operation locks to prevent EBADF errors
    this.fileOperationLocks = new Map(); // Track ongoing file operations
    this.isShuttingDown = false; // Track shutdown state

    // Ensure auth directory exists
    if (!fs.existsSync(this.authDir)) {
      fs.mkdirSync(this.authDir, { recursive: true });
    }
  }

  /**
   * Set database service (for late injection)
   */
  setDatabaseService(databaseService) {
    this.databaseService = databaseService;
  }

  /**
   * Safe file operation wrapper to prevent EBADF errors
   */
  async safeFileOperation(operationKey, operation) {
    if (this.isShuttingDown) {
      throw new Error('Service is shutting down, cannot perform file operations');
    }

    // Check if operation is already in progress
    if (this.fileOperationLocks.has(operationKey)) {
      await this.fileOperationLocks.get(operationKey);
    }

    // Create new operation promise
    const operationPromise = (async () => {
      try {
        return await operation();
      } catch (error) {
        if (error.code === 'EBADF' || error.message.includes('bad file descriptor')) {
          devWarn(`File descriptor error for ${operationKey}, retrying...`);
          // Wait a bit and retry once
          await new Promise(resolve => setTimeout(resolve, 100));
          return await operation();
        }
        throw error;
      } finally {
        this.fileOperationLocks.delete(operationKey);
      }
    })();

    this.fileOperationLocks.set(operationKey, operationPromise);
    return operationPromise;
  }

  /**
   * Initialize store for a session
   */
  initializeStore(sessionId) {
    if (!this.stores.has(sessionId)) {
      const storeDir = path.join(this.authDir, sessionId);
      const storeFile = path.join(storeDir, 'baileys_store.json');

      const store = makeInMemoryStore({
        logger: pino({ level: 'silent' })
      });

      // Try to read existing store data
      if (fs.existsSync(storeFile)) {
        try {
          store.readFromFile(storeFile);
        } catch (error) {
          // Silently handle store loading errors
        }
      }

      // Disable automatic store saving to prevent file descriptor issues during initialization
      // Store will be saved manually when needed
      let saveInterval = null;

      // Only enable auto-save after app is fully initialized
      setTimeout(() => {
        saveInterval = setInterval(() => {
          try {
            if (!fs.existsSync(storeDir)) {
              fs.mkdirSync(storeDir, { recursive: true });
            }
            store.writeToFile(storeFile);
          } catch (error) {
            devWarn(`Failed to save store data for session ${sessionId}:`, error);
          }
        }, 60000); // Save every minute instead of 30 seconds
      }, 10000); // Wait 10 seconds after initialization

      // Store the interval ID so we can clear it later
      store._saveInterval = saveInterval;

      this.stores.set(sessionId, store);
      return store;
    }

    return this.stores.get(sessionId);
  }

  /**
   * Clean up store for a session
   */
  cleanupStore(sessionId) {
    const store = this.stores.get(sessionId);
    if (store) {
      // Clear the save interval
      if (store._saveInterval) {
        clearInterval(store._saveInterval);
      }

      // Save final state
      try {
        const storeDir = path.join(this.authDir, sessionId);
        const storeFile = path.join(storeDir, 'baileys_store.json');
        if (!fs.existsSync(storeDir)) {
          fs.mkdirSync(storeDir, { recursive: true });
        }
        store.writeToFile(storeFile);
      } catch (error) {
        this.logger.warn(`Failed to save final store data for session ${sessionId}:`, error);
      }

      this.stores.delete(sessionId);
    }
  }

  /**
   * Restore all existing sessions from database on app startup
   */
  async restoreAllSessions() {
    try {
      if (!this.databaseService) {
        this.logger.warn('Database service not available for session restoration');
        return;
      }

      this.logger.info('🔄 Restoring existing WhatsApp sessions...');
      
      // Get all active sessions from database (including disconnected ones for reconnection)
      const sessions = await this.databaseService.query(`
        SELECT session_id, status FROM whatsapp_sessions 
        WHERE is_active = 1 AND status IN ('connected', 'qr_ready', 'connecting', 'disconnected')
        ORDER BY created_at DESC
      `);

      this.logger.info(`📊 Database query result: ${JSON.stringify(sessions)}`);

      if (!sessions || !sessions.success || !sessions.data || sessions.data.length === 0) {
        this.logger.info('❌ No active sessions found to restore');
        return;
      }

      const sessionData = sessions.data;
      this.logger.info(`✅ Found ${sessionData.length} sessions to restore`);

      for (const session of sessionData) {
        const sessionId = session.session_id;
        const status = session.status;
        
        this.logger.info(`Restoring session ${sessionId} with status: ${status}`);
        
        if (status === 'connected') {
          // For connected sessions, try to restore the connection
          await this.restoreSession(sessionId);
        } else if (status === 'disconnected') {
          // For disconnected sessions, restore but they'll need QR scan
          this.logger.info(`Session ${sessionId} was disconnected, restoring for QR reconnection`);
          await this.restoreSession(sessionId);
        } else {
          // For other statuses, may need new authentication
          this.logger.info(`Session ${sessionId} needs re-authentication (status: ${status})`);
        }
      }

      this.logger.info('✅ Session restoration completed');
    } catch (error) {
      this.logger.error('Error restoring sessions:', error);
    }
  }

  /**
   * Create a new WhatsApp session
   * @param {string} sessionId - Unique identifier for the session
   * @returns {Promise<{success: boolean, qrCode?: string, message?: string}>}
   */
  async createSession(sessionId) {
    try {
      if (this.sessions.has(sessionId)) {
        return {
          success: false,
          message: 'Session already exists'
        };
      }

      // Use setImmediate to prevent blocking the UI thread
      return new Promise((resolve, reject) => {
        setImmediate(async () => {
          try {
            const sessionDir = path.join(this.authDir, sessionId);
            if (!fs.existsSync(sessionDir)) {
              fs.mkdirSync(sessionDir, { recursive: true });
            }

            // Use setTimeout to make auth state creation non-blocking
            setTimeout(async () => {
              try {
                const { state, saveCreds } = await this.safeFileOperation(
                  `auth-${sessionId}`,
                  () => useMultiFileAuthState(sessionDir)
                );

                // Initialize store for this session
                const store = this.initializeStore(sessionId);

                const socket = makeWASocket({
                  auth: state,
                  logger: this.logger,
                  printQRInTerminal: false,
                  browser: Browsers.ubuntu('Lead Wave Desktop'),
                  generateHighQualityLinkPreview: true,
                  markOnlineOnConnect: false,
                  syncFullHistory: false,
                  defaultQueryTimeoutMs: 60000,
                  getMessage: async (key) => {
                    if (store) {
                      const msg = await store.loadMessage(key.remoteJid, key.id);
                      return msg?.message || undefined;
                    }
                    return undefined;
                  }
                });

                // Bind store to socket events
                store.bind(socket.ev);

                this.sessions.set(sessionId, socket);
                this.sessionStates.set(sessionId, {
                  id: sessionId,
                  status: 'connecting',
                  qrCode: null,
                  lastSeen: new Date(),
                  phoneNumber: null,
                  profilePicture: null,
                  isLoggedIn: false,
                  usingPairingCode: false,
                  pairingPhoneNumber: null
                });

                // Handle connection updates
                socket.ev.on('connection.update', async (update) => {
                  await this.handleConnectionUpdate(sessionId, update);
                });

                // Handle credential updates
                socket.ev.on('creds.update', saveCreds);

                // Handle incoming messages
                socket.ev.on('messages.upsert', async (messageUpdate) => {
                  await this.handleIncomingMessages(sessionId, messageUpdate);
                });

                // Handle contacts updates
                socket.ev.on('contacts.update', async (contacts) => {
                  await this.handleContactsUpdate(sessionId, contacts);
                });

                // Handle calls (both incoming and outgoing)
                socket.ev.on('call', async (calls) => {
                  await this.handleCalls(sessionId, calls);
                });

                // Handle presence updates
                socket.ev.on('presence.update', async (presence) => {
                  await this.handlePresenceUpdate(sessionId, presence);
                });

                this.logger.info(`Session ${sessionId} created successfully`);

                resolve({
                  success: true,
                  message: 'Session created successfully'
                });

              } catch (error) {
                this.logger.error(`Error in socket creation for ${sessionId}:`, error);

                // Clean up on error
                this.sessions.delete(sessionId);
                this.sessionStates.delete(sessionId);

                resolve({
                  success: false,
                  message: error.message
                });
              }
            }, 0); // Use setTimeout with 0ms to yield control back to event loop

          } catch (error) {
            this.logger.error(`Error creating session ${sessionId}:`, error);

            // Clean up on error
            this.sessions.delete(sessionId);
            this.sessionStates.delete(sessionId);

            resolve({
              success: false,
              message: error.message
            });
          }
        });
      });

    } catch (error) {
      this.logger.error(`Error creating session ${sessionId}:`, error);

      return {
        success: false,
        message: error.message
      };
    }
  }

  /**
   * Restore existing session from auth files
   */
  async restoreSession(sessionId) {
    try {
      if (this.sessions.has(sessionId)) {
        this.logger.info(`Session ${sessionId} already exists in memory`);
        return { success: true, message: 'Session already loaded' };
      }

      const sessionDir = path.join(this.authDir, sessionId);
      if (!fs.existsSync(sessionDir)) {
        this.logger.warn(`No auth files found for session ${sessionId}`);
        return { success: false, message: 'No auth files found' };
      }

      this.logger.info(`Restoring session ${sessionId} from auth files...`);

      const { state, saveCreds } = await this.safeFileOperation(
        `restore-${sessionId}`,
        () => useMultiFileAuthState(sessionDir)
      );
      
      // Check if session has valid credentials
      if (!state.creds || !state.creds.noiseKey) {
        return { success: false, message: 'Invalid credentials' };
      }
      
      // Initialize store for this session
      const store = this.initializeStore(sessionId);

      const socket = makeWASocket({
        auth: state,
        logger: this.logger,
        printQRInTerminal: false,
        browser: Browsers.ubuntu('Lead Wave Desktop'),
        generateHighQualityLinkPreview: true,
        markOnlineOnConnect: false,
        syncFullHistory: false,
        defaultQueryTimeoutMs: 60000,
        getMessage: async (key) => {
          if (store) {
            const msg = await store.loadMessage(key.remoteJid, key.id);
            return msg?.message || undefined;
          }
          return undefined;
        }
      });

      // Bind store to socket events
      store.bind(socket.ev);

      this.sessions.set(sessionId, socket);

      // Set initial state as connecting (will be updated by connection.update)
      this.sessionStates.set(sessionId, {
        id: sessionId,
        status: 'connecting',
        qrCode: null,
        lastSeen: new Date(),
        phoneNumber: null,
        profilePicture: null,
        isLoggedIn: false,
        usingPairingCode: false,
        pairingPhoneNumber: null
      });

      // Handle connection updates
      socket.ev.on('connection.update', async (update) => {
        await this.handleConnectionUpdate(sessionId, update);
      });

      // Handle credential updates
      socket.ev.on('creds.update', saveCreds);

      // Handle incoming messages
      socket.ev.on('messages.upsert', async (messageUpdate) => {
        await this.handleIncomingMessages(sessionId, messageUpdate);
        // Also check for call log messages (outgoing calls)
        await this.handleCallLogMessages(sessionId, messageUpdate);
      });

      // Handle contacts updates
      socket.ev.on('contacts.update', async (contacts) => {
        await this.handleContactsUpdate(sessionId, contacts);
      });

      // Handle calls (both incoming and outgoing)
      socket.ev.on('call', async (calls) => {
        await this.handleCalls(sessionId, calls);
      });

      // Handle presence updates
      socket.ev.on('presence.update', async (presence) => {
        await this.handlePresenceUpdate(sessionId, presence);
      });

      this.logger.info(`Session ${sessionId} restoration initiated`);
      
      // Emit a session connected event immediately if this session was already connected
      // This helps the frontend update immediately on app startup
      setTimeout(() => {
        if (this.databaseService) {
          this.databaseService.get(`
            SELECT status, phone_number FROM whatsapp_sessions 
            WHERE session_id = ? AND status = 'connected'
          `, [sessionId]).then(dbSession => {
            if (dbSession) {
              this.logger.info(`Emitting session_connected for restored session ${sessionId}`);
              this.emit('session_connected', {
                sessionId,
                status: 'connected',
                isLoggedIn: true,
                phoneNumber: dbSession.phone_number,
                profilePicture: null,
                timestamp: new Date()
              });
            }
          }).catch(err => {
            this.logger.error(`Error checking restored session status:`, err);
          });
        }
      }, 2000); // Wait 2 seconds for connection to stabilize
      
      return { success: true, message: 'Session restoration initiated' };

    } catch (error) {
      this.logger.error(`Error restoring session ${sessionId}:`, error);
      return { success: false, message: error.message };
    }
  }

  /**
   * Handle connection updates - Following official Baileys documentation
   */
  async handleConnectionUpdate(sessionId, update) {
    const { connection, lastDisconnect, qr, isNewLogin } = update;
    const sessionState = this.sessionStates.get(sessionId);
    
    if (!sessionState) {
      this.logger.warn(`Session state not found for ${sessionId}`);
      return;
    }

    // Debug logging
    this.logger.info(`Connection update for ${sessionId}: connection=${connection}, qr=${!!qr}, isNewLogin=${isNewLogin}`);

    // Handle QR code generation - from Baileys docs
    // Only generate QR if this is a new login (no existing credentials) AND not using pairing code
    if (qr && isNewLogin !== false && !sessionState.usingPairingCode) {
      try {
        const qrCodeDataURL = await QRCode.toDataURL(qr);
        sessionState.qrCode = qrCodeDataURL;
        sessionState.status = 'qr_ready';

        this.emit('qr_code', {
          sessionId,
          qrCode: qrCodeDataURL
        });

        console.log(`🔄 QR code emitted for session ${sessionId}, data URL length: ${qrCodeDataURL.length}`);
        this.logger.info(`QR code generated for session ${sessionId}`);

        // Update database with QR code (non-blocking)
        if (this.databaseService && this.databaseService.run) {
          this.databaseService.run(`
            UPDATE whatsapp_sessions
            SET qr_code = ?, status = 'qr_ready', updated_at = CURRENT_TIMESTAMP
            WHERE session_id = ?
          `, [qrCodeDataURL, sessionId]).catch(dbError => {
            this.logger.error(`Database update error for QR code ${sessionId}:`, dbError);
          });
        }

      } catch (error) {
        this.logger.error(`Error generating QR code for ${sessionId}:`, error);
      }
    } else if (qr && sessionState.usingPairingCode) {
      this.logger.info(`QR code suppressed for session ${sessionId} - using pairing code authentication`);
      // Update session status to indicate pairing code is active
      sessionState.status = 'pairing_code_ready';
      this.sessionStates.set(sessionId, sessionState);
    }

    // Handle connection status changes
    if (connection === 'open') {
      sessionState.status = 'connected';
      sessionState.isLoggedIn = true;
      sessionState.qrCode = null; // Clear QR code when connected
      
      // Get session info from socket
      const socket = this.sessions.get(sessionId);
      let phoneNumber = null;
      let profilePicture = null;
      
      if (socket && socket.user) {
        phoneNumber = socket.user.id?.split(':')[0] || null;
        try {
          profilePicture = await socket.profilePictureUrl(socket.user.id, 'image');
        } catch (error) {
          // Profile picture might not be available
          this.logger.debug(`Could not fetch profile picture for ${sessionId}:`, error.message);
        }
      }
      
      // ALWAYS emit the session_connected event first
      this.emit('session_connected', {
        sessionId,
        status: 'connected',
        isLoggedIn: true,
        phoneNumber,
        profilePicture,
        timestamp: new Date()
      });
      
      this.logger.info(`Session ${sessionId} connected successfully${phoneNumber ? ` with phone ${phoneNumber}` : ''}`);
      
      // Update database with phone number if available (non-blocking)
      const updateQuery = phoneNumber 
        ? `UPDATE whatsapp_sessions 
           SET status = 'connected', 
               phone_number = ?,
               profile_picture = ?,
               connected_at = CURRENT_TIMESTAMP,
               qr_code = NULL,
               updated_at = CURRENT_TIMESTAMP
           WHERE session_id = ?`
        : `UPDATE whatsapp_sessions 
           SET status = 'connected', 
               connected_at = CURRENT_TIMESTAMP,
               qr_code = NULL,
               updated_at = CURRENT_TIMESTAMP
           WHERE session_id = ?`;
      
      const updateParams = phoneNumber 
        ? [phoneNumber, profilePicture, sessionId]
        : [sessionId];
      
      // Don't await this - make it non-blocking
      if (this.databaseService && this.databaseService.run) {
        this.databaseService.run(updateQuery, updateParams).catch(dbError => {
          this.logger.error(`Database update error for connected session ${sessionId}:`, dbError);
        });
      } else {
        this.logger.warn(`Database service not available for session ${sessionId} update`);
      }
      
    } else if (connection === 'connecting') {
      sessionState.status = 'connecting';
      
      // Emit both session_update and session_connecting for QR modal reactivity
      this.emit('session_connecting', {
        sessionId,
        status: 'connecting',
        isLoggedIn: false,
        timestamp: new Date()
      });
      
      this.emit('session_update', {
        sessionId,
        status: 'connecting',
        isLoggedIn: false,
        timestamp: new Date()
      });
      
    } else if (connection === 'close') {
      const shouldReconnect = lastDisconnect?.error?.output?.statusCode;
      
      if (shouldReconnect === DisconnectReason.restartRequired) {
        // This is normal after QR scanning - restart the connection
        this.logger.info(`Restart required for session ${sessionId}, creating new socket...`);
        
        // Update status to connecting during restart
        sessionState.status = 'connecting';
        
        // Emit connecting event for QR modal reactivity
        this.emit('session_connecting', {
          sessionId,
          status: 'connecting',
          isLoggedIn: false,
          timestamp: new Date()
        });
        
        this.emit('session_update', {
          sessionId,
          status: 'connecting',
          isLoggedIn: false,
          timestamp: new Date()
        });
        
        // Update database status (non-blocking)
        if (this.databaseService && this.databaseService.run) {
          this.databaseService.run(`
            UPDATE whatsapp_sessions 
            SET status = 'connecting',
                updated_at = CURRENT_TIMESTAMP
            WHERE session_id = ?
          `, [sessionId]).catch(dbError => {
            this.logger.error(`Database update error for session ${sessionId}:`, dbError);
          });
        }
        
        // Close current socket
        const currentSocket = this.sessions.get(sessionId);
        if (currentSocket) {
          try {
            await currentSocket.end();
          } catch (error) {
            // Ignore errors when closing
          }
          this.sessions.delete(sessionId);
        }
        
        // Create new socket with same auth state
        setTimeout(() => {
          this.restartSession(sessionId);
        }, 1000); // Small delay before reconnecting
        
        return; // Don't emit disconnection event for restart
        
      } else if (shouldReconnect === DisconnectReason.connectionClosed) {
        this.logger.info(`Connection closed for session ${sessionId}, attempting reconnect...`);
        sessionState.status = 'reconnecting';
        
        // Attempt reconnection
        setTimeout(() => {
          this.restartSession(sessionId);
        }, 5000);
        
      } else if (shouldReconnect === DisconnectReason.connectionLost) {
        this.logger.info(`Connection lost for session ${sessionId}, attempting reconnect...`);
        sessionState.status = 'reconnecting';
        
        setTimeout(() => {
          this.restartSession(sessionId);
        }, 3000);
        
      } else if (shouldReconnect === DisconnectReason.loggedOut) {
        // Check if this is a manual disconnection to avoid double-processing
        if (this.manualDisconnections.has(sessionId)) {
          devLog(`🔌 handleConnectionUpdate: Session ${sessionId} logged out due to manual disconnection, skipping automatic handling`);
          this.logger.info(`Session ${sessionId} logged out due to manual disconnection, skipping automatic handling`);
          return; // Skip processing since it's already handled by disconnectSession()
        }
        
        devLog(`🔌 handleConnectionUpdate: Processing automatic logout for session ${sessionId} (not manual)`);
        
        // Device was removed from WhatsApp Web - keep session but mark as disconnected
        this.logger.info(`Session ${sessionId} logged out (device removed), marking as disconnected`);
        sessionState.status = 'disconnected';
        sessionState.isLoggedIn = false;
        sessionState.qrCode = null; // Clear any existing QR code
        
        this.emit('session_disconnected', {
          sessionId,
          reason: 'Device removed from WhatsApp Web',
          timestamp: new Date()
        });
        
        // Update database (non-blocking) - keep session active for reconnection
        if (this.databaseService && this.databaseService.run) {
          this.databaseService.run(`
            UPDATE whatsapp_sessions 
            SET status = 'disconnected',
                qr_code = NULL,
                disconnected_at = CURRENT_TIMESTAMP,
                updated_at = CURRENT_TIMESTAMP
            WHERE session_id = ?
          `, [sessionId]).catch(dbError => {
            this.logger.error(`Database update error for session ${sessionId}:`, dbError);
          });
        }
        
        this.logger.info(`Session ${sessionId} disconnected: Device removed from WhatsApp Web`);
        
      } else {
        // Other disconnect reasons - mark as disconnected but keep session
        sessionState.status = 'disconnected';
        sessionState.isLoggedIn = false;
        
        const reason = lastDisconnect?.error?.message || 'Unknown';
        this.logger.info(`Session ${sessionId} disconnected with reason: ${reason}`);
        
        this.emit('session_disconnected', {
          sessionId,
          reason,
          timestamp: new Date()
        });
        
        // Update database (non-blocking) - keep session active for reconnection
        if (this.databaseService && this.databaseService.run) {
          this.databaseService.run(`
            UPDATE whatsapp_sessions 
            SET status = 'disconnected',
                disconnected_at = CURRENT_TIMESTAMP,
                updated_at = CURRENT_TIMESTAMP
            WHERE session_id = ?
          `, [sessionId]).catch(dbError => {
            this.logger.error(`Database update error for session ${sessionId}:`, dbError);
          });
        }
        
        this.logger.info(`Session ${sessionId} disconnected: ${reason}`);
      }
    }
  }

  /**
   * Handle incoming messages
   */
  async handleIncomingMessages(sessionId, messageUpdate) {
    const { messages, type } = messageUpdate;
    this.logger.info(`📨 Handling ${messages.length} messages of type ${type} for session ${sessionId}`);

    if (type === 'notify') {
      for (const message of messages) {
        this.logger.info(`📨 Processing message: fromMe=${message.key.fromMe}, remoteJid=${message.key.remoteJid}`);

        // Enhanced debugging for button responses
        if (message.message) {
          this.logger.info(`📨 Message structure keys: ${Object.keys(message.message).join(', ')}`);
          if (message.message.interactiveResponseMessage) {
            this.logger.info(`📨 🔥 INTERACTIVE RESPONSE MESSAGE DETECTED!`);
            this.logger.info(`📨 Interactive response: ${JSON.stringify(message.message.interactiveResponseMessage, null, 2)}`);
          }
          if (message.message.buttonsResponseMessage) {
            this.logger.info(`📨 🔥 BUTTONS RESPONSE MESSAGE DETECTED!`);
            this.logger.info(`📨 Buttons response: ${JSON.stringify(message.message.buttonsResponseMessage, null, 2)}`);
          }
          if (message.message.listResponseMessage) {
            this.logger.info(`📨 🔥 LIST RESPONSE MESSAGE DETECTED!`);
            this.logger.info(`📨 List response: ${JSON.stringify(message.message.listResponseMessage, null, 2)}`);
          }
          if (message.message.templateButtonReplyMessage) {
            this.logger.info(`📨 🔥 TEMPLATE BUTTON REPLY MESSAGE DETECTED!`);
            this.logger.info(`📨 Template button reply: ${JSON.stringify(message.message.templateButtonReplyMessage, null, 2)}`);
          }
        }

        if (!message.key.fromMe) {
          const formattedMessage = this.formatMessage(message);
          this.logger.info(`📨 Emitting message_received event for session ${sessionId}`);

          this.emit('message_received', {
            sessionId,
            message: message, // Pass original message for auto-reply processing
            formattedMessage: formattedMessage // Keep formatted version for other uses
          });
        }
      }
    }
  }

  /**
   * Handle contacts updates
   */
  async handleContactsUpdate(sessionId, contacts) {
    this.emit('contacts_update', {
      sessionId,
      contacts
    });
  }

  /**
   * Manual trigger for outgoing call responder
   */
  async triggerOutgoingCallResponse(sessionId, contactJid) {
    try {
      this.logger.info(`📞 MANUAL OUTGOING CALL TRIGGER: ${contactJid}`);

      // Create a synthetic outgoing call event
      const syntheticCall = {
        id: `manual_${Date.now()}`,
        from: contactJid,
        chatId: contactJid,
        status: 'outgoing_manual',
        isVideo: false,
        isGroup: false,
        date: new Date(),
        offline: false,
        timestamp: new Date(),
        isOutgoing: true,
        manual: true
      };

      // Process call responder rules immediately for manual outgoing calls
      await this.processCallResponderRules(sessionId, syntheticCall);

      return { success: true, message: 'Outgoing call response triggered' };
    } catch (error) {
      this.logger.error('Error triggering outgoing call response:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Handle call log messages to detect outgoing calls
   */
  async handleCallLogMessages(sessionId, messageUpdate) {
    const { messages, type } = messageUpdate;

    // Only log when actually processing call-related messages
    if (type === 'notify') {
      for (const message of messages) {
        // Check if this is a call log message
        if (message.message && message.message.callLogMessage) {
          const callLog = message.message.callLogMessage;

          this.logger.info(`📞 OUTGOING CALL LOG DETECTED: ${JSON.stringify(callLog)}`);

          // Create a synthetic call event for outgoing calls
          const syntheticCall = {
            id: message.key.id,
            from: message.key.remoteJid,
            chatId: message.key.remoteJid,
            status: 'outgoing_complete',
            isVideo: callLog.isVideo || false,
            isGroup: false,
            date: new Date(message.messageTimestamp * 1000),
            offline: false,
            timestamp: new Date(),
            isOutgoing: true,
            callOutcome: callLog.callOutcome,
            durationSecs: callLog.durationSecs
          };

          this.logger.info(`📞 SYNTHETIC OUTGOING CALL: ${JSON.stringify(syntheticCall)}`);

          // Emit as call received event
          this.emit('call_received', {
            sessionId,
            call: syntheticCall
          });

          // Process call responder rules immediately for outgoing calls
          await this.processCallResponderRules(sessionId, syntheticCall);
        }
      }
    }
  }

  /**
   * Handle all calls (incoming and outgoing) - FIXED VERSION
   */
  async handleCalls(sessionId, calls) {
    for (const call of calls) {
      // Get session info to determine if call is outgoing
      const sessionInfo = this.sessions.get(sessionId);
      let sessionPhoneNumber = sessionInfo?.phoneNumber;

      // If not found in session info, try to get from database
      if (!sessionPhoneNumber) {
        try {
          const dbResult = await this.databaseService.get(
            'SELECT phone_number FROM whatsapp_sessions WHERE session_id = ?',
            [sessionId]
          );
          sessionPhoneNumber = dbResult?.phone_number;
        } catch (error) {
          this.logger.error('Error retrieving session phone from DB:', error);
        }
      }

      const callData = {
        id: call.id,
        from: call.from,
        chatId: call.chatId,
        status: call.status,
        isVideo: call.isVideo,
        isGroup: call.isGroup,
        groupJid: call.groupJid,
        date: call.date,
        offline: call.offline,
        timestamp: new Date()
      };

      // Determine if this is an outgoing call
      const isOutgoingCall = sessionPhoneNumber && call.from && call.from.includes(sessionPhoneNumber.replace(/\+/g, ''));

      // Log call event for debugging with direction
      this.logger.info(`📞 Call event: ${call.status} | From: ${call.from} | Direction: ${isOutgoingCall ? 'OUTGOING' : 'INCOMING'}`);

      // Emit call received event (this triggers the event service)
      this.emit('call_received', {
        sessionId,
        call: {
          ...callData,
          isOutgoing: isOutgoingCall
        }
      });
    }
  }

  /**
   * Process call responder rules - FIXED VERSION
   */
  async processCallResponderRules(sessionId, callData) {
    // Process call responder rules called
    devLog("🚨🚨🚨 PROCESS CALL RESPONDER RULES CALLED! 🚨🚨🚨");
    this.logger.info("🚨🚨🚨 PROCESS CALL RESPONDER RULES CALLED! 🚨🚨🚨");
    this.logger.info(`📞 SessionId: ${sessionId}, Status: ${callData.status}, From: ${callData.from}`);

    try {
      this.logger.info(`📞 Starting call responder processing...`);
      devLog(`📞 Starting call responder processing...`);
      // Skip if database service not available
      if (!this.databaseService) {
        this.logger.warn(`📞 Database service not available for call responder rules ${sessionId}`);
        devLog(`📞 Database service not available for call responder rules ${sessionId}`);
        return;
      }

      this.logger.info(`📞 Database service available, continuing...`);
      devLog(`📞 Database service available, continuing...`);

      // Initialize call tracking if not exists
      if (!this.callTracker) {
        this.callTracker = new Map();
        this.logger.info(`📞 Initialized call tracker`);
        devLog(`📞 Initialized call tracker`);
      }

      // Create unique call identifier
      const callKey = `${sessionId}_${callData.id}_${callData.from}`;
      this.logger.info(`📞 Created call key: ${callKey}`);
      console.log(`📞 Created call key: ${callKey}`);

      // Track this call event
      if (!this.callTracker.has(callKey)) {
        this.logger.info(`📞 New call, creating tracker entry`);
        console.log(`📞 New call, creating tracker entry`);

        this.callTracker.set(callKey, {
          sessionId,
          callId: callData.id,
          from: callData.from,
          statuses: [],
          processed: false,
          firstSeen: Date.now(),
          lastUpdate: Date.now()
        });
      }

      const callInfo = this.callTracker.get(callKey);
      callInfo.statuses.push(callData.status);
      callInfo.lastUpdate = Date.now();

      this.logger.info(`📞 Call tracking: ${callKey} | Statuses: ${callInfo.statuses.join(' -> ')} | Current: ${callData.status}`);

      // IMMEDIATE PROCESSING: If this is a terminate, accept, or reject event, process immediately
      if (['terminate', 'accept', 'reject'].includes(callData.status)) {
        this.logger.info(`📞 IMMEDIATE PROCESSING: Final event "${callData.status}" received`);

        // Wait a short delay to ensure any concurrent events are captured
        setTimeout(async () => {
          await this.processFinalCall(callKey);
        }, 1000); // 1 second delay

        // Skip timeout setup for final events - return early
        return;
      }

      // Clean up old call tracking (older than 2 minutes)
      const twoMinutesAgo = Date.now() - (2 * 60 * 1000);
      for (const [key, info] of this.callTracker.entries()) {
        if (info.lastUpdate < twoMinutesAgo) {
          this.callTracker.delete(key);
        }
      }

      // Wait for call to stabilize (no new events for 10 seconds)
      this.logger.info(`📞 Setting up 10-second timeout for call stabilization`);

      setTimeout(async () => {
        this.logger.info(`📞 Timeout triggered - checking call stabilization`);

        try {
          const currentInfo = this.callTracker.get(callKey);

          if (!currentInfo || currentInfo.processed) {
            this.logger.info(`📞 Call already processed or cleaned up`);
            return; // Already processed or cleaned up
          }

          // Check if call has been stable (no updates in last 10 seconds)
          const timeSinceLastUpdate = Date.now() - currentInfo.lastUpdate;

          if (timeSinceLastUpdate < 10000) {
            this.logger.info(`📞 Still receiving updates, waiting more...`);
            return; // Still receiving updates, wait more
          }

          // Mark as processed
          currentInfo.processed = true;
          this.logger.info(`📞 Marking call as processed`);

          // Analyze the complete call sequence
          const statuses = currentInfo.statuses;
          this.logger.info(`📞 Raw statuses array: [${statuses.join(', ')}]`);

          const hasOffer = statuses.includes('offer');
          const hasRinging = statuses.includes('ringing');
          const hasAccept = statuses.includes('accept');
          const hasReject = statuses.includes('reject');
          const hasTerminate = statuses.includes('terminate');

          this.logger.info(`📞 Call sequence analysis: offer=${hasOffer}, ringing=${hasRinging}, accept=${hasAccept}, reject=${hasReject}, terminate=${hasTerminate}`);

          let finalCallType = null;

          // Check if this is an outgoing call for timeout processing too
          const sessionInfo = this.sessions.get(currentInfo.sessionId);
          let sessionPhoneNumber = sessionInfo?.phoneNumber;

          // If not found in session info, try to get from database
          if (!sessionPhoneNumber) {
            try {
              const dbResult = await this.databaseService.get(
                'SELECT phone_number FROM whatsapp_sessions WHERE session_id = ?',
                [currentInfo.sessionId]
              );
              sessionPhoneNumber = dbResult?.phone_number;
            } catch (error) {
              this.logger.error('TIMEOUT: Error retrieving session phone from DB:', error);
            }
          }
          const isOutgoingCall = sessionPhoneNumber && currentInfo.from && currentInfo.from.includes(sessionPhoneNumber.replace(/\+/g, ''));

          this.logger.info(`📞 TIMEOUT: Call direction check - Session Phone: ${sessionPhoneNumber}, From: ${currentInfo.from}, IsOutgoing: ${isOutgoingCall}`);
          console.log(`📞 TIMEOUT: Call direction check - Session Phone: ${sessionPhoneNumber}, From: ${currentInfo.from}, IsOutgoing: ${isOutgoingCall}`);

          // Determine call outcome based on complete sequence
          // TIMEOUT PROCESSING: Only process calls that have final events (accept, reject, terminate)
          if (isOutgoingCall || currentInfo.manual) {
            // For outgoing calls (detected or manual), any terminate means the call ended
            if (hasTerminate || currentInfo.manual) {
              finalCallType = 'outgoing';
              this.logger.info(`📞 TIMEOUT: Final call type: OUTGOING (call made by user) - ${currentInfo.manual ? 'MANUAL' : 'AUTO'}`);
            }
          } else {
            // For incoming calls, use existing logic
            if (hasReject) {
              finalCallType = 'rejected';
              this.logger.info(`📞 TIMEOUT: Final call type: REJECTED (user rejected the call)`);
            } else if (hasAccept) {
              finalCallType = 'received';
              this.logger.info(`📞 TIMEOUT: Final call type: RECEIVED (call was answered)`);
            } else if (hasOffer && hasRinging && hasTerminate && !hasAccept) {
              finalCallType = 'missed';
              this.logger.info(`📞 TIMEOUT: Final call type: MISSED (call rang but wasn't answered)`);
            }
          }

          if (!finalCallType) {
            // TIMEOUT: Don't process incomplete calls - wait for final events
            this.logger.info(`📞 TIMEOUT: Call incomplete, waiting for final events: ${statuses.join(' -> ')}`);
            this.logger.info(`📞 TIMEOUT: Sequence details: offer=${hasOffer}, ringing=${hasRinging}, accept=${hasAccept}, reject=${hasReject}, terminate=${hasTerminate}`);

            // Reset processed flag so immediate processing can handle it later
            currentInfo.processed = false;
            return;
          }

          // Process call responder rules for the final call type
          if (finalCallType) {
            this.logger.info(`📞 Processing final call responder for type: ${finalCallType}`);

            await this.processFinalCallResponder(sessionId, {
              ...callData,
              status: finalCallType,
              originalSequence: statuses.join(' -> ')
            });
          }
        } catch (timeoutError) {
          this.logger.error(`📞 Error in timeout callback:`, timeoutError);
          console.log(`📞 Error in timeout callback:`, timeoutError);
          return;
        }
      }, 10000); // Wait 10 seconds for call to stabilize

    } catch (error) {
      console.log(`❌ ERROR in processCallResponderRules:`, error);
      this.logger.error(`❌ ERROR in processCallResponderRules for ${sessionId}:`, error);
      this.logger.error(`❌ Error stack:`, error.stack);
    }
  }

  /**
   * Process final call immediately when terminate/accept/reject is received
   */
  async processFinalCall(callKey) {
    this.logger.info(`📞 PROCESS FINAL CALL STARTED for ${callKey}`);

    try {
      if (!this.callTracker || !this.callTracker.has(callKey)) {
        this.logger.info(`📞 Call key ${callKey} not found in tracker`);
        return;
      }

      const currentInfo = this.callTracker.get(callKey);
      if (currentInfo.processed) {
        this.logger.info(`📞 Call ${callKey} already processed`);
        return;
      }

      // Mark as processed
      currentInfo.processed = true;
      this.logger.info(`📞 IMMEDIATE: Marking call as processed`);

      // Analyze the complete call sequence
      const statuses = currentInfo.statuses;
      this.logger.info(`📞 IMMEDIATE: Raw statuses array: [${statuses.join(', ')}]`);

      const hasOffer = statuses.includes('offer');
      const hasRinging = statuses.includes('ringing');
      const hasAccept = statuses.includes('accept');
      const hasReject = statuses.includes('reject');
      const hasTerminate = statuses.includes('terminate');

      this.logger.info(`📞 IMMEDIATE: Call sequence analysis: offer=${hasOffer}, ringing=${hasRinging}, accept=${hasAccept}, reject=${hasReject}, terminate=${hasTerminate}`);

      let finalCallType = null;

      // Check if this is an outgoing call
      const sessionInfo = this.sessions.get(currentInfo.sessionId);
      let sessionPhoneNumber = sessionInfo?.phoneNumber;

      // If not found in session info, try to get from database
      if (!sessionPhoneNumber) {
        try {
          const dbResult = await this.databaseService.get(
            'SELECT phone_number FROM whatsapp_sessions WHERE session_id = ?',
            [currentInfo.sessionId]
          );
          sessionPhoneNumber = dbResult?.phone_number;
        } catch (error) {
          this.logger.error('IMMEDIATE: Error retrieving session phone from DB:', error);
        }
      }
      const isOutgoingCall = sessionPhoneNumber && currentInfo.from && currentInfo.from.includes(sessionPhoneNumber.replace(/\+/g, ''));

      this.logger.info(`📞 IMMEDIATE: Call direction check - IsOutgoing: ${isOutgoingCall}`);

      // Determine call outcome based on complete sequence
      if (isOutgoingCall || currentInfo.manual) {
        // For outgoing calls (detected or manual), any terminate means the call ended
        if (hasTerminate || currentInfo.manual) {
          finalCallType = 'outgoing';
          this.logger.info(`📞 IMMEDIATE: Final call type: OUTGOING (call made by user) - ${currentInfo.manual ? 'MANUAL' : 'AUTO'}`);
        }
      } else {
        // For incoming calls, use existing logic
        if (hasReject) {
          finalCallType = 'rejected';
          this.logger.info(`📞 IMMEDIATE: Final call type: REJECTED (user rejected the call)`);
        } else if (hasAccept) {
          finalCallType = 'received';
          this.logger.info(`📞 IMMEDIATE: Final call type: RECEIVED (call was answered)`);
        } else if (hasOffer && hasRinging && hasTerminate && !hasAccept) {
          finalCallType = 'missed';
          this.logger.info(`📞 IMMEDIATE: Final call type: MISSED (call rang but wasn't answered)`);
        }
      }

      if (!finalCallType) {
        this.logger.info(`📞 IMMEDIATE: Unable to determine call type from sequence: ${statuses.join(' -> ')}`);
        return;
      }

      // Process call responder rules for the final call type
      if (finalCallType) {
        this.logger.info(`📞 IMMEDIATE: Processing final call responder for type: ${finalCallType}`);

        await this.processFinalCallResponder(currentInfo.sessionId, {
          id: currentInfo.callId,
          from: currentInfo.from,
          status: finalCallType,
          originalSequence: statuses.join(' -> ')
        });
      }
    } catch (error) {
      this.logger.error(`❌ ERROR in processFinalCall:`, error);
      console.log(`❌ ERROR in processFinalCall:`, error);
    }
  }

  /**
   * Process final call responder after call sequence is complete
   */
  async processFinalCallResponder(sessionId, callData) {
    try {
      this.logger.info(`📞 ========== PROCESSING FINAL CALL RESPONDER ==========`);
      this.logger.info(`📞 SessionId: ${sessionId}`);
      this.logger.info(`📞 Final Call Type: ${callData.status}`);
      this.logger.info(`📞 From: ${callData.from}`);
      this.logger.info(`📞 Original Sequence: ${callData.originalSequence}`);
      this.logger.info(`📞 =====================================================`);

      // Get active call responder rules for this session
      const response = await this.databaseService.query(
        `SELECT * FROM call_responses
         WHERE session_id = ? AND is_active = 1
         ORDER BY created_at ASC`,
        [sessionId]
      );

      if (!response.success || !response.data.length) {
        this.logger.info(`📞 No active call responder rules found for session ${sessionId}`);
        return;
      }

      for (const rule of response.data) {
        const callTypes = JSON.parse(rule.call_types || '[]');

        // Check if this rule applies to the final call type
        if (callTypes.includes(callData.status)) {
          this.logger.info(`📞 Call responder rule "${rule.name}" triggered for ${callData.status} call from ${callData.from}`);

          // Send response
          await this.sendCallResponse(sessionId, callData, rule);
        }
      }
    } catch (error) {
      this.logger.error(`Error processing final call responder for ${sessionId}:`, error);
    }
  }

  /**
   * Send call response message
   */
  async sendCallResponse(sessionId, callData, rule) {
    try {
      let result;

      // Determine message content and type
      if (rule.message_type === 'template' && rule.template_id) {
        // Get template content
        const templateResponse = await this.databaseService.query(
          'SELECT * FROM message_templates WHERE id = ?',
          [rule.template_id]
        );

        if (templateResponse.success && templateResponse.data.length > 0) {
          const template = templateResponse.data[0];

          // Prepare template variables for call responder context
          const templateVariables = {
            name: callData.from.split('@')[0], // Extract phone number as name
            phone: callData.from.split('@')[0],
            callType: callData.status,
            callTime: new Date(callData.timestamp).toLocaleString(),
            isVideo: callData.isVideo ? 'Video' : 'Voice'
          };

          result = await this.sendTemplateMessage(sessionId, callData.from, template, templateVariables);
        } else {
          this.logger.warn(`Template ${rule.template_id} not found for call response rule ${rule.name}`);
          return;
        }
      } else {
        // Send custom message with optional attachment
        if (rule.attachment_file && rule.attachment_type) {
          // Send message with attachment
          const fs = require('fs');
          const path = require('path');

          if (fs.existsSync(rule.attachment_file)) {
            const messageContent = {
              caption: rule.message_content || ''
            };

            // Set the media content based on attachment type
            switch (rule.attachment_type) {
              case 'image':
                messageContent.image = { url: rule.attachment_file };
                break;
              case 'video':
                messageContent.video = { url: rule.attachment_file };
                break;
              case 'audio':
                messageContent.audio = { url: rule.attachment_file };
                break;
              case 'document':
                messageContent.document = { url: rule.attachment_file };
                messageContent.fileName = path.basename(rule.attachment_file);
                break;
            }

            result = await this.sendMediaMessage(sessionId, callData.from, messageContent);
          } else {
            this.logger.warn(`Attachment file ${rule.attachment_file} not found for call response rule ${rule.name}`);
            // Fall back to text message
            result = await this.sendTextMessage(sessionId, callData.from, rule.message_content);
          }
        } else {
          // Send text message
          result = await this.sendTextMessage(sessionId, callData.from, rule.message_content);
        }
      }

      if (result && result.success) {
        // Update usage statistics
        if (this.databaseService) {
          this.databaseService.query(
            'UPDATE call_responses SET usage_count = COALESCE(usage_count, 0) + 1, last_used = CURRENT_TIMESTAMP WHERE id = ?',
            [rule.id]
          ).catch(dbError => {
            this.logger.error(`Database update error for call response count ${rule.id}:`, dbError);
          });

          // Log the activity
          this.databaseService.query(
            `INSERT INTO activity_logs (action_type, description, metadata)
             VALUES (?, ?, ?)`,
            [
              'call_response_sent',
              `Call response sent for rule ${rule.name} to ${callData.from}`,
              JSON.stringify({
                ruleId: rule.id,
                ruleName: rule.name,
                sessionId: sessionId,
                callType: callData.status,
                fromNumber: callData.from,
                messageType: rule.message_type,
                hasAttachment: !!(rule.attachment_file && rule.attachment_type),
                messageId: result.messageId
              })
            ]
          ).catch(dbError => {
            this.logger.error(`Database log error for call response ${rule.id}:`, dbError);
          });
        }

        this.logger.info(`Call response sent for rule ${rule.name} to ${callData.from}`);
      } else {
        this.logger.error(`Failed to send call response for rule ${rule.name}:`, result?.error || 'Unknown error');
      }
    } catch (error) {
      this.logger.error(`Error sending call response for rule ${rule.name}:`, error);
    }
  }

  /**
   * Handle presence updates
   */
  async handlePresenceUpdate(sessionId, presence) {
    this.emit('presence_update', {
      sessionId,
      presence
    });
  }

  /**
   * Get chats for a session
   */
  async getChats(sessionId) {
    try {


      const socket = this.sessions.get(sessionId);
      if (!socket) {

        return { success: false, message: 'Session not found' };
      }

      const store = this.stores.get(sessionId);
      if (!store) {

        return { success: false, message: 'Store not found for session' };
      }

      // Get chats from the store
      const chats = store.chats.all();

      this.logger.info(`Found ${chats.length} chats in store for session ${sessionId}`);

      // Process chats to match our expected format
      const processedChats = chats.map(chat => {
        // Get the last message for this chat
        const chatMessages = store.messages[chat.id];
        let lastMessage = null;
        let lastMessageTimestamp = chat.conversationTimestamp || Date.now() / 1000;

        if (chatMessages && chatMessages.array.length > 0) {
          const lastMsg = chatMessages.array[chatMessages.array.length - 1];
          lastMessage = {
            text: this.getMessageText(lastMsg.message),
            timestamp: lastMsg.messageTimestamp
          };
          lastMessageTimestamp = lastMsg.messageTimestamp;
        }

        return {
          id: chat.id,
          name: chat.name || this.formatPhoneNumber(chat.id),
          lastMessage: lastMessage || { text: 'No messages yet', timestamp: lastMessageTimestamp },
          unreadCount: chat.unreadCount || 0,
          profilePicture: null, // Will be fetched separately if needed
          conversationTimestamp: lastMessageTimestamp
        };
      });

      // Sort by last message timestamp (most recent first)
      processedChats.sort((a, b) => (b.lastMessage.timestamp || 0) - (a.lastMessage.timestamp || 0));

      this.logger.info(`Retrieved ${processedChats.length} chats for session ${sessionId}`);

      // If no chats found, try database fallback first
      if (processedChats.length === 0) {
        try {
          console.log(`🔄 WhatsApp Service: No chats in store, attempting to get from database for session ${sessionId}`);
          this.logger.info(`No chats in store, attempting to get from database for session ${sessionId}`);

          // Try to get chats from database
          const dbChats = await this.getChatsFromDatabase(sessionId);
          if (dbChats.length > 0) {
            this.logger.info(`Found ${dbChats.length} chats in database for session ${sessionId}`);
            return { success: true, chats: dbChats };
          }

          // If database is also empty, try to sync from WhatsApp
          console.log(`🔄 WhatsApp Service: No chats in database, attempting to sync from WhatsApp for session ${sessionId}`);
          this.logger.info(`No chats in database, attempting to sync from WhatsApp for session ${sessionId}`);
          await this.syncChatsFromWhatsApp(sessionId);

          // Try again after sync
          const chatsAfterSync = store.chats.all();
          if (chatsAfterSync.length > 0) {
            this.logger.info(`Found ${chatsAfterSync.length} chats after sync for session ${sessionId}`);
            // Re-process the chats
            const syncedChats = chatsAfterSync.map(chat => {
              const chatMessages = store.messages[chat.id];
              let lastMessage = null;
              let lastMessageTimestamp = chat.conversationTimestamp || Date.now() / 1000;

              if (chatMessages && chatMessages.array.length > 0) {
                const lastMsg = chatMessages.array[chatMessages.array.length - 1];
                lastMessage = {
                  text: this.getMessageText(lastMsg.message),
                  timestamp: lastMsg.messageTimestamp
                };
                lastMessageTimestamp = lastMsg.messageTimestamp;
              }

              return {
                id: chat.id,
                name: chat.name || this.formatPhoneNumber(chat.id),
                lastMessage: lastMessage || { text: 'No messages yet', timestamp: lastMessageTimestamp },
                unreadCount: chat.unreadCount || 0,
                profilePicture: null,
                conversationTimestamp: lastMessageTimestamp
              };
            });

            syncedChats.sort((a, b) => (b.lastMessage.timestamp || 0) - (a.lastMessage.timestamp || 0));
            return { success: true, chats: syncedChats };
          }
        } catch (syncError) {
          this.logger.warn(`Failed to sync chats from WhatsApp for session ${sessionId}:`, syncError);
        }
      }

      return { success: true, chats: processedChats };
    } catch (error) {
      this.logger.error(`Error getting chats for session ${sessionId}:`, error);
      return { success: false, message: error.message };
    }
  }

  /**
   * Get chats from database when store is empty
   */
  async getChatsFromDatabase(sessionId) {
    try {
      // Get unique contacts who have sent/received messages
      const query = `
        SELECT
          contact_phone,
          MAX(timestamp) as last_message_time,
          (SELECT content FROM message_history mh2
           WHERE mh2.contact_phone = mh.contact_phone
           AND mh2.session_id = mh.session_id
           ORDER BY timestamp DESC LIMIT 1) as last_message_content,
          (SELECT message_type FROM message_history mh3
           WHERE mh3.contact_phone = mh.contact_phone
           AND mh3.session_id = mh.session_id
           ORDER BY timestamp DESC LIMIT 1) as last_message_type
        FROM message_history mh
        WHERE session_id = ?
        GROUP BY contact_phone
        ORDER BY last_message_time DESC
      `;

      const result = await this.database.query(query, [sessionId]);

      if (!result.success || !result.data) {
        return [];
      }

      // Convert database results to chat format
      const chats = result.data.map(row => {
        const chatId = `${row.contact_phone}@s.whatsapp.net`;
        const lastMessageTime = new Date(row.last_message_time).getTime() / 1000;

        return {
          id: chatId,
          name: this.formatPhoneNumber(chatId),
          lastMessage: {
            text: row.last_message_content || 'No messages yet',
            timestamp: lastMessageTime
          },
          unreadCount: 0,
          profilePicture: null,
          conversationTimestamp: lastMessageTime
        };
      });

      this.logger.info(`Retrieved ${chats.length} chats from database for session ${sessionId}`);
      return chats;
    } catch (error) {
      this.logger.error(`Error getting chats from database for session ${sessionId}:`, error);
      return [];
    }
  }

  /**
   * Helper method to extract text from message object
   */
  getMessageText(messageObj) {
    if (!messageObj) return '';

    return messageObj.conversation ||
           messageObj.extendedTextMessage?.text ||
           messageObj.imageMessage?.caption ||
           messageObj.videoMessage?.caption ||
           messageObj.documentMessage?.caption ||
           messageObj.audioMessage?.caption ||
           (messageObj.imageMessage ? '📷 Photo' : '') ||
           (messageObj.videoMessage ? '🎥 Video' : '') ||
           (messageObj.audioMessage ? '🎵 Audio' : '') ||
           (messageObj.documentMessage ? '📄 Document' : '') ||
           (messageObj.stickerMessage ? '🎭 Sticker' : '') ||
           (messageObj.locationMessage ? '📍 Location' : '') ||
           (messageObj.contactMessage ? '👤 Contact' : '') ||
           'Message';
  }

  /**
   * Helper method to format phone number for display
   */
  formatPhoneNumber(jid) {
    if (!jid) return '';
    const phoneNumber = jid.split('@')[0];
    if (phoneNumber.length > 10) {
      // Format as +XX XXX XXX XXXX
      return `+${phoneNumber.slice(0, -10)} ${phoneNumber.slice(-10, -7)} ${phoneNumber.slice(-7, -4)} ${phoneNumber.slice(-4)}`;
    }
    return phoneNumber;
  }

  /**
   * Sync chats from WhatsApp
   */
  async syncChatsFromWhatsApp(sessionId) {
    try {
      const socket = this.sessions.get(sessionId);
      if (!socket) {
        throw new Error('Session not found');
      }

      this.logger.info(`Syncing chats from WhatsApp for session ${sessionId}`);

      // Try to get chat list from WhatsApp
      // Note: Baileys doesn't have a direct "getChats" method, but the store should
      // automatically populate as messages come in. We can trigger this by:

      // 1. Request presence updates (this can trigger chat sync)
      try {
        await socket.presenceSubscribe(socket.user?.id);
      } catch (presenceError) {
        this.logger.warn(`Failed to subscribe to presence for session ${sessionId}:`, presenceError);
      }

      // 2. The store should automatically populate from the connection events
      // The bind(socket.ev) call should handle this automatically

      this.logger.info(`Chat sync initiated for session ${sessionId}`);
      return { success: true };
    } catch (error) {
      this.logger.error(`Error syncing chats from WhatsApp for session ${sessionId}:`, error);
      throw error;
    }
  }

  /**
   * Get chat history for a specific chat
   */
  async getChatHistory(sessionId, chatId, limit = 50, beforeTimestamp = null) {
    try {
      const socket = this.sessions.get(sessionId);
      if (!socket) {
        this.logger.error(`Session ${sessionId} not found for getChatHistory`);
        return { success: false, message: 'Session not found' };
      }

      const store = this.stores.get(sessionId);
      if (!store) {
        this.logger.error(`Store not found for session ${sessionId}`);
        return { success: false, message: 'Store not found for session' };
      }

      this.logger.info(`Getting chat history for ${chatId} in session ${sessionId}, limit: ${limit}`);

      // Get messages from store
      const chatMessages = store.messages[chatId];
      this.logger.info(`Store messages for ${chatId}:`, chatMessages ? `${chatMessages.array?.length || 0} messages` : 'no messages');

      if (!chatMessages || !chatMessages.array || chatMessages.array.length === 0) {
        this.logger.info(`No local messages found for ${chatId}, attempting to fetch from WhatsApp`);

        // Try to fetch from WhatsApp if no local messages
        try {
          const messages = await socket.fetchMessageHistory(chatId, limit);
          this.logger.info(`Fetched ${messages?.length || 0} messages from WhatsApp for ${chatId}`);
          return { success: true, messages: messages || [] };
        } catch (fetchError) {
          this.logger.warn(`Could not fetch message history for ${chatId}:`, fetchError);
          return { success: true, messages: [] };
        }
      }

      let messages = [...chatMessages.array];
      this.logger.info(`Processing ${messages.length} messages from store for ${chatId}`);

      // Filter by timestamp if provided
      if (beforeTimestamp) {
        const originalLength = messages.length;
        messages = messages.filter(msg => msg.messageTimestamp < beforeTimestamp);
        this.logger.info(`Filtered messages by timestamp: ${originalLength} -> ${messages.length}`);
      }

      // Sort by timestamp (oldest first)
      messages.sort((a, b) => a.messageTimestamp - b.messageTimestamp);

      // Apply limit
      if (limit && messages.length > limit) {
        messages = messages.slice(-limit); // Get the most recent messages up to limit
        this.logger.info(`Applied limit: showing last ${limit} messages`);
      }

      this.logger.info(`Retrieved ${messages.length} messages for chat ${chatId} in session ${sessionId}`);
      return { success: true, messages };
    } catch (error) {
      this.logger.error(`Error getting chat history for ${chatId} in session ${sessionId}:`, error);
      return { success: false, message: error.message };
    }
  }

  /**
   * Mark chat as read
   */
  async markChatAsRead(sessionId, chatId) {
    try {
      const socket = this.sessions.get(sessionId);
      if (!socket) {
        return { success: false, message: 'Session not found' };
      }

      const store = this.stores.get(sessionId);
      if (!store) {
        return { success: false, message: 'Store not found for session' };
      }

      // Get unread messages from the chat
      const chatMessages = store.messages[chatId];
      if (chatMessages && chatMessages.array) {
        const unreadMessages = chatMessages.array.filter(msg =>
          !msg.key.fromMe && (!msg.status || msg.status !== 'read')
        );

        if (unreadMessages.length > 0) {
          // Get the latest unread message to mark as read
          const latestMessage = unreadMessages[unreadMessages.length - 1];

          try {
            await socket.readMessages([latestMessage.key]);
            this.logger.info(`Marked ${unreadMessages.length} messages as read in chat ${chatId}`);
          } catch (readError) {
            this.logger.warn(`Failed to mark messages as read in chat ${chatId}:`, readError);
          }
        }
      }

      return { success: true, message: 'Chat marked as read' };
    } catch (error) {
      this.logger.error(`Error marking chat as read for ${chatId} in session ${sessionId}:`, error);
      return { success: false, message: error.message };
    }
  }

  /**
   * Format message for consistent structure
   */
  formatMessage(message) {
    try {
      // Safely extract text content
      let text = '';
      if (message.message) {
        if (message.message.conversation) {
          text = message.message.conversation;
        } else if (message.message.extendedTextMessage && message.message.extendedTextMessage.text) {
          text = message.message.extendedTextMessage.text;
        } else if (message.message.interactiveResponseMessage && message.message.interactiveResponseMessage.nativeFlowResponseMessage) {
          // Handle interactive list/flow responses (newer format)
          const nativeFlow = message.message.interactiveResponseMessage.nativeFlowResponseMessage;
          if (nativeFlow.paramsJson) {
            try {
              const params = JSON.parse(nativeFlow.paramsJson);
              text = params.id || params.title || params.display_text || 'Unknown selection';
              this.logger.info(`📨 🔥 formatMessage extracted interactive list response - params: ${JSON.stringify(params)}, final text: "${text}"`);
            } catch (error) {
              this.logger.error(`Error parsing interactive response params:`, error);
              text = 'Unknown selection';
            }
          } else {
            text = 'Unknown selection';
          }
        } else if (message.message.interactiveResponseMessage && message.message.interactiveResponseMessage.body && message.message.interactiveResponseMessage.body.text) {
          // Handle interactive button responses
          text = message.message.interactiveResponseMessage.body.text;
          this.logger.info(`📨 🔥 formatMessage extracted interactive response text: "${text}"`);
        } else if (message.message.buttonsResponseMessage && message.message.buttonsResponseMessage.selectedDisplayText) {
          // Handle legacy button responses
          text = message.message.buttonsResponseMessage.selectedDisplayText;
          this.logger.info(`📨 🔥 formatMessage extracted button response text: "${text}"`);
        } else if (message.message.listResponseMessage && message.message.listResponseMessage.title) {
          // Handle list responses
          const rowId = message.message.listResponseMessage.singleSelectReply?.selectedRowId;
          text = message.message.listResponseMessage.title;
          this.logger.info(`📨 🔥 formatMessage extracted list response - rowId: "${rowId}", title: "${text}"`);
        } else if (message.message.templateButtonReplyMessage && message.message.templateButtonReplyMessage.selectedDisplayText) {
          // Handle template button responses
          text = message.message.templateButtonReplyMessage.selectedDisplayText;
          this.logger.info(`📨 🔥 formatMessage extracted template button response text: "${text}"`);
        } else if (message.message.imageMessage && message.message.imageMessage.caption) {
          text = message.message.imageMessage.caption;
        } else if (message.message.videoMessage && message.message.videoMessage.caption) {
          text = message.message.videoMessage.caption;
        }
      }

      return {
        id: message.key?.id || '',
        from: message.key?.remoteJid || '',
        fromMe: message.key?.fromMe || false,
        timestamp: message.messageTimestamp || Date.now(),
        text: text,
        type: this.getMessageType(message.message || {}),
        participant: message.key?.participant || null
      };
    } catch (error) {
      this.logger.error('Error formatting message:', error);
      return {
        id: '',
        from: '',
        fromMe: false,
        timestamp: Date.now(),
        text: '',
        type: 'text',
        participant: null
      };
    }
  }

  /**
   * Get message type
   */
  getMessageType(messageContent) {
    if (!messageContent || typeof messageContent !== 'object') {
      return 'text';
    }

    if (messageContent.conversation) return 'text';
    if (messageContent.extendedTextMessage) return 'text';
    if (messageContent.imageMessage) return 'image';
    if (messageContent.videoMessage) return 'video';
    if (messageContent.audioMessage) return 'audio';
    if (messageContent.documentMessage) return 'document';
    if (messageContent.stickerMessage) return 'sticker';
    if (messageContent.contactMessage) return 'contact';
    if (messageContent.locationMessage) return 'location';
    return 'text'; // Default to text instead of unknown
  }

  /**
   * General send message method - routes to appropriate specific method
   */
  async sendMessage(sessionId, to, content, type = 'text', options = {}) {
    try {
      // Early validation for text messages to prevent sending empty content
      if (type === 'text') {
        let textContent = content;
        if (typeof content === 'object' && content.text) {
          textContent = content.text;
        }
        if (!textContent || (typeof textContent === 'string' && textContent.trim() === '')) {
          this.logger.error(`❌ Refusing to send empty text message. Content: "${textContent}"`);
          return {
            success: false,
            error: 'Cannot send empty text message'
          };
        }
      }

      console.log('🔍 DEBUG: sendMessage called with:', {
        sessionId,
        to,
        type,
        content: JSON.stringify(content),
        hasButtons: content && content.buttons ? content.buttons.length : 0,
        contentKeys: content ? Object.keys(content) : []
      });

      switch (type) {
        case 'text':
          if (typeof content === 'object' && content.text) {
            return await this.sendTextMessage(sessionId, to, content.text);
          }
          return await this.sendTextMessage(sessionId, to, content);

        case 'image':
        case 'video':
        case 'audio':
        case 'document':
          // Handle media messages
          if (typeof content === 'object' && content[type]) {
            const socket = this.sessions.get(sessionId);
            if (!socket) {
              throw new Error('Session not found');
            }

            let mediaMessage = {};
            const mediaData = content[type];

            // Handle different media data formats
            if (typeof mediaData === 'object' && mediaData.url) {
              // Check if it's a base64 data URL
              if (mediaData.url.startsWith('data:')) {
                // Convert base64 data URL to buffer
                const base64Data = mediaData.url.split(',')[1];
                const buffer = Buffer.from(base64Data, 'base64');
                mediaMessage[type] = buffer;
              } else {
                // Regular URL
                mediaMessage[type] = mediaData;
              }
            } else if (typeof mediaData === 'string') {
              // Direct URL or base64 string
              if (mediaData.startsWith('data:')) {
                // Convert base64 data URL to buffer
                const base64Data = mediaData.split(',')[1];
                const buffer = Buffer.from(base64Data, 'base64');
                mediaMessage[type] = buffer;
              } else {
                mediaMessage[type] = { url: mediaData };
              }
            } else {
              // Direct buffer or other format
              mediaMessage[type] = mediaData;
            }

            // Add caption if provided
            if (content.caption) {
              mediaMessage.caption = content.caption;
            }

            // Add other properties for specific media types
            if (type === 'document' && content.fileName) {
              mediaMessage.fileName = content.fileName;
            }
            if (type === 'audio' && content.mimetype) {
              mediaMessage.mimetype = content.mimetype;
            }
            if (content.viewOnce) {
              mediaMessage.viewOnce = content.viewOnce;
            }

            const result = await socket.sendMessage(to, mediaMessage);
            return {
              success: true,
              messageId: result.key.id,
              timestamp: result.messageTimestamp
            };
          } else if (options.mediaBuffer) {
            // This is a direct media upload with buffer
            return await this.sendMediaMessage(sessionId, to, options.mediaBuffer, type, content);
          } else {
            throw new Error(`No media content or buffer provided for ${type} message`);
          }

        case 'button':
        case 'buttons':
        case 'interactive':
          // Check if it's the new Itsukichann/Baileys format with interactiveMessage
          if (content.interactiveMessage) {
            const socket = this.sessions.get(sessionId);
            if (!socket) {
              throw new Error('Session not found');
            }

            // Convert to the format expected by Itsukichann/Baileys
            const interactiveMsg = {
              text: content.interactiveMessage.body.text,
              footer: content.interactiveMessage.footer?.text,
              buttons: content.interactiveMessage.nativeFlowMessage.buttons.map((btn, index) => {
                const params = JSON.parse(btn.buttonParamsJson);
                return {
                  buttonId: params.id || `btn_${index}`,
                  buttonText: { displayText: params.display_text },
                  type: 1
                };
              })
            };

            const result = await socket.sendMessage(to, interactiveMsg);
            return {
              success: true,
              messageId: result.key.id,
              timestamp: result.messageTimestamp
            };
          }
          // Check if it's the old format with interactiveButtons
          if (content.interactiveButtons) {
            const socket = this.sessions.get(sessionId);
            if (!socket) {
              throw new Error('Session not found');
            }
            const result = await socket.sendMessage(to, content);
            return {
              success: true,
              messageId: result.key.id,
              timestamp: result.messageTimestamp
            };
          }
          // Check if it's the format with buttons array (from bulk messaging)
          if (content.buttons && Array.isArray(content.buttons)) {
            const socket = this.sessions.get(sessionId);
            if (!socket) {
              throw new Error('Session not found');
            }
            const result = await socket.sendMessage(to, content);
            return {
              success: true,
              messageId: result.key.id,
              timestamp: result.messageTimestamp
            };
          }
          return await this.sendInteractiveMessage(sessionId, to, content);

        case 'list':
          // Check if it's the new Itsukichann/Baileys format with interactiveMessage
          if (content.interactiveMessage) {
            const socket = this.sessions.get(sessionId);
            if (!socket) {
              throw new Error('Session not found');
            }

            // Convert to the format expected by Itsukichann/Baileys
            const listButton = content.interactiveMessage.nativeFlowMessage.buttons[0];
            const listParams = JSON.parse(listButton.buttonParamsJson);

            const listMsg = {
              text: content.interactiveMessage.body.text,
              footer: content.interactiveMessage.footer?.text,
              title: listParams.title,
              buttonText: listParams.title,
              sections: listParams.sections
            };

            const result = await socket.sendMessage(to, listMsg);
            return {
              success: true,
              messageId: result.key.id,
              timestamp: result.messageTimestamp
            };
          }
          // Check if it's the format with sections array and text (from Auto Reply/message processor)
          if (content.sections && Array.isArray(content.sections) && content.text) {
            const socket = this.sessions.get(sessionId);
            if (!socket) {
              throw new Error('Session not found');
            }
            const result = await socket.sendMessage(to, content);
            return {
              success: true,
              messageId: result.key.id,
              timestamp: result.messageTimestamp
            };
          }
          // Check if it's the format with sections array (from bulk messaging - different structure)
          if (content.sections && Array.isArray(content.sections) && content.body) {
            return await this.sendInteractiveListMessage(sessionId, to, content);
          }
          // Check if it's the old format with sections
          if (content.sections) {
            const socket = this.sessions.get(sessionId);
            if (!socket) {
              throw new Error('Session not found');
            }
            const result = await socket.sendMessage(to, content);
            return {
              success: true,
              messageId: result.key.id,
              timestamp: result.messageTimestamp
            };
          }
          return await this.sendListMessage(sessionId, to, content, options.buttonText || 'Select Option', options.sections || []);

        case 'poll':
          // Handle template-based poll messages
          if (typeof content === 'object' && content.poll) {
            const socket = this.sessions.get(sessionId);
            if (!socket) {
              throw new Error('Session not found');
            }

            const result = await socket.sendMessage(to, content);
            return {
              success: true,
              messageId: result.key.id,
              timestamp: result.messageTimestamp
            };
          }
          return await this.sendPollMessage(sessionId, to, content);

        case 'contact':
          // Handle template-based contact messages
          if (typeof content === 'object' && content.contacts) {
            const socket = this.sessions.get(sessionId);
            if (!socket) {
              throw new Error('Session not found');
            }

            const result = await socket.sendMessage(to, content);
            return {
              success: true,
              messageId: result.key.id,
              timestamp: result.messageTimestamp
            };
          }
          return await this.sendContactMessage(sessionId, to, content);

        case 'location':
          return await this.sendLocationMessage(sessionId, to, content);



        case 'cta_button':
          return await this.sendCTAButtonMessage(sessionId, to, content);

        case 'copy_code':
          return await this.sendCopyCodeMessage(sessionId, to, content);

        case 'mixed_buttons':
          this.logger.info(`🔍 DEBUG: Routing to sendMixedButtonsMessage with content:`, JSON.stringify(content, null, 2));
          return await this.sendMixedButtonsMessage(sessionId, to, content);

        default:
          // Default to text message
          if (typeof content === 'object' && content.text) {
            return await this.sendTextMessage(sessionId, to, content.text);
          }
          return await this.sendTextMessage(sessionId, to, content);
      }
    } catch (error) {
      this.logger.error(`Error sending message from ${sessionId}:`, error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Send text message
   */
  async sendTextMessage(sessionId, to, text) {
    try {
      // Validate text parameter
      if (text === undefined || text === null) {
        this.logger.error(`❌ Cannot send message: text is ${text}`);
        return {
          success: false,
          error: `Text parameter is ${text}`
        };
      }

      // Properly extract text content from objects to prevent [object Object]
      let textMessage;
      if (typeof text === 'object' && text !== null) {
        if (text.text) {
          textMessage = String(text.text);
        } else if (text.content) {
          textMessage = String(text.content);
        } else if (text.body && text.body.text) {
          textMessage = String(text.body.text);
        } else {
          this.logger.warn(`❌ Object passed to sendTextMessage without text property:`, text);
          textMessage = JSON.stringify(text);
        }
      } else {
        textMessage = String(text);
      }

      // Final validation to prevent [object Object]
      if (textMessage === '[object Object]' || !textMessage || textMessage.trim() === '') {
        this.logger.error(`❌ Invalid text message content: "${textMessage}" - refusing to send empty message`);
        return {
          success: false,
          error: 'Cannot send empty message'
        };
      }

      this.logger.info(`📤 Sending text message from session ${sessionId} to ${to}: "${textMessage}"`);

      const socket = this.sessions.get(sessionId);
      if (!socket) {
        this.logger.error(`❌ Session ${sessionId} not found`);
        throw new Error('Session not found');
      }

      this.logger.info(`📤 Socket found, sending message...`);
      const result = await socket.sendMessage(to, { text: textMessage });

      this.logger.info(`✅ Message sent successfully: ${result.key.id}`);
      return {
        success: true,
        messageId: result.key.id,
        timestamp: result.messageTimestamp
      };
    } catch (error) {
      this.logger.error(`❌ Error sending text message from ${sessionId}:`, error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Send media message
   */
  async sendMediaMessage(sessionId, to, mediaBuffer, mediaType, caption = '') {
    try {
      const socket = this.sessions.get(sessionId);
      if (!socket) {
        throw new Error('Session not found');
      }

      const mediaMessage = {};
      mediaMessage[mediaType] = mediaBuffer;
      if (caption) mediaMessage.caption = caption;

      const result = await socket.sendMessage(to, mediaMessage);
      return {
        success: true,
        messageId: result.key.id,
        timestamp: result.messageTimestamp
      };
    } catch (error) {
      this.logger.error(`Error sending media message from ${sessionId}:`, error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Download media from message
   */
  async downloadMedia(sessionId, messageKey) {
    try {
      const socket = this.sessions.get(sessionId);
      if (!socket) {
        throw new Error('Session not found');
      }

      // Get the message from store
      const store = this.stores.get(sessionId);
      if (!store) {
        throw new Error('Store not found for session');
      }

      const message = await store.loadMessage(messageKey.remoteJid, messageKey.id);
      if (!message) {
        throw new Error('Message not found');
      }

      // Check if it's a media message
      const messageContent = message.message;
      if (!messageContent) {
        throw new Error('No message content found');
      }

      // Download media using Baileys downloadContentFromMessage
      const stream = await downloadContentFromMessage(message, getContentType(messageContent));

      // Convert stream to buffer
      const bufferArray = [];
      for await (const chunk of stream) {
        bufferArray.push(chunk);
      }
      const buffer = Buffer.concat(bufferArray);

      // Determine MIME type
      let mimeType = 'application/octet-stream';
      if (messageContent.imageMessage) {
        mimeType = messageContent.imageMessage.mimetype || 'image/jpeg';
      } else if (messageContent.videoMessage) {
        mimeType = messageContent.videoMessage.mimetype || 'video/mp4';
      } else if (messageContent.audioMessage) {
        mimeType = messageContent.audioMessage.mimetype || 'audio/mp4';
      } else if (messageContent.documentMessage) {
        mimeType = messageContent.documentMessage.mimetype || 'application/octet-stream';
      }

      return {
        success: true,
        buffer: buffer,
        mimeType: mimeType,
        size: buffer.length
      };
    } catch (error) {
      this.logger.error(`Error downloading media from ${sessionId}:`, error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Send interactive button message
   */
  async sendButtonMessage(sessionId, to, text, buttons) {
    try {
      const socket = this.sessions.get(sessionId);
      if (!socket) {
        throw new Error('Session not found');
      }

      const buttonMessage = {
        text,
        buttons: buttons.map((btn, index) => ({
          buttonId: btn.id || `btn_${index}`,
          buttonText: { displayText: btn.text },
          type: 1
        })),
        headerType: 1
      };

      const result = await socket.sendMessage(to, buttonMessage);
      return {
        success: true,
        messageId: result.key.id,
        timestamp: result.messageTimestamp
      };
    } catch (error) {
      this.logger.error(`Error sending button message from ${sessionId}:`, error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Send interactive list message
   */
  async sendListMessage(sessionId, to, text, buttonText, sections) {
    try {
      const socket = this.sessions.get(sessionId);
      if (!socket) {
        throw new Error('Session not found');
      }

      const listMessage = {
        text,
        buttonText,
        sections: sections.map(section => ({
          title: section.title,
          rows: section.rows.map(row => ({
            title: row.title,
            description: row.description || '',
            rowId: row.id
          }))
        }))
      };

      const result = await socket.sendMessage(to, listMessage);
      return {
        success: true,
        messageId: result.key.id,
        timestamp: result.messageTimestamp
      };
    } catch (error) {
      this.logger.error(`Error sending list message from ${sessionId}:`, error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Send poll message
   */
  async sendPollMessage(sessionId, to, pollData) {
    try {
      const socket = this.sessions.get(sessionId);
      if (!socket) {
        throw new Error('Session not found');
      }

      const pollMessage = {
        poll: {
          name: pollData.name,
          values: pollData.values,
          selectableCount: pollData.selectableCount || 1
        }
      };

      const result = await socket.sendMessage(to, pollMessage);
      return {
        success: true,
        messageId: result.key.id,
        timestamp: result.messageTimestamp
      };
    } catch (error) {
      this.logger.error(`Error sending poll message from ${sessionId}:`, error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Send contact message
   */
  async sendContactMessage(sessionId, to, contactData) {
    try {
      const socket = this.sessions.get(sessionId);
      if (!socket) {
        throw new Error('Session not found');
      }

      const result = await socket.sendMessage(to, contactData);
      return {
        success: true,
        messageId: result.key.id,
        timestamp: result.messageTimestamp
      };
    } catch (error) {
      this.logger.error(`Error sending contact message from ${sessionId}:`, error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Send location message
   */
  async sendLocationMessage(sessionId, to, locationData) {
    try {
      const socket = this.sessions.get(sessionId);
      if (!socket) {
        throw new Error('Session not found');
      }

      const result = await socket.sendMessage(to, locationData);
      return {
        success: true,
        messageId: result.key.id,
        timestamp: result.messageTimestamp
      };
    } catch (error) {
      this.logger.error(`Error sending location message from ${sessionId}:`, error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Send interactive message (buttons, lists, etc.)
   */
  async sendInteractiveMessage(sessionId, to, interactiveData) {
    try {
      const socket = this.sessions.get(sessionId);
      if (!socket) {
        throw new Error('Session not found');
      }

      const result = await socket.sendMessage(to, interactiveData);
      return {
        success: true,
        messageId: result.key.id,
        timestamp: result.messageTimestamp
      };
    } catch (error) {
      this.logger.error(`Error sending interactive message from ${sessionId}:`, error);
      return {
        success: false,
        error: error.message
      };
    }
  }



  /**
   * Send interactive buttons message (new Baileys format)
   */
  async sendInteractiveButtonsMessage(sessionId, to, content) {
    try {
      const socket = this.sessions.get(sessionId);
      if (!socket) {
        throw new Error('Session not found');
      }

      // New Baileys interactive buttons format
      const interactiveMessage = {
        interactiveMessage: {
          header: content.header ? {
            title: content.header.title,
            subtitle: content.header.subtitle,
            hasMediaAttachment: false
          } : undefined,
          body: {
            text: content.body.text
          },
          footer: content.footer ? {
            text: content.footer.text
          } : undefined,
          nativeFlowMessage: {
            buttons: content.buttons.map((button, index) => ({
              name: 'quick_reply',
              buttonParamsJson: JSON.stringify({
                display_text: button.text,
                id: button.id || `btn_${index}`
              })
            })),
            messageParamsJson: ''
          }
        }
      };

      const result = await socket.sendMessage(to, interactiveMessage);
      return {
        success: true,
        messageId: result.key.id,
        timestamp: result.messageTimestamp
      };
    } catch (error) {
      this.logger.error(`Error sending interactive buttons message from ${sessionId}:`, error);
      this.logger.error(`Button content that failed:`, JSON.stringify(content, null, 2));
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Send interactive list message (new Baileys format)
   */
  async sendInteractiveListMessage(sessionId, to, content) {
    try {
      const socket = this.sessions.get(sessionId);
      if (!socket) {
        throw new Error('Session not found');
      }

      // New Baileys interactive list format
      const interactiveMessage = {
        interactiveMessage: {
          header: content.header ? {
            title: content.header.title,
            subtitle: content.header.subtitle,
            hasMediaAttachment: false
          } : undefined,
          body: {
            text: content.body.text
          },
          footer: content.footer ? {
            text: content.footer.text
          } : undefined,
          nativeFlowMessage: {
            buttons: [{
              name: 'single_select',
              buttonParamsJson: JSON.stringify({
                title: content.buttonText || 'Select Option',
                sections: content.sections.map(section => ({
                  title: section.title,
                  rows: section.rows.map(row => ({
                    header: row.title,
                    title: row.title,
                    description: row.description,
                    id: row.id
                  }))
                }))
              })
            }],
            messageParamsJson: ''
          }
        }
      };

      const result = await socket.sendMessage(to, interactiveMessage);
      return {
        success: true,
        messageId: result.key.id,
        timestamp: result.messageTimestamp
      };
    } catch (error) {
      this.logger.error(`Error sending interactive list message from ${sessionId}:`, error);
      return {
        success: false,
        error: error.message
      };
    }
  }



  /**
   * Send call-to-action (CTA) button message
   */
  async sendCTAButtonMessage(sessionId, to, content) {
    try {
      const socket = this.sessions.get(sessionId);
      if (!socket) {
        throw new Error('Session not found');
      }

      // Try multiple formats to ensure compatibility
      let result;

      // Format 1: Simple interactiveButtons (most compatible)
      try {
        const ctaMessage = {
          text: content.body.text,
          footer: content.footer && content.footer.text ? content.footer.text : undefined,
          interactiveButtons: [{
            name: 'cta_url',
            buttonParamsJson: JSON.stringify({
              display_text: content.button.text,
              url: content.button.url,
              merchant_url: content.button.url
            })
          }]
        };

        this.logger.info(`Attempting CTA button format 1 to ${to}:`, JSON.stringify(ctaMessage, null, 2));
        result = await socket.sendMessage(to, ctaMessage);

        this.logger.info(`CTA button message sent successfully with format 1:`, {
          messageId: result.key.id,
          timestamp: result.messageTimestamp
        });

        return {
          success: true,
          messageId: result.key.id,
          timestamp: result.messageTimestamp
        };
      } catch (format1Error) {
        this.logger.warn(`Format 1 failed, trying format 2:`, format1Error.message);

        // Format 2: Fallback to simple text with URL
        try {
          const fallbackMessage = {
            text: `${content.body.text}\n\n🔗 ${content.button.text}: ${content.button.url}`,
            footer: content.footer && content.footer.text ? content.footer.text : undefined
          };

          this.logger.info(`Attempting CTA fallback format to ${to}:`, JSON.stringify(fallbackMessage, null, 2));
          result = await socket.sendMessage(to, fallbackMessage);

          this.logger.info(`CTA fallback message sent successfully:`, {
            messageId: result.key.id,
            timestamp: result.messageTimestamp
          });

          return {
            success: true,
            messageId: result.key.id,
            timestamp: result.messageTimestamp,
            fallback: true
          };
        } catch (format2Error) {
          throw format2Error;
        }
      }
    } catch (error) {
      this.logger.error(`Error sending CTA button message from ${sessionId}:`, error);
      this.logger.error(`CTA content that failed:`, JSON.stringify(content, null, 2));
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Send copy code button message
   */
  async sendCopyCodeMessage(sessionId, to, content) {
    try {
      const socket = this.sessions.get(sessionId);
      if (!socket) {
        throw new Error('Session not found');
      }

      // Use the correct format for copy code button messages according to Itsukichann/Baileys official docs
      const copyMessage = {
        text: content.body.text,
        title: content.title || undefined,
        subtitle: content.subtitle || undefined,
        footer: content.footer ? content.footer.text : undefined,
        buttons: [{
          name: 'cta_copy',
          buttonParamsJson: JSON.stringify({
            display_text: content.button.text,
            copy_code: content.button.code
          })
        }]
      };

      this.logger.info(`Sending copy code message to ${to}:`, copyMessage);
      const result = await socket.sendMessage(to, copyMessage);

      this.logger.info(`Copy code message sent successfully:`, {
        messageId: result.key.id,
        timestamp: result.messageTimestamp
      });

      return {
        success: true,
        messageId: result.key.id,
        timestamp: result.messageTimestamp
      };
    } catch (error) {
      this.logger.error(`Error sending copy code message from ${sessionId}:`, error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Send mixed interactive buttons message (combines quick reply, CTA URL, CTA call, and copy code buttons)
   */
  async sendMixedButtonsMessage(sessionId, to, content) {
    try {
      const socket = this.sessions.get(sessionId);
      if (!socket) {
        throw new Error('Session not found');
      }

      // Build the interactiveButtons array with mixed button types
      const interactiveButtons = content.buttons.map((button, index) => {
        switch (button.type) {
          case 'quick_reply':
            return {
              name: 'quick_reply',
              buttonParamsJson: JSON.stringify({
                display_text: button.text,
                id: button.id || `btn_${index}`
              })
            };

          case 'cta_url':
            return {
              name: 'cta_url',
              buttonParamsJson: JSON.stringify({
                display_text: button.text,
                url: button.url,
                merchant_url: button.url
              })
            };

          case 'cta_call':
            return {
              name: 'cta_call',
              buttonParamsJson: JSON.stringify({
                display_text: button.text,
                phone_number: button.phone_number
              })
            };

          case 'copy_code':
            return {
              name: 'cta_copy',
              buttonParamsJson: JSON.stringify({
                display_text: button.text,
                copy_code: button.code
              })
            };

          default:
            // Fallback to quick_reply for unknown types
            return {
              name: 'quick_reply',
              buttonParamsJson: JSON.stringify({
                display_text: button.text,
                id: button.id || `btn_${index}`
              })
            };
        }
      });

      const mixedButtonMessage = {
        text: content.body.text,
        footer: content.footer && content.footer.text ? content.footer.text : undefined,
        interactiveButtons: interactiveButtons
      };

      this.logger.info(`Sending mixed buttons message to ${to}:`, JSON.stringify(mixedButtonMessage, null, 2));
      const result = await socket.sendMessage(to, mixedButtonMessage);

      this.logger.info(`Mixed buttons message sent successfully:`, {
        messageId: result.key.id,
        timestamp: result.messageTimestamp
      });

      return {
        success: true,
        messageId: result.key.id,
        timestamp: result.messageTimestamp
      };
    } catch (error) {
      this.logger.error(`Error sending mixed buttons message from ${sessionId}:`, error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Send flow message (for complex forms)
   */
  async sendFlowMessage(sessionId, to, content) {
    try {
      const socket = this.sessions.get(sessionId);
      if (!socket) {
        throw new Error('Session not found');
      }

      const interactiveMessage = {
        interactiveMessage: {
          body: {
            text: content.body.text
          },
          footer: content.footer ? {
            text: content.footer.text
          } : undefined,
          nativeFlowMessage: {
            buttons: [{
              name: 'flow',
              buttonParamsJson: JSON.stringify({
                display_text: content.button.text,
                flow_message_version: '3',
                flow_token: content.flow.token,
                flow_id: content.flow.id,
                flow_cta: content.flow.cta,
                flow_action: content.flow.action || 'navigate',
                flow_action_payload: {
                  screen: content.flow.screen || 'WELCOME_SCREEN'
                }
              })
            }],
            messageParamsJson: ''
          }
        }
      };

      const result = await socket.sendMessage(to, interactiveMessage);
      return {
        success: true,
        messageId: result.key.id,
        timestamp: result.messageTimestamp
      };
    } catch (error) {
      this.logger.error(`Error sending flow message from ${sessionId}:`, error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Template message based on template data
   */
  async sendTemplateMessage(sessionId, to, templateData, variables = {}) {
    try {
      const socket = this.sessions.get(sessionId);
      if (!socket) {
        throw new Error('Session not found');
      }

      // Replace variables in content
      let content = templateData.content;
      const templateVariables = JSON.parse(templateData.variables || '[]');

      templateVariables.forEach(variable => {
        const value = variables[variable] || `{{${variable}}}`;
        content = content.replace(new RegExp(`\\{\\{${variable}\\}\\}`, 'g'), value);
      });

      let result;

      switch (templateData.type) {
        case 'text':
          result = await this.sendTextMessage(sessionId, to, content);
          break;

        case 'image':
          const imageAttachments = JSON.parse(templateData.attachments || '[]');
          const imageSettings = JSON.parse(templateData.media_settings || '{}');
          if (imageAttachments.length > 0) {
            const attachment = imageAttachments[0];

            let imageMessage;

            if (attachment && attachment.data && attachment.data.startsWith('data:')) {
              // Handle base64 data URL
              const base64Data = attachment.data.split(',')[1];
              const buffer = Buffer.from(base64Data, 'base64');
              imageMessage = {
                image: buffer,
                caption: content,
                ...(imageSettings.viewOnce && { viewOnce: true })
              };
            } else if (typeof attachment === 'string') {
              // Handle URL
              imageMessage = {
                image: { url: attachment },
                caption: content,
                ...(imageSettings.viewOnce && { viewOnce: true })
              };
            } else if (attachment && typeof attachment === 'object' && attachment.url) {
              // Handle object with URL
              imageMessage = {
                image: { url: attachment.url },
                caption: content,
                ...(imageSettings.viewOnce && { viewOnce: true })
              };
            } else {

              throw new Error('Invalid image attachment format');
            }

            result = await socket.sendMessage(to, imageMessage);
            result = { success: true, messageId: result.key.id, timestamp: result.messageTimestamp };
          } else {
            throw new Error('No image attachment found');
          }
          break;

        case 'video':
          const videoAttachments = JSON.parse(templateData.attachments || '[]');
          const videoSettings = JSON.parse(templateData.media_settings || '{}');
          if (videoAttachments.length > 0) {
            const attachment = videoAttachments[0];
            let videoMessage;

            if (attachment.data && attachment.data.startsWith('data:')) {
              // Handle base64 data URL
              const base64Data = attachment.data.split(',')[1];
              const buffer = Buffer.from(base64Data, 'base64');
              videoMessage = {
                video: buffer,
                caption: content,
                ...(videoSettings.viewOnce && { viewOnce: true })
              };
            } else if (typeof attachment === 'string') {
              // Handle URL
              videoMessage = {
                video: { url: attachment },
                caption: content,
                ...(videoSettings.viewOnce && { viewOnce: true })
              };
            } else if (attachment && typeof attachment === 'object' && attachment.url) {
              // Handle object with URL
              videoMessage = {
                video: { url: attachment.url },
                caption: content,
                ...(videoSettings.viewOnce && { viewOnce: true })
              };
            } else {
              throw new Error('Invalid video attachment format');
            }

            result = await socket.sendMessage(to, videoMessage);
            result = { success: true, messageId: result.key.id, timestamp: result.messageTimestamp };
          } else {
            throw new Error('No video attachment found');
          }
          break;

        case 'audio':
          const audioAttachments = JSON.parse(templateData.attachments || '[]');
          if (audioAttachments.length > 0) {
            const attachment = audioAttachments[0];
            let audioMessage;

            if (attachment.data && attachment.data.startsWith('data:')) {
              // Handle base64 data URL
              const base64Data = attachment.data.split(',')[1];
              const buffer = Buffer.from(base64Data, 'base64');
              audioMessage = {
                audio: buffer,
                mimetype: attachment.type || 'audio/mp4'
              };
            } else if (typeof attachment === 'string') {
              // Handle URL
              audioMessage = {
                audio: { url: attachment },
                mimetype: 'audio/mp4'
              };
            } else {
              throw new Error('Invalid audio attachment format');
            }

            result = await socket.sendMessage(to, audioMessage);
            result = { success: true, messageId: result.key.id, timestamp: result.messageTimestamp };
          } else {
            throw new Error('No audio attachment found');
          }
          break;

        case 'document':
          const docAttachments = JSON.parse(templateData.attachments || '[]');
          const docSettings = JSON.parse(templateData.media_settings || '{}');
          if (docAttachments.length > 0) {
            const attachment = docAttachments[0];
            let documentMessage;

            if (attachment.data && attachment.data.startsWith('data:')) {
              // Handle base64 data URL
              const base64Data = attachment.data.split(',')[1];
              const buffer = Buffer.from(base64Data, 'base64');
              documentMessage = {
                document: buffer,
                fileName: attachment.name || docSettings.fileName || 'document.pdf',
                caption: content
              };
            } else if (typeof attachment === 'string') {
              // Handle URL
              documentMessage = {
                document: { url: attachment },
                fileName: docSettings.fileName || 'document.pdf',
                caption: content
              };
            } else {
              throw new Error('Invalid document attachment format');
            }

            result = await socket.sendMessage(to, documentMessage);
            result = { success: true, messageId: result.key.id, timestamp: result.messageTimestamp };
          } else {
            throw new Error('No document attachment found');
          }
          break;

        case 'buttons':
          const buttons = JSON.parse(templateData.buttons || '[]');

          // Log the exact data we're trying to send
          console.log('🔍 Button template data:', JSON.stringify(templateData, null, 2));
          console.log('🔍 Parsed buttons:', JSON.stringify(buttons, null, 2));

          // Try multiple button formats based on Itsukichann/Baileys documentation
          if (buttons.length > 0) {
            try {
              // Format 1: Try the interactiveButtons format from documentation
              const buttonInteractiveSettings = JSON.parse(templateData.interactive_settings || '{}');
              const buttonMessage = {
                text: content,
                footer: buttonInteractiveSettings.footerText || 'Choose an option:',
                interactiveButtons: buttons.map((btn, index) => ({
                  name: 'quick_reply',
                  buttonParamsJson: JSON.stringify({
                    display_text: btn.text,
                    id: btn.id || `btn_${index}`
                  })
                }))
              };

              result = await socket.sendMessage(to, buttonMessage);
              result = { success: true, messageId: result.key.id, timestamp: result.messageTimestamp };
            } catch (error) {
              this.logger.error(`InteractiveButtons format failed:`, error);
              this.logger.error(`InteractiveButtons message that failed:`, JSON.stringify(buttonMessage, null, 2));

              try {
                // Format 2: Try the legacy buttons format from SingleMessage.js
                const legacyButtonMessage = {
                  text: content,
                  footer: buttonInteractiveSettings.footerText || 'Choose an option:',
                  buttons: buttons.map((btn, index) => ({
                    buttonId: btn.id || `btn_${index}`,
                    buttonText: { displayText: btn.text },
                    type: 1
                  }))
                };

                this.logger.info(`Trying legacy button format:`, JSON.stringify(legacyButtonMessage, null, 2));
                result = await socket.sendMessage(to, legacyButtonMessage);
                result = { success: true, messageId: result.key.id, timestamp: result.messageTimestamp };
              } catch (error2) {
                this.logger.error(`Legacy buttons format failed:`, error2);
                this.logger.error(`Legacy button message that failed:`, JSON.stringify(legacyButtonMessage, null, 2));
                // Fallback to text message if all button formats fail
                result = await socket.sendMessage(to, { text: content });
                result = { success: true, messageId: result.key.id, timestamp: result.messageTimestamp };
              }
            }
          } else {
            // Fallback to text message if no buttons
            result = await socket.sendMessage(to, { text: content });
            result = { success: true, messageId: result.key.id, timestamp: result.messageTimestamp };
          }
          break;

        case 'list':
          const listSections = JSON.parse(templateData.list_sections || '[]');
          const listInteractiveSettings = JSON.parse(templateData.interactive_settings || '{}');

          // Use new interactive list format
          if (listSections.length > 0) {
            const interactiveContent = {
              body: { text: content },
              footer: { text: listInteractiveSettings.footerText || 'Select an option' },
              buttonText: listInteractiveSettings.buttonText || 'View Options',
              sections: listSections.map(section => ({
                title: section.title,
                rows: section.rows.map(row => ({
                  id: row.id,
                  title: row.title,
                  description: row.description
                }))
              }))
            };

            result = await this.sendInteractiveListMessage(sessionId, to, interactiveContent);
          } else {
            // Fallback to text message if no list sections
            result = await socket.sendMessage(to, { text: content });
            result = { success: true, messageId: result.key.id, timestamp: result.messageTimestamp };
          }
          break;

        case 'poll':
          const pollOptions = JSON.parse(templateData.poll_options || '[]');
          const pollMessage = {
            poll: {
              name: content,
              values: pollOptions.map(opt => typeof opt === 'string' ? opt : opt.text),
              selectableCount: 1
            }
          };
          result = await socket.sendMessage(to, pollMessage);
          result = { success: true, messageId: result.key.id, timestamp: result.messageTimestamp };
          break;

        case 'contact':
          const contactInfo = JSON.parse(templateData.contact_info || '{}');

          // Generate vCard format
          const vcard = `BEGIN:VCARD
VERSION:3.0
FN:${contactInfo.name || 'Contact'}
N:${contactInfo.name ? contactInfo.name.split(' ').reverse().join(';') : 'Contact'}
TEL;TYPE=CELL:${contactInfo.phone || ''}
${contactInfo.email ? `EMAIL:${contactInfo.email}` : ''}
${contactInfo.organization ? `ORG:${contactInfo.organization}` : ''}
END:VCARD`.replace(/\n\n/g, '\n').trim();

          const contactMessage = {
            contactsArrayMessage: {
              displayName: contactInfo.name || 'Contact',
              contacts: [{
                displayName: contactInfo.name || 'Contact',
                vcard: vcard
              }]
            }
          };

          result = await socket.sendMessage(to, contactMessage);
          result = { success: true, messageId: result.key.id, timestamp: result.messageTimestamp };
          break;

        case 'location':
          const locationInfo = JSON.parse(templateData.location_info || '{}');
          const locationMessage = {
            location: {
              degreesLatitude: locationInfo.latitude,
              degreesLongitude: locationInfo.longitude,
              name: locationInfo.name || 'Location',
              address: locationInfo.address || ''
            }
          };
          result = await socket.sendMessage(to, locationMessage);
          result = { success: true, messageId: result.key.id, timestamp: result.messageTimestamp };
          break;



        case 'cta_button':
          const ctaData = JSON.parse(templateData.cta_data || '{}');
          if (ctaData.button && ctaData.button.url) {
            const ctaContent = {
              body: { text: content },
              footer: ctaData.footer && ctaData.footer.text ? { text: ctaData.footer.text } : undefined,
              button: {
                text: ctaData.button.text,
                url: ctaData.button.url
              }
            };
            result = await this.sendCTAButtonMessage(sessionId, to, ctaContent);
          } else {
            result = await socket.sendMessage(to, { text: content });
            result = { success: true, messageId: result.key.id, timestamp: result.messageTimestamp };
          }
          break;

        case 'copy_code':
          const copyData = JSON.parse(templateData.copy_data || '{}');
          if (copyData.button && copyData.button.code) {
            const copyContent = {
              body: { text: content },
              footer: copyData.footer && copyData.footer.text ? { text: copyData.footer.text } : undefined,
              button: {
                text: copyData.button.text,
                code: copyData.button.code
              }
            };
            result = await this.sendCopyCodeMessage(sessionId, to, copyContent);
          } else {
            result = await socket.sendMessage(to, { text: content });
            result = { success: true, messageId: result.key.id, timestamp: result.messageTimestamp };
          }
          break;

        case 'flow':
          const flowData = JSON.parse(templateData.flow_data || '{}');
          if (flowData.flow && flowData.flow.id) {
            const flowContent = {
              body: { text: content },
              footer: flowData.footer ? { text: flowData.footer.text } : undefined,
              button: {
                text: flowData.button.text
              },
              flow: flowData.flow
            };
            result = await this.sendFlowMessage(sessionId, to, flowContent);
          } else {
            result = await socket.sendMessage(to, { text: content });
            result = { success: true, messageId: result.key.id, timestamp: result.messageTimestamp };
          }
          break;

        case 'mixed_buttons':
          const mixedButtonsData = JSON.parse(templateData.mixed_buttons_data || '{"buttons": [], "footer": {"text": ""}}');
          if (mixedButtonsData.buttons && mixedButtonsData.buttons.length > 0) {
            const mixedContent = {
              body: { text: content },
              footer: mixedButtonsData.footer && mixedButtonsData.footer.text ? { text: mixedButtonsData.footer.text } : undefined,
              buttons: mixedButtonsData.buttons
            };
            result = await this.sendMixedButtonsMessage(sessionId, to, mixedContent);
          } else {
            result = await socket.sendMessage(to, { text: content });
            result = { success: true, messageId: result.key.id, timestamp: result.messageTimestamp };
          }
          break;

        default:
          result = await this.sendTextMessage(sessionId, to, content);
      }

      return result;
    } catch (error) {
      this.logger.error(`Error sending template message from ${sessionId}:`, error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get session status
   */
  getSessionStatus(sessionId) {
    return this.sessionStates.get(sessionId) || null;
  }

  /**
   * Get all sessions
   */
  getAllSessions() {
    return Array.from(this.sessionStates.values());
  }

  /**
   * Disconnect session (logout but keep session data for reconnection)
   */
  async disconnectSession(sessionId) {
    try {
      console.log(`🔌 DISCONNECT ONLY: Starting disconnect for ${sessionId}`);
      
      // Mark this session as being manually disconnected
      this.manualDisconnections.add(sessionId);
      
      const socket = this.sessions.get(sessionId);
      if (socket) {
        try {
          // Properly logout from WhatsApp
          await socket.logout();
          this.logger.info(`Session ${sessionId} logged out successfully`);
        } catch (logoutError) {
          this.logger.warn(`Logout error for session ${sessionId}:`, logoutError.message);
        }
        
        // Remove from active sessions but keep session state for reconnection
        this.sessions.delete(sessionId);
      }
      
      // Update session state to disconnected
      const sessionState = this.sessionStates.get(sessionId);
      if (sessionState) {
        sessionState.status = 'disconnected';
        sessionState.isLoggedIn = false;
        sessionState.qrCode = null;
      }
      
      // CRITICAL: Update database status to disconnected but DO NOT SET is_active = 0
      if (this.databaseService && this.databaseService.run) {
        console.log(`🔌 DISCONNECT ONLY: Updating database for ${sessionId} - keeping is_active = 1`);
        await this.databaseService.run(`
          UPDATE whatsapp_sessions 
          SET status = 'disconnected',
              qr_code = NULL,
              disconnected_at = CURRENT_TIMESTAMP,
              updated_at = CURRENT_TIMESTAMP
          WHERE session_id = ? AND is_active = 1
        `, [sessionId]);
      }
      
      // Emit disconnection event
      this.emit('session_disconnected', {
        sessionId,
        reason: 'Manually disconnected by user',
        timestamp: new Date()
      });
      
      // Remove from manual disconnections tracking after a short delay
      setTimeout(() => {
        this.manualDisconnections.delete(sessionId);
      }, 5000); // Increased to 5 seconds
      
      console.log(`🔌 DISCONNECT ONLY: Completed disconnect for ${sessionId} - session should remain visible`);
      return {
        success: true,
        message: 'Session disconnected successfully'
      };
    } catch (error) {
      this.logger.error(`Error disconnecting session ${sessionId}:`, error);
      // Remove from tracking on error
      this.manualDisconnections.delete(sessionId);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Delete session (completely remove session and files)
   */
  async deleteSession(sessionId) {
    try {
      console.log(`🗑️ DELETE ONLY: Starting complete deletion for ${sessionId}`);
      
      // Delete from WhatsApp socket
      const socket = this.sessions.get(sessionId);
      if (socket) {
        try {
          // Add timeout to prevent hanging
          const logoutPromise = socket.logout();
          const timeoutPromise = new Promise((_, reject) =>
            setTimeout(() => reject(new Error('Logout timeout')), 5000)
          );

          await Promise.race([logoutPromise, timeoutPromise]);
        } catch (logoutError) {
          // Continue with deletion even if logout fails
          console.log('🗑️ DELETE ONLY: Logout error (continuing with deletion):', logoutError.message);
        }
        this.sessions.delete(sessionId);
      }
      
      // Delete from session states
      this.sessionStates.delete(sessionId);

      // Clean up store
      this.cleanupStore(sessionId);
      
      // Delete session files
      const sessionDir = path.join(this.authDir, sessionId);
      if (fs.existsSync(sessionDir)) {
        fs.rmSync(sessionDir, { recursive: true, force: true });
        console.log(`🗑️ DELETE ONLY: Deleted session files for ${sessionId}`);
      }
      
      // CRITICAL: Actually delete from database by setting is_active = 0
      try {
        console.log(`🗑️ DELETE ONLY: Setting is_active = 0 for ${sessionId}`);
        const WhatsAppSession = require('../models/WhatsAppSession');
        const session = await WhatsAppSession.findBySessionId(sessionId);
        if (session) {
          await session.delete(); // This sets is_active = 0
          console.log(`🗑️ DELETE ONLY: Session ${sessionId} marked as inactive in database`);
        }
      } catch (dbError) {
        console.error(`🗑️ DELETE ONLY: Error deleting session ${sessionId} from database:`, dbError);
        // Don't fail the whole operation for database errors
      }
      
      this.emit('session_deleted', { sessionId });
      
      console.log(`🗑️ DELETE ONLY: Completed deletion for ${sessionId} - session should disappear from UI`);
      return {
        success: true,
        message: 'Session deleted successfully'
      };
    } catch (error) {
      this.logger.error(`Error deleting session ${sessionId}:`, error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Check if phone number is registered on WhatsApp
   */
  async checkNumberExists(sessionId, phoneNumber) {
    try {
      const socket = this.sessions.get(sessionId);
      if (!socket) {
        throw new Error('Session not found');
      }

      const result = await socket.onWhatsApp(phoneNumber);
      return {
        success: true,
        exists: result.length > 0,
        jid: result[0]?.jid
      };
    } catch (error) {
      this.logger.error(`Error checking number ${phoneNumber}:`, error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Verify if phone number is registered on WhatsApp (wrapper for checkNumberExists)
   */
  async verifyNumber(phoneNumber) {
    try {
      // Get the first active session
      const activeSessions = Array.from(this.sessions.keys());
      if (activeSessions.length === 0) {
        throw new Error('No active WhatsApp sessions available');
      }

      const sessionId = activeSessions[0];
      return await this.checkNumberExists(sessionId, phoneNumber);
    } catch (error) {
      this.logger.error(`Error verifying number ${phoneNumber}:`, error);
      return {
        success: false,
        exists: false,
        error: error.message
      };
    }
  }

  /**
   * Fetch all participating groups using Baileys API
   */
  async fetchAllGroups(sessionId) {
    try {
      const socket = this.sessions.get(sessionId);
      if (!socket) {
        throw new Error('Session not found');
      }

      this.logger.info(`Fetching all groups for session ${sessionId}`);

      // Use Baileys groupFetchAllParticipating method
      const groupsData = await socket.groupFetchAllParticipating();

      if (!groupsData) {
        return {
          success: true,
          groups: []
        };
      }

      // Process groups data with enhanced admin detection
      const groups = [];

      for (const group of Object.values(groupsData)) {
        // Determine if user is admin - comprehensive logic with debugging
        const userJid = socket.user?.id;
        let isAdmin = false;

        this.logger.info(`🔍 Checking admin status for group: ${group.subject} (${group.id})`);
        this.logger.info(`👤 Current user JID: ${userJid}`);

        if (userJid && group.participants) {
          // Extract phone number from user JID
          const userPhone = userJid.replace('@s.whatsapp.net', '').replace('@c.us', '');

          // Check multiple possible JID formats
          const possibleUserJids = [
            userJid,
            userPhone,
            `${userPhone}@s.whatsapp.net`,
            `${userPhone}@c.us`
          ];

          this.logger.info(`🔍 Possible user JIDs: ${JSON.stringify(possibleUserJids)}`);
          this.logger.info(`👥 Group participants: ${JSON.stringify(group.participants.map(p => ({ id: p.id, admin: p.admin })))}`);

          // Find the user in participants
          const userParticipant = group.participants.find(participant => {
            const participantPhone = participant.id.replace('@s.whatsapp.net', '').replace('@c.us', '');
            const isMatch = possibleUserJids.some(jid => {
              const jidPhone = jid.replace('@s.whatsapp.net', '').replace('@c.us', '');
              return participantPhone === jidPhone || participant.id === jid;
            });

            if (isMatch) {
              this.logger.info(`✅ Found user in participants: ${participant.id} with admin status: ${participant.admin}`);
            }

            return isMatch;
          });

          if (userParticipant) {
            isAdmin = userParticipant.admin === 'admin' || userParticipant.admin === 'superadmin' || userParticipant.admin === true;
            this.logger.info(`🎯 Final admin status for ${group.subject}: ${isAdmin} (admin field: ${userParticipant.admin})`);
          } else {
            this.logger.warn(`❌ User not found in participants for group: ${group.subject}`);
          }
        }

        // If admin status is still false, try a quick invite code test as fallback
        if (!isAdmin && group.id) {
          try {
            this.logger.info(`🔄 Testing admin status via invite code for: ${group.subject}`);
            const inviteResult = await socket.groupInviteCode(group.id);
            if (inviteResult) {
              isAdmin = true;
              this.logger.info(`✅ Confirmed admin status via invite code for: ${group.subject}`);
            }
          } catch (error) {
            this.logger.info(`❌ Not admin for ${group.subject}: ${error.message}`);
          }
        }

        // Check if it's a community (communities have different structure)
        const isCommunity = group.id?.includes('@newsletter') || group.linkedParent || false;

        groups.push({
          id: group.id,
          subject: group.subject,
          desc: group.desc,
          creation: group.creation,
          participants: group.participants || [],
          isAdmin: isAdmin,
          isCommunity: isCommunity,
          announce: group.announce || false,
          restrict: group.restrict || false,
          inviteCode: group.inviteCode || null,
          size: group.size || (group.participants?.length || 0)
        });
      }

      this.logger.info(`Successfully fetched ${groups.length} groups for session ${sessionId}`);

      return {
        success: true,
        groups: groups
      };

    } catch (error) {
      this.logger.error(`Error fetching groups for session ${sessionId}:`, error);
      return {
        success: false,
        error: error.message,
        groups: []
      };
    }
  }

  /**
   * Get detailed group metadata
   */
  async getGroupMetadata(sessionId, groupId) {
    try {
      const socket = this.sessions.get(sessionId);
      if (!socket) {
        throw new Error('Session not found');
      }

      this.logger.info(`Fetching metadata for group ${groupId} in session ${sessionId}`);

      // Use Baileys groupMetadata method
      const metadata = await socket.groupMetadata(groupId);

      if (!metadata) {
        throw new Error('Group metadata not found');
      }

      // Determine if user is admin
      const userJid = socket.user?.id;
      const isAdmin = metadata.participants?.some(participant =>
        participant.id === userJid && (participant.admin === 'admin' || participant.admin === 'superadmin')
      ) || false;

      // Process participants data
      const participants = metadata.participants?.map(participant => ({
        id: participant.id,
        admin: participant.admin === 'admin',
        isSuperAdmin: participant.admin === 'superadmin',
        name: participant.name || null
      })) || [];

      const processedMetadata = {
        id: metadata.id,
        subject: metadata.subject,
        desc: metadata.desc,
        creation: metadata.creation,
        participants: participants,
        isAdmin: isAdmin,
        announce: metadata.announce || false,
        restrict: metadata.restrict || false,
        inviteCode: metadata.inviteCode || null,
        size: metadata.size || participants.length
      };

      this.logger.info(`Successfully fetched metadata for group ${groupId}`);

      return {
        success: true,
        metadata: processedMetadata
      };

    } catch (error) {
      this.logger.error(`Error fetching group metadata for ${groupId}:`, error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get group invite code
   */
  async getGroupInviteCode(sessionId, groupId) {
    try {
      const socket = this.sessions.get(sessionId);
      if (!socket) {
        throw new Error('Session not found');
      }

      this.logger.info(`🔗 Getting invite code for group ${groupId} in session ${sessionId}`);

      // First, verify admin status by checking group metadata
      const userJid = socket.user?.id;
      if (userJid) {
        try {
          const groupMetadata = await socket.groupMetadata(groupId);
          const userPhone = userJid.replace('@s.whatsapp.net', '').replace('@c.us', '');

          const userParticipant = groupMetadata.participants?.find(p => {
            const pPhone = p.id.replace('@s.whatsapp.net', '').replace('@c.us', '');
            return pPhone === userPhone;
          });

          if (!userParticipant) {
            this.logger.warn(`❌ User not found in group participants for ${groupId}`);
            throw new Error('You are not a member of this group');
          }

          const isAdmin = userParticipant.admin === 'admin' || userParticipant.admin === 'superadmin' || userParticipant.admin === true;

          if (!isAdmin) {
            this.logger.warn(`❌ User is not admin in group ${groupId}. Admin status: ${userParticipant.admin}`);
            throw new Error('You must be an admin to generate invite links for this group');
          }

          this.logger.info(`✅ Admin verification passed for group ${groupId}`);
        } catch (metadataError) {
          this.logger.warn(`⚠️ Could not verify admin status: ${metadataError.message}`);
          // Continue anyway - let WhatsApp API handle the permission check
        }
      }

      // Attempt to get invite code
      const inviteCode = await socket.groupInviteCode(groupId);

      this.logger.info(`✅ Successfully got invite code for group ${groupId}: ${inviteCode}`);

      return {
        success: true,
        inviteCode: inviteCode,
        inviteLink: `https://chat.whatsapp.com/${inviteCode}`
      };

    } catch (error) {
      this.logger.error(`❌ Error getting invite code for group ${groupId}:`, error);

      // Provide more specific error messages
      let errorMessage = error.message;
      if (error.message.includes('not-admin') || error.message.includes('forbidden')) {
        errorMessage = 'You must be an admin to generate invite links for this group';
      } else if (error.message.includes('not-authorized')) {
        errorMessage = 'Not authorized to access this group';
      } else if (error.message.includes('group-not-found')) {
        errorMessage = 'Group not found';
      }

      return {
        success: false,
        error: errorMessage
      };
    }
  }

  /**
   * Get group info by invite code
   */
  async getGroupInfoByInviteCode(sessionId, inviteCode) {
    try {
      const socket = this.sessions.get(sessionId);
      if (!socket) {
        throw new Error('Session not found');
      }

      // Clean invite code (remove URL if provided)
      const cleanCode = inviteCode.replace('https://chat.whatsapp.com/', '');

      this.logger.info(`Getting group info for invite code ${cleanCode} in session ${sessionId}`);

      const groupInfo = await socket.groupGetInviteInfo(cleanCode);

      return {
        success: true,
        groupInfo: groupInfo
      };

    } catch (error) {
      this.logger.error(`Error getting group info for invite code ${inviteCode}:`, error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get contact info
   */
  async getContactInfo(sessionId, phoneNumber) {
    try {
      const socket = this.sessions.get(sessionId);
      if (!socket) {
        throw new Error('Session not found');
      }

      const contact = await socket.getBusinessProfile(phoneNumber);
      return {
        success: true,
        contact
      };
    } catch (error) {
      this.logger.error(`Error getting contact info for ${phoneNumber}:`, error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get store for a session (for contact name retrieval)
   */
  getStore(sessionId) {
    return this.stores.get(sessionId);
  }



  /**
   * Create a new session specifically for pairing code authentication
   */
  async createPairingCodeSession(phoneNumber) {
    let sessionId = null; // Initialize sessionId at the top level

    try {
      // Clean phone number (remove + and any non-digits) - Context7 requirement
      let cleanPhoneNumber = phoneNumber.replace(/[^0-9]/g, '');

      if (!cleanPhoneNumber || cleanPhoneNumber.length < 10) {
        throw new Error('Invalid phone number format. Please enter a valid phone number with country code (e.g., ************ for India)');
      }

      // Validate phone number format for common countries
      if (cleanPhoneNumber.length < 10 || cleanPhoneNumber.length > 15) {
        throw new Error('Phone number must be between 10-15 digits including country code');
      }

      // E.164 phone number formatting - Following Baileys documentation
      // E.164 format: Country code + national number (WITHOUT + prefix)
      let phoneNumbersToTry = [];

      // Clean and normalize the phone number
      let normalizedNumber = cleanPhoneNumber.replace(/[\s\-\(\)\.]/g, ''); // Remove spaces, dashes, parentheses, dots
      normalizedNumber = normalizedNumber.replace(/^\+/, ''); // Remove leading + if present

      this.logger.info(`🔢 Normalized phone number: "${normalizedNumber}" (length: ${normalizedNumber.length})`);

      // Validate and format according to E.164 standard (without + prefix as per Baileys docs)
      if (normalizedNumber.startsWith('91') && normalizedNumber.length === 12) {
        // Indian number with country code: 91XXXXXXXXXX
        const mobileNumber = normalizedNumber.substring(2);

        // Validate Indian mobile number format (should start with 6-9)
        if (/^[6-9]\d{9}$/.test(mobileNumber)) {
          this.logger.info(`🔢 ✅ Valid Indian number: CC=91, Mobile=${mobileNumber}`);

          // E.164 format for Indian numbers (PRIMARY format as per Baileys docs)
          phoneNumbersToTry = [
            normalizedNumber,  // ************ (E.164 format - RECOMMENDED)
          ];
        } else {
          this.logger.warn(`🔢 ❌ Invalid Indian mobile number format: ${mobileNumber}`);
          throw new Error(`Invalid Indian mobile number format. Must start with 6-9 and be 10 digits.`);
        }
      } else if (normalizedNumber.length === 10 && /^[6-9]\d{9}$/.test(normalizedNumber)) {
        // Indian mobile number without country code: XXXXXXXXXX
        this.logger.info(`🔢 ✅ Valid Indian mobile (no CC): ${normalizedNumber}`);

        // Add country code for E.164 format
        phoneNumbersToTry = [
          `91${normalizedNumber}`, // ************ (E.164 format - RECOMMENDED)
        ];
      } else if (normalizedNumber.startsWith('1') && normalizedNumber.length === 11) {
        // US/Canada number: 1XXXXXXXXXX
        const areaCode = normalizedNumber.substring(1, 4);
        const localNumber = normalizedNumber.substring(4);
        this.logger.info(`🔢 ✅ US/Canada number: CC=1, Area=${areaCode}, Local=${localNumber}`);

        phoneNumbersToTry = [
          normalizedNumber, // 1XXXXXXXXXX (E.164 format)
        ];
      } else if (normalizedNumber.startsWith('44') && normalizedNumber.length >= 12 && normalizedNumber.length <= 13) {
        // UK number: 44XXXXXXXXXXX
        this.logger.info(`🔢 ✅ UK number detected: ${normalizedNumber}`);

        phoneNumbersToTry = [
          normalizedNumber, // 44XXXXXXXXXXX (E.164 format)
        ];
      } else {
        // Generic international number - assume it's already in E.164 format
        this.logger.info(`🔢 ⚠️ Generic international format: ${normalizedNumber}`);

        if (normalizedNumber.length >= 7 && normalizedNumber.length <= 15) {
          phoneNumbersToTry = [
            normalizedNumber, // As provided (assuming E.164)
          ];
        } else {
          throw new Error(`Invalid phone number length. Must be between 7-15 digits for international format.`);
        }
      }

      this.logger.info(`🔢 Will try ${phoneNumbersToTry.length} E.164 phone number format(s): ${phoneNumbersToTry.join(', ')}`);
      this.logger.info(`🔢 📋 Using E.164 format (without + prefix) as required by Baileys documentation`);

      // Generate a new session ID for pairing code
      sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const sessionDir = path.join(this.authDir, sessionId);

      // Ensure session directory exists
      if (!fs.existsSync(sessionDir)) {
        fs.mkdirSync(sessionDir, { recursive: true });
      }

      this.logger.info(`Creating new pairing code session ${sessionId} for ${cleanPhoneNumber}`);
      this.logger.info(`🔢 Phone number details: original="${phoneNumber}", cleaned="${cleanPhoneNumber}", length=${cleanPhoneNumber.length}`);

      const { state, saveCreds } = await this.safeFileOperation(
        `pairing-${sessionId}`,
        () => useMultiFileAuthState(sessionDir)
      );

      // Initialize store for this session
      const store = this.initializeStore(sessionId);

      const socket = makeWASocket({
        auth: state,
        logger: this.logger,
        printQRInTerminal: false, // Critical for pairing code
        browser: Browsers.ubuntu('Lead Wave Desktop'),
        generateHighQualityLinkPreview: true,
        markOnlineOnConnect: false,
        syncFullHistory: false,
        defaultQueryTimeoutMs: 90000, // Increased timeout for pairing stability
        connectTimeoutMs: 90000, // Increased connection timeout
        keepAliveIntervalMs: 25000, // More frequent keep-alive for pairing
        retryRequestDelayMs: 2000, // Longer delay between retries
        maxMsgRetryCount: 5, // More retry attempts for pairing
        qrTimeout: 120000, // 2 minutes QR timeout
        connectCooldownMs: 5000, // Cooldown between connection attempts
        transactionOpts: { maxCommitRetries: 15, delayBetweenTriesMs: 5000 }, // More robust transaction handling
        msgRetryCounterCache: new NodeCache(),
        userDevicesCache: new NodeCache({ stdTTL: 300 }), // 5 minute cache
        cachedGroupMetadata: new NodeCache({ stdTTL: 300 }),
        getMessage: async (key) => {
          if (store) {
            const msg = await store.loadMessage(key.remoteJid, key.id);
            return msg?.message || undefined;
          }
          return undefined;
        }
      });

      // Bind store to socket events
      store.bind(socket.ev);

      this.sessions.set(sessionId, socket);
      this.sessionStates.set(sessionId, {
        id: sessionId,
        status: 'connecting',
        qrCode: null,
        lastSeen: new Date(),
        phoneNumber: null,
        profilePicture: null,
        isLoggedIn: false,
        usingPairingCode: true,
        pairingPhoneNumber: cleanPhoneNumber
      });

      // Handle connection updates
      socket.ev.on('connection.update', async (update) => {
        await this.handleConnectionUpdate(sessionId, update);
      });

      // Handle credential updates
      socket.ev.on('creds.update', saveCreds);

      // Handle incoming messages
      socket.ev.on('messages.upsert', async (messageUpdate) => {
        await this.handleIncomingMessages(sessionId, messageUpdate);
      });

      // Handle contacts updates
      socket.ev.on('contacts.update', async (contacts) => {
        await this.handleContactsUpdate(sessionId, contacts);
      });

      // Wait for socket to be ready - use a more reliable approach
      await new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('Socket connection timeout'));
        }, 45000); // Increased to 45 seconds

        let connectionEstablished = false;
        let qrReceived = false;

        // Listen for connection updates to know when we're ready
        const connectionHandler = (update) => {
          this.logger.info(`Connection update during pairing setup: ${JSON.stringify(update)}`);

          if (update.connection === 'open') {
            connectionEstablished = true;
            if (qrReceived) {
              clearTimeout(timeout);
              socket.ev.off('connection.update', connectionHandler);
              resolve();
            }
          } else if (update.qr) {
            qrReceived = true;
            if (connectionEstablished) {
              clearTimeout(timeout);
              socket.ev.off('connection.update', connectionHandler);
              resolve();
            }
          }

          // If we get QR code, we can proceed even without full connection
          if (update.qr && !connectionEstablished) {
            this.logger.info('QR received, proceeding with pairing code request');
            clearTimeout(timeout);
            socket.ev.off('connection.update', connectionHandler);
            resolve();
          }
        };

        socket.ev.on('connection.update', connectionHandler);

        // Also check if socket is already ready
        if (socket.ws && socket.ws.readyState === 1) {
          clearTimeout(timeout);
          resolve();
        }
      });

      // Small delay to ensure auth state is ready
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Check if client is already registered - Context7 requirement
      // Use try-catch to handle cases where authState might not be fully ready
      try {
        if (socket.authState && socket.authState.creds && socket.authState.creds.registered) {
          throw new Error('Device is already registered. Please use QR code authentication instead.');
        }
      } catch (authError) {
        this.logger.warn(`Auth state check warning for ${sessionId}:`, authError.message);
        // Continue with pairing code generation even if auth state check fails
      }

      // Request pairing code using standard Baileys approach - Following Context7 documentation
      let code;
      let successfulFormat = null;

      this.logger.info(`🔢 Using standard Baileys pairing code method (E.164 format)`);

      // Wait for socket to be ready for pairing code request - Simplified approach
      this.logger.info(`🔢 Waiting for socket to be ready for pairing code request...`);

      // Give the socket a moment to establish connection and generate QR
      await new Promise(resolve => setTimeout(resolve, 3000));

      this.logger.info(`🔢 ✅ Socket should be ready for pairing code request`);

      // Try each phone number format until one works
      for (let i = 0; i < phoneNumbersToTry.length; i++) {
        const currentFormat = phoneNumbersToTry[i];
        this.logger.info(`🔢 Attempt ${i + 1}/${phoneNumbersToTry.length}: Trying E.164 format "${currentFormat}" on session ${sessionId}`);

        try {
          this.logger.info(`🔢 Calling socket.requestPairingCode("${currentFormat}") - Standard Baileys method`);

          // Use standard Baileys pairing code method (no custom parameter)
          code = await socket.requestPairingCode(currentFormat);

          if (code) {
            successfulFormat = currentFormat;
            this.logger.info(`🔢 ✅ SUCCESS! E.164 format "${currentFormat}" worked. Received code: ${code}`);

            // Keep the socket alive and wait for pairing completion
            this.logger.info(`🔢 Pairing code generated, keeping connection alive for pairing...`);

            // Set up enhanced connection monitoring for pairing
            this.setupPairingConnectionMonitoring(socket, sessionId);

            break;
          }
        } catch (error) {
          this.logger.warn(`🔢 ❌ E.164 format "${currentFormat}" failed:`, error.message);

          // If this is not the last format, continue trying
          if (i < phoneNumbersToTry.length - 1) {
            this.logger.info(`🔢 Trying next E.164 format...`);
            await new Promise(resolve => setTimeout(resolve, 2000)); // Longer delay between attempts
          }
        }
      }

      if (!code) {
        throw new Error(`Failed to generate pairing code after trying all formats: ${phoneNumbersToTry.join(', ')}`);
      }

      this.logger.info(`🔢 ✅ ENHANCED PAIRING CODE GENERATED SUCCESSFULLY!`);
      this.logger.info(`🔢 📱 Original input: ${cleanPhoneNumber}`);
      this.logger.info(`🔢 ✅ Successful format: ${successfulFormat}`);
      this.logger.info(`🔢 🔐 Generated code: ${code}`);
      this.logger.info(`🔢 📋 Code format: Standard Baileys pairing code`);

      // Save session to database
      if (this.databaseService && this.databaseService.run) {
        await this.databaseService.run(`
          INSERT INTO whatsapp_sessions (session_id, name, device_name, status, phone_number, created_at, updated_at)
          VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        `, [sessionId, `Device ${cleanPhoneNumber}`, `WhatsApp Device`, 'pairing_code_ready', cleanPhoneNumber]);
      }

      return {
        success: true,
        code: code,
        phoneNumber: cleanPhoneNumber,
        successfulFormat: successfulFormat,
        sessionId: sessionId,
        enhanced: true // Flag to indicate this is using standard Baileys pairing
      };
    } catch (error) {
      this.logger.error(`🔢 Enhanced pairing code generation failed:`, error);
      throw error;
    }
  }

  /**
   * Enhanced connection monitoring for pairing code sessions
   */
  setupPairingConnectionMonitoring(socket, sessionId) {
    this.logger.info(`🔢 Setting up enhanced connection monitoring for pairing session: ${sessionId}`);

    // Monitor for successful pairing
    const pairingSuccessHandler = (update) => {
      if (update.connection === 'open') {
        this.logger.info(`🔢 ✅ PAIRING SUCCESSFUL! Session ${sessionId} is now connected`);
        this.emit('pairing_success', { sessionId, timestamp: new Date() });
      }
    };

    // Monitor for pairing errors
    const pairingErrorHandler = (error) => {
      this.logger.warn(`🔢 ⚠️ Pairing connection error for ${sessionId}:`, error.message);

      // Handle specific error codes
      if (error.message.includes('503')) {
        this.logger.warn(`🔢 503 Service Unavailable - WhatsApp servers may be busy`);
        this.emit('pairing_error', {
          sessionId,
          error: 'WhatsApp servers are temporarily unavailable. Please try again in a few minutes.',
          code: '503',
          timestamp: new Date()
        });
      } else if (error.message.includes('Stream Errored')) {
        this.logger.warn(`🔢 Stream error - Connection interrupted during pairing`);
        this.emit('pairing_error', {
          sessionId,
          error: 'Connection was interrupted. Please try generating a new pairing code.',
          code: 'STREAM_ERROR',
          timestamp: new Date()
        });
      }
    };

    // Add event listeners
    socket.ev.on('connection.update', pairingSuccessHandler);
    socket.ev.on('connection.error', pairingErrorHandler);

    // Clean up listeners after 5 minutes
    setTimeout(() => {
      socket.ev.off('connection.update', pairingSuccessHandler);
      socket.ev.off('connection.error', pairingErrorHandler);
      this.logger.info(`🔢 Cleaned up pairing monitoring for session: ${sessionId}`);
    }, 5 * 60 * 1000);
  }

  /**
   * Request pairing code for phone number - Following Context7 documentation
   */
  async requestPairingCode(sessionId, phoneNumber) {
    try {
      const socket = this.sessions.get(sessionId);
      if (!socket) {
        throw new Error('Session not found or not connected');
      }

      // Clean phone number (remove + and any non-digits) - Context7 requirement
      const cleanPhoneNumber = phoneNumber.replace(/[^0-9]/g, '');

      if (!cleanPhoneNumber || cleanPhoneNumber.length < 10) {
        throw new Error('Invalid phone number format');
      }

      this.logger.info(`Requesting pairing code for ${cleanPhoneNumber} on session ${sessionId}`);

      // Check if client is already registered - Context7 requirement
      if (socket.authState.creds.registered) {
        throw new Error('Device is already registered. Please disconnect and create a new session for pairing code authentication.');
      }

      // Mark this session as using pairing code to prevent QR generation
      const sessionState = this.sessionStates.get(sessionId);
      if (sessionState) {
        sessionState.usingPairingCode = true;
        sessionState.pairingPhoneNumber = cleanPhoneNumber;
        this.sessionStates.set(sessionId, sessionState);
      }

      // Request pairing code from Baileys - Context7 pattern
      const code = await socket.requestPairingCode(cleanPhoneNumber);

      this.logger.info(`Pairing code generated for ${cleanPhoneNumber}: ${code}`);

      return {
        success: true,
        code: code,
        phoneNumber: cleanPhoneNumber,
        sessionId: sessionId
      };

    } catch (error) {
      this.logger.error(`Error requesting pairing code for ${phoneNumber}:`, error);
      return {
        success: false,
        error: error.message,
        phoneNumber: phoneNumber,
        sessionId: sessionId
      };
    }
  }

  /**
   * Force reconnect session (for manual reconnection with QR)
   */
  async forceReconnectSession(sessionId) {
    try {
      this.logger.info(`🔄 Force reconnecting session ${sessionId}...`);

      // Remove existing session from memory if it exists
      const existingSocket = this.sessions.get(sessionId);
      if (existingSocket) {
        try {
          await existingSocket.end();
        } catch (error) {
          // Ignore errors when closing
        }
        this.sessions.delete(sessionId);
      }

      // Clear session state
      this.sessionStates.delete(sessionId);

      // Clear auth files to force new QR generation
      const sessionDir = path.join(this.authDir, sessionId);
      if (fs.existsSync(sessionDir)) {
        const authFiles = ['creds.json', 'keys.json', 'session-info.json'];
        for (const file of authFiles) {
          const filePath = path.join(sessionDir, file);
          if (fs.existsSync(filePath)) {
            fs.unlinkSync(filePath);
            this.logger.info(`Cleared auth file: ${file}`);
          }
        }
      }

      // Create fresh session
      return await this.createSession(sessionId);

    } catch (error) {
      this.logger.error(`Error force reconnecting session ${sessionId}:`, error);
      return {
        success: false,
        message: error.message
      };
    }
  }

  /**
   * Restart session after QR scan (internal method)
   */
  async restartSession(sessionId) {
    try {
      const sessionDir = path.join(this.authDir, sessionId);
      if (!fs.existsSync(sessionDir)) {
        fs.mkdirSync(sessionDir, { recursive: true });
      }

      const { state, saveCreds } = await this.safeFileOperation(
        `restart-${sessionId}`,
        () => useMultiFileAuthState(sessionDir)
      );

      const socket = makeWASocket({
        auth: state,
        logger: this.logger,
        printQRInTerminal: false,
        browser: Browsers.ubuntu('Lead Wave Desktop'),
        generateHighQualityLinkPreview: true,
        markOnlineOnConnect: false,
        syncFullHistory: false,
        defaultQueryTimeoutMs: 60000,
      });

      this.sessions.set(sessionId, socket);
      
      // Update session state
      const sessionState = this.sessionStates.get(sessionId) || {};
      sessionState.status = 'connecting';
      this.sessionStates.set(sessionId, sessionState);

      // Handle connection updates
      socket.ev.on('connection.update', async (update) => {
        await this.handleConnectionUpdate(sessionId, update);
      });

      // Handle credential updates
      socket.ev.on('creds.update', saveCreds);

      // Handle incoming messages
      socket.ev.on('messages.upsert', async (messageUpdate) => {
        await this.handleIncomingMessages(sessionId, messageUpdate);
      });

      // Handle contacts updates
      socket.ev.on('contacts.update', async (contacts) => {
        await this.handleContactsUpdate(sessionId, contacts);
      });

      // Handle calls (both incoming and outgoing)
      socket.ev.on('call', async (calls) => {
        await this.handleCalls(sessionId, calls);
      });

      // Handle presence updates
      socket.ev.on('presence.update', async (presence) => {
        await this.handlePresenceUpdate(sessionId, presence);
      });

      this.logger.info(`Session ${sessionId} restarted successfully`);
      
      return {
        success: true,
        message: 'Session restarted successfully'
      };

    } catch (error) {
      this.logger.error(`Error restarting session ${sessionId}:`, error);
      return {
        success: false,
        message: error.message
      };
    }
  }
  /**
   * Shutdown the service gracefully
   */
  async shutdown() {
    this.isShuttingDown = true;

    try {
      // Wait for any ongoing file operations to complete
      const pendingOperations = Array.from(this.fileOperationLocks.values());
      if (pendingOperations.length > 0) {
        console.log(`Waiting for ${pendingOperations.length} file operations to complete...`);
        await Promise.allSettled(pendingOperations);
      }

      // Close all sessions
      for (const [sessionId, socket] of this.sessions.entries()) {
        try {
          if (socket && typeof socket.end === 'function') {
            await socket.end();
          }
        } catch (error) {
          console.warn(`Error closing session ${sessionId}:`, error.message);
        }
      }

      // Clean up stores
      for (const sessionId of this.stores.keys()) {
        this.cleanupStore(sessionId);
      }

      // Clear all maps
      this.sessions.clear();
      this.sessionStates.clear();
      this.stores.clear();
      this.fileOperationLocks.clear();

      console.log('WhatsApp service shutdown complete');
    } catch (error) {
      console.error('Error during WhatsApp service shutdown:', error);
    }
  }
}

module.exports = WhatsAppService;