const initSqlJs = require('sql.js');
const fs = require('fs');
const path = require('path');

async function monitorProductionDatabase() {
  console.log('🔍 Monitoring PRODUCTION database for new messages...');
  console.log('📱 Send "PHP" from WhatsApp now!\n');
  
  // Use the actual production database path
  const dbPath = path.join(process.env.APPDATA, 'Electron', 'leadwave.db');
  
  if (!fs.existsSync(dbPath)) {
    console.log('❌ Production database not found at:', dbPath);
    return;
  }
  
  // Get initial message count
  let lastMessageId = 0;
  
  try {
    const SQL = await initSqlJs();
    let filebuffer = fs.readFileSync(dbPath);
    let db = new SQL.Database(filebuffer);
    
    const initialCount = db.exec('SELECT MAX(id) as max_id FROM message_history');
    if (initialCount.length > 0 && initialCount[0].values.length > 0) {
      lastMessageId = initialCount[0].values[0][0] || 0;
    }
    db.close();
    
    console.log(`📊 Starting from message ID: ${lastMessageId}`);
    console.log('🔄 Monitoring for new messages...\n');
    
  } catch (error) {
    console.error('❌ Error reading initial database:', error.message);
    return;
  }
  
  // Monitor for new messages every 2 seconds
  const monitorInterval = setInterval(async () => {
    try {
      const SQL = await initSqlJs();
      const filebuffer = fs.readFileSync(dbPath);
      const db = new SQL.Database(filebuffer);
      
      // Check for new messages
      const newMessages = db.exec(`
        SELECT id, session_id, content, message_type, direction, created_at
        FROM message_history 
        WHERE id > ?
        ORDER BY id ASC
      `, [lastMessageId]);
      
      if (newMessages.length > 0 && newMessages[0].values.length > 0) {
        console.log(`\n🆕 NEW MESSAGE(S) DETECTED! (${newMessages[0].values.length} new)`);
        
        newMessages[0].values.forEach((row, index) => {
          const [id, sessionId, content, messageType, direction, createdAt] = row;
          console.log(`   ${index + 1}. [${direction}] "${content}" (${messageType})`);
          console.log(`      ID: ${id}, Session: ${sessionId}`);
          
          // Check if this is a PHP message
          if (content.toLowerCase().includes('php') || content.toLowerCase().includes('development')) {
            console.log(`      🎯 PHP-RELATED MESSAGE DETECTED!`);
            
            // Test keyword matching manually
            const keywords = ['php', 'development'];
            const messageText = content.toLowerCase().trim();
            
            keywords.forEach(keyword => {
              const match = messageText.includes(keyword);
              console.log(`         "${keyword}" matches "${messageText}": ${match ? '✅ YES' : '❌ NO'}`);
            });
            
            // Check flows for this session
            console.log(`         🔍 Checking flows for session ${sessionId}...`);
            
            const flows = db.exec(`
              SELECT id, name, trigger_keywords FROM chatbot_flows 
              WHERE session_id = ? AND is_active = 1
            `, [sessionId]);
            
            if (flows.length > 0 && flows[0].values.length > 0) {
              console.log(`         ✅ Found ${flows[0].values.length} active flow(s):`);
              flows[0].values.forEach(flowRow => {
                const [flowId, flowName, triggerKeywords] = flowRow;
                console.log(`            - "${flowName}" (ID: ${flowId}) with keywords: "${triggerKeywords}"`);
                
                // Test if keywords match
                const flowKeywords = triggerKeywords.toLowerCase().split(',').map(k => k.trim());
                const hasMatch = flowKeywords.some(keyword => messageText.includes(keyword));
                console.log(`            - Should trigger: ${hasMatch ? '✅ YES' : '❌ NO'}`);
                
                if (hasMatch) {
                  // Check for nodes
                  const nodes = db.exec('SELECT COUNT(*) as count FROM chatbot_nodes WHERE flow_id = ?', [flowId]);
                  const nodeCount = nodes.length > 0 ? nodes[0].values[0][0] : 0;
                  console.log(`            - Has ${nodeCount} node(s)`);
                  
                  // Check for active conversations
                  const activeConvs = db.exec(`
                    SELECT COUNT(*) as count FROM chatbot_conversations 
                    WHERE session_id = ? AND status = 'active'
                  `, [sessionId]);
                  const activeCount = activeConvs.length > 0 ? activeConvs[0].values[0][0] : 0;
                  console.log(`            - Active conversations: ${activeCount}`);
                  
                  if (activeCount > 0) {
                    console.log(`            🚨 ACTIVE CONVERSATIONS BLOCKING NEW TRIGGERS!`);
                  } else if (nodeCount === 0) {
                    console.log(`            🚨 NO NODES - FLOW CANNOT START!`);
                  } else {
                    console.log(`            ✅ Should work - investigating why it doesn't...`);
                  }
                }
              });
            } else {
              console.log(`         ❌ No active flows found for session ${sessionId}`);
            }
          }
          
          lastMessageId = Math.max(lastMessageId, id);
        });
      }
      
      db.close();
      
    } catch (error) {
      console.error('❌ Error monitoring:', error.message);
    }
  }, 2000);
  
  // Stop monitoring after 2 minutes
  setTimeout(() => {
    clearInterval(monitorInterval);
    console.log('\n⏰ Monitoring stopped after 2 minutes');
    process.exit(0);
  }, 120000);
  
  console.log('⏰ Monitoring will stop automatically in 2 minutes...');
}

monitorProductionDatabase().catch(console.error);
