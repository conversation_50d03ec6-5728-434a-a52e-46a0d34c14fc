const initSqlJs = require('sql.js');
const fs = require('fs');
const path = require('path');
const os = require('os');

async function forceMigrateAllDatabases() {
  console.log('🔧 FORCE MIGRATING ALL DATABASE LOCATIONS...');
  
  // All possible database paths including development
  const possiblePaths = [
    // Development paths
    path.join(process.cwd(), 'data', 'leadwave.db'),
    path.join(process.cwd(), 'leadwave.db'),
    path.join(process.cwd(), 'database', 'leadwave.db'),
    path.join(process.cwd(), 'src', 'database', 'leadwave.db'),
    path.join(process.cwd(), 'build', 'leadwave.db'),
    path.join(process.cwd(), 'temp-fix', 'leadwave.db'),
    path.join(process.cwd(), 'temp-fix', 'data', 'leadwave.db'),
    
    // Standard Electron userData paths
    path.join(os.homedir(), 'AppData', 'Roaming', 'Electron', 'leadwave.db'),
    path.join(os.homedir(), 'AppData', 'Roaming', 'leadwave-whatsapp-desktop', 'leadwave.db'),
    path.join(os.homedir(), 'AppData', 'Roaming', 'Lead Wave', 'leadwave.db'),
    path.join(os.homedir(), 'AppData', 'Roaming', 'LeadWave', 'leadwave.db'),
    path.join(os.homedir(), 'AppData', 'Roaming', 'WAZBIT', 'leadwave.db'),
    path.join(os.homedir(), 'AppData', 'Local', 'Electron', 'leadwave.db'),
    path.join(os.homedir(), 'AppData', 'Local', 'leadwave-whatsapp-desktop', 'leadwave.db'),
    path.join(os.homedir(), 'AppData', 'Local', 'Lead Wave', 'leadwave.db'),
    path.join(os.homedir(), 'AppData', 'Local', 'LeadWave', 'leadwave.db'),
    path.join(os.homedir(), 'AppData', 'Local', 'WAZBIT', 'leadwave.db'),
    
    // Alternative paths
    path.join(os.homedir(), 'Lead Wave', 'leadwave.db'),
    path.join(os.homedir(), 'LeadWave', 'leadwave.db'),
    path.join(os.homedir(), 'WAZBIT', 'leadwave.db')
  ];
  
  const SQL = await initSqlJs();
  let migratedCount = 0;
  let foundDatabases = [];
  
  console.log('\n📁 Scanning for ALL database files...');
  
  for (const dbPath of possiblePaths) {
    if (!fs.existsSync(dbPath)) {
      continue;
    }
    
    console.log(`\n✅ Found database: ${dbPath}`);
    foundDatabases.push(dbPath);
    
    try {
      const filebuffer = fs.readFileSync(dbPath);
      const database = new SQL.Database(filebuffer);
      
      // Check if it has chatbot_flows table
      const tables = database.exec("SELECT name FROM sqlite_master WHERE type='table' AND name='chatbot_flows'");
      
      if (tables.length === 0) {
        console.log('   ⚠️  No chatbot_flows table found, skipping...');
        database.close();
        continue;
      }
      
      // Check current schema
      const tableInfo = database.exec('PRAGMA table_info(chatbot_flows)');
      const columns = tableInfo[0].values.map(row => row[1]);
      
      console.log(`   📋 Current columns: ${columns.slice(0, 8).join(', ')}...`);
      
      let needsMigration = false;
      
      // FORCE ADD keyword_match_type column
      if (!columns.includes('keyword_match_type')) {
        console.log('   🔧 ADDING keyword_match_type column...');
        database.exec("ALTER TABLE chatbot_flows ADD COLUMN keyword_match_type TEXT DEFAULT 'contains'");
        needsMigration = true;
      } else {
        console.log('   ✅ keyword_match_type column exists');
      }
      
      // FORCE ADD keyword_case_sensitive column
      if (!columns.includes('keyword_case_sensitive')) {
        console.log('   🔧 ADDING keyword_case_sensitive column...');
        database.exec("ALTER TABLE chatbot_flows ADD COLUMN keyword_case_sensitive BOOLEAN DEFAULT 0");
        needsMigration = true;
      } else {
        console.log('   ✅ keyword_case_sensitive column exists');
      }
      
      // Always update existing flows with default values
      console.log('   🔧 Updating existing flows with default values...');
      database.exec(`
        UPDATE chatbot_flows 
        SET keyword_match_type = COALESCE(keyword_match_type, 'contains'),
            keyword_case_sensitive = COALESCE(keyword_case_sensitive, 0)
      `);
      
      // Save the updated database
      const data = database.export();
      fs.writeFileSync(dbPath, Buffer.from(data));
      console.log('   ✅ Database updated and saved');
      migratedCount++;
      
      // Show flows in this database
      const flows = database.exec('SELECT id, name, keyword_match_type, keyword_case_sensitive FROM chatbot_flows');
      if (flows.length > 0 && flows[0].values.length > 0) {
        console.log('   📊 Flows in this database:');
        flows[0].values.forEach(row => {
          const [id, name, matchType, caseSensitive] = row;
          console.log(`     - "${name}": match_type=${matchType}, case_sensitive=${caseSensitive}`);
        });
      } else {
        console.log('   📊 No flows found in this database');
      }
      
      database.close();
      
    } catch (error) {
      console.error(`   ❌ Error processing database: ${error.message}`);
    }
  }
  
  console.log('\n📊 MIGRATION SUMMARY:');
  console.log(`   📁 Found databases: ${foundDatabases.length}`);
  console.log(`   🔧 Processed databases: ${migratedCount}`);
  
  if (foundDatabases.length > 0) {
    console.log('\n📍 Database locations found:');
    foundDatabases.forEach((dbPath, index) => {
      console.log(`   ${index + 1}. ${dbPath}`);
    });
  }
  
  console.log('\n🎉 FORCE MIGRATION COMPLETE!');
  console.log('🔄 Please restart the LeadWave application now.');
  console.log('📱 The keyword matching options should now work properly.');
}

forceMigrateAllDatabases().catch(console.error);
