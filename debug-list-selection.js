const DatabaseService = require('./src/services/database.service');
const MessageProcessor = require('./src/services/message-processor.service');
const EventService = require('./src/services/event.service');

async function debugListSelection() {
  console.log('🔍 Starting List Selection Debug...');
  
  const db = new DatabaseService();
  await db.initialize();
  
  const messageProcessor = new MessageProcessor(db);
  const eventService = new EventService(db, messageProcessor);
  
  // Override parseIncomingMessage to add detailed debugging
  const originalParseIncomingMessage = messageProcessor.parseIncomingMessage.bind(messageProcessor);
  messageProcessor.parseIncomingMessage = function(message) {
    console.log('\n📨 RAW MESSAGE RECEIVED:');
    console.log('   Message keys:', Object.keys(message).join(', '));
    
    if (message.message) {
      console.log('   Message.message keys:', Object.keys(message.message).join(', '));
      
      // Check for different types of list responses
      if (message.message.listResponseMessage) {
        console.log('   📋 LIST RESPONSE MESSAGE DETECTED!');
        console.log('   Full listResponseMessage:', JSON.stringify(message.message.listResponseMessage, null, 2));
      }
      
      if (message.message.interactiveResponseMessage) {
        console.log('   🔄 INTERACTIVE RESPONSE MESSAGE DETECTED!');
        console.log('   Full interactiveResponseMessage:', JSON.stringify(message.message.interactiveResponseMessage, null, 2));
      }
      
      if (message.message.buttonsResponseMessage) {
        console.log('   🔘 BUTTONS RESPONSE MESSAGE DETECTED!');
        console.log('   Full buttonsResponseMessage:', JSON.stringify(message.message.buttonsResponseMessage, null, 2));
      }
      
      if (message.message.templateButtonReplyMessage) {
        console.log('   📄 TEMPLATE BUTTON REPLY MESSAGE DETECTED!');
        console.log('   Full templateButtonReplyMessage:', JSON.stringify(message.message.templateButtonReplyMessage, null, 2));
      }
      
      // Check for conversation or extended text
      if (message.message.conversation) {
        console.log('   💬 CONVERSATION MESSAGE:', message.message.conversation);
      }
      
      if (message.message.extendedTextMessage) {
        console.log('   📝 EXTENDED TEXT MESSAGE:', JSON.stringify(message.message.extendedTextMessage, null, 2));
      }
    }
    
    const parsed = originalParseIncomingMessage(message);
    console.log('   ✅ PARSED RESULT:');
    console.log('     Text:', parsed.text);
    console.log('     Type:', parsed.type);
    console.log('     From:', parsed.from);
    console.log('     FromMe:', parsed.fromMe);
    
    return parsed;
  };
  
  // Override checkChatbotTriggers to add debugging
  const originalCheckChatbotTriggers = eventService.checkChatbotTriggers.bind(eventService);
  eventService.checkChatbotTriggers = async function(sessionId, message) {
    console.log('\n🤖 CHATBOT TRIGGER CHECK:');
    console.log('   Session ID:', sessionId);
    
    const parsed = this.messageProcessor.parseIncomingMessage(message);
    console.log('   Parsed text for chatbot:', parsed.text);
    console.log('   Should trigger chatbot:', !parsed.fromMe && parsed.text?.trim());
    
    // Check for existing flows
    const flows = await this.databaseService.all('SELECT * FROM chatbot_flows WHERE is_active = 1');
    if (flows.success && flows.data) {
      console.log('   Available flows:');
      flows.data.forEach(flow => {
        const keywords = flow.trigger_keywords ? flow.trigger_keywords.split(',').map(k => k.trim().toLowerCase()) : [];
        const matches = keywords.some(keyword => parsed.text.toLowerCase().includes(keyword));
        console.log(`     - "${flow.name}": keywords [${keywords.join(', ')}] - matches: ${matches}`);
      });
    }
    
    return originalCheckChatbotTriggers(sessionId, message);
  };
  
  // Override handleMessageReceived to monitor all message processing
  const originalHandleMessageReceived = eventService.handleMessageReceived.bind(eventService);
  eventService.handleMessageReceived = async function(data) {
    console.log('\n📥 MESSAGE RECEIVED EVENT:');
    console.log('   Session ID:', data.sessionId);
    console.log('   Message timestamp:', data.message.messageTimestamp);
    console.log('   Message from:', data.message.key?.remoteJid);
    console.log('   Message fromMe:', data.message.key?.fromMe);
    
    return originalHandleMessageReceived(data);
  };
  
  console.log('✅ Debug overrides installed');
  console.log('📱 Now send a list selection in WhatsApp and watch the output...');
  console.log('🛑 Press Ctrl+C to stop debugging');
  
  // Keep the script running
  process.on('SIGINT', () => {
    console.log('\n\n🛑 Stopping debug...');
    process.exit(0);
  });
  
  // Keep alive
  setInterval(() => {
    // Just keep the process alive
  }, 1000);
}

debugListSelection().catch(console.error);
