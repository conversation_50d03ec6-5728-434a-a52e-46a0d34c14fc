const MessageProcessor = require('./src/services/message-processor.service');

function testKeywordMatchingOptions() {
  console.log('🧪 Testing new keyword matching options...\n');
  
  const messageProcessor = new MessageProcessor();
  
  // Test cases
  const testCases = [
    {
      message: 'PHP',
      keyword: 'php',
      caseSensitive: false,
      matchType: 'contains',
      expected: true,
      description: 'Case-insensitive contains match'
    },
    {
      message: 'PHP',
      keyword: 'php',
      caseSensitive: true,
      matchType: 'contains',
      expected: false,
      description: 'Case-sensitive contains match (should fail)'
    },
    {
      message: 'PHP',
      keyword: 'PHP',
      caseSensitive: true,
      matchType: 'exact',
      expected: true,
      description: 'Case-sensitive exact match'
    },
    {
      message: 'PHP Development',
      keyword: 'PHP',
      caseSensitive: true,
      matchType: 'starts_with',
      expected: true,
      description: 'Case-sensitive starts with'
    },
    {
      message: 'I need PHP help',
      keyword: 'help',
      caseSensitive: false,
      matchType: 'ends_with',
      expected: true,
      description: 'Case-insensitive ends with'
    },
    {
      message: 'php',
      keyword: 'PHP',
      caseSensitive: false,
      matchType: 'exact',
      expected: true,
      description: 'Case-insensitive exact match'
    }
  ];
  
  console.log('📋 Test Results:');
  console.log('================');
  
  let passed = 0;
  let failed = 0;
  
  testCases.forEach((test, index) => {
    // Prepare message and keyword based on case sensitivity
    const testMessage = test.caseSensitive ? test.message : test.message.toLowerCase();
    const testKeyword = test.caseSensitive ? test.keyword : test.keyword.toLowerCase();
    
    // Run the test
    const result = messageProcessor.matchesKeyword(testMessage, testKeyword, test.matchType);
    const success = result === test.expected;
    
    console.log(`\n${index + 1}. ${test.description}`);
    console.log(`   Message: "${test.message}"`);
    console.log(`   Keyword: "${test.keyword}"`);
    console.log(`   Match Type: ${test.matchType}`);
    console.log(`   Case Sensitive: ${test.caseSensitive}`);
    console.log(`   Expected: ${test.expected}`);
    console.log(`   Result: ${result}`);
    console.log(`   Status: ${success ? '✅ PASS' : '❌ FAIL'}`);
    
    if (success) {
      passed++;
    } else {
      failed++;
    }
  });
  
  console.log('\n📊 Summary:');
  console.log(`   ✅ Passed: ${passed}`);
  console.log(`   ❌ Failed: ${failed}`);
  console.log(`   📈 Success Rate: ${Math.round((passed / testCases.length) * 100)}%`);
  
  if (failed === 0) {
    console.log('\n🎉 All tests passed! The new keyword matching options are working correctly.');
  } else {
    console.log('\n⚠️  Some tests failed. Please check the implementation.');
  }
  
  // Test the specific PHP case
  console.log('\n🎯 Testing your specific PHP case:');
  console.log('===================================');
  
  const phpTests = [
    { message: 'PHP', keywords: 'php,development', matchType: 'contains', caseSensitive: false },
    { message: 'PHP', keywords: 'php,development', matchType: 'exact', caseSensitive: false },
    { message: 'PHP', keywords: 'PHP,Development', matchType: 'exact', caseSensitive: true },
    { message: 'Development', keywords: 'php,development', matchType: 'contains', caseSensitive: false }
  ];
  
  phpTests.forEach((test, index) => {
    const keywords = test.caseSensitive 
      ? test.keywords.split(',').map(k => k.trim())
      : test.keywords.toLowerCase().split(',').map(k => k.trim());
    
    const testMessage = test.caseSensitive ? test.message : test.message.toLowerCase();
    
    const hasMatch = keywords.some(keyword => 
      messageProcessor.matchesKeyword(testMessage, keyword, test.matchType)
    );
    
    console.log(`\n${index + 1}. Message: "${test.message}"`);
    console.log(`   Keywords: [${keywords.join(', ')}]`);
    console.log(`   Match Type: ${test.matchType}`);
    console.log(`   Case Sensitive: ${test.caseSensitive}`);
    console.log(`   Result: ${hasMatch ? '✅ MATCH' : '❌ NO MATCH'}`);
  });
  
  console.log('\n💡 Recommendations for your PHP flow:');
  console.log('   • Use "Contains" match type for flexible matching');
  console.log('   • Disable "Match case" for case-insensitive matching');
  console.log('   • This will match "PHP", "php", "Php", etc.');
}

testKeywordMatchingOptions();
