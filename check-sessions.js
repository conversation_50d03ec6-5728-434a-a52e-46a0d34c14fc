const DatabaseService = require('./src/services/database.service');

async function checkSessions() {
  console.log('🔍 Checking WhatsApp Sessions...');
  
  const db = new DatabaseService();
  await db.initialize();
  
  console.log('\n📱 All Sessions in Database:');
  const allSessions = await db.all('SELECT * FROM whatsapp_sessions');
  if (allSessions.success && allSessions.data) {
    allSessions.data.forEach(session => {
      console.log(`  - ID: ${session.id}, Session Key: ${session.session_id}`);
      console.log(`    Name: ${session.name}`);
      console.log(`    Status: ${session.status}`);
      console.log(`    Is Active: ${session.is_active}`);
      console.log(`    Phone: ${session.phone_number}`);
      console.log(`    Created: ${session.created_at}`);
      console.log('    ---');
    });
  } else {
    console.log('  No sessions found in database');
  }
  
  console.log('\n🟢 Active Sessions:');
  const activeSessions = await db.all('SELECT * FROM whatsapp_sessions WHERE is_active = 1');
  if (activeSessions.success && activeSessions.data && activeSessions.data.length > 0) {
    activeSessions.data.forEach(session => {
      console.log(`  ✅ ${session.name} (${session.session_id})`);
      console.log(`     Status: ${session.status}`);
      console.log(`     Phone: ${session.phone_number}`);
    });
  } else {
    console.log('  ❌ No active sessions found');
  }
  
  console.log('\n🔗 Connected Sessions:');
  const connectedSessions = await db.all('SELECT * FROM whatsapp_sessions WHERE status = "connected"');
  if (connectedSessions.success && connectedSessions.data && connectedSessions.data.length > 0) {
    connectedSessions.data.forEach(session => {
      console.log(`  ✅ ${session.name} (${session.session_id})`);
      console.log(`     Is Active: ${session.is_active}`);
      console.log(`     Phone: ${session.phone_number}`);
    });
  } else {
    console.log('  ❌ No connected sessions found');
  }
  
  console.log('\n💡 DIAGNOSIS:');
  if (!activeSessions.success || !activeSessions.data || activeSessions.data.length === 0) {
    console.log('❌ ISSUE: No active WhatsApp sessions found');
    console.log('🔧 SOLUTION: You need to connect a WhatsApp session first');
    console.log('📱 Steps:');
    console.log('   1. Open the Lead Wave application');
    console.log('   2. Go to WhatsApp Sessions');
    console.log('   3. Add a new session or reconnect existing session');
    console.log('   4. Scan QR code or use pairing code');
    console.log('   5. Wait for connection to be established');
  } else if (!connectedSessions.success || !connectedSessions.data || connectedSessions.data.length === 0) {
    console.log('⚠️ ISSUE: Sessions exist but none are connected');
    console.log('🔧 SOLUTION: Reconnect your WhatsApp session');
    console.log('📱 Steps:');
    console.log('   1. Open the Lead Wave application');
    console.log('   2. Go to WhatsApp Sessions');
    console.log('   3. Click reconnect on your session');
    console.log('   4. Follow the connection process');
  } else {
    console.log('✅ Sessions look good - the issue might be elsewhere');
  }
  
  process.exit(0);
}

checkSessions().catch(console.error);
