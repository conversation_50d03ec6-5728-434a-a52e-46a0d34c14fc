const DatabaseService = require('./src/services/database.service');

async function checkDbSessions() {
  console.log('🔍 Checking Database Sessions Directly...');
  
  const db = new DatabaseService();
  await db.initialize();
  
  console.log('\n📊 Raw Session Data:');
  const allSessions = await db.all('SELECT * FROM whatsapp_sessions');
  if (allSessions.success && allSessions.data) {
    console.log(`Found ${allSessions.data.length} sessions in database:`);
    allSessions.data.forEach((session, index) => {
      console.log(`\n${index + 1}. Session Details:`);
      console.log(`   ID: ${session.id}`);
      console.log(`   Session Key: ${session.session_id}`);
      console.log(`   Name: ${session.name}`);
      console.log(`   Phone: ${session.phone_number}`);
      console.log(`   Status: "${session.status}"`);
      console.log(`   Is Active: ${session.is_active}`);
      console.log(`   Created: ${session.created_at}`);
      console.log(`   Updated: ${session.updated_at}`);
    });
  } else {
    console.log('❌ No sessions found or query failed');
  }
  
  console.log('\n🔍 Checking Session with ytrty name:');
  const ytrtySessions = await db.all('SELECT * FROM whatsapp_sessions WHERE name LIKE "%ytrty%"');
  if (ytrtySessions.success && ytrtySessions.data && ytrtySessions.data.length > 0) {
    ytrtySessions.data.forEach(session => {
      console.log(`  Found: ${session.name} - Status: "${session.status}", Active: ${session.is_active}`);
    });
  } else {
    console.log('  ❌ No sessions found with ytrty name');
  }
  
  console.log('\n🔍 Checking Session with phone +917261902348:');
  const phoneSessions = await db.all('SELECT * FROM whatsapp_sessions WHERE phone_number LIKE "%917261902348%"');
  if (phoneSessions.success && phoneSessions.data && phoneSessions.data.length > 0) {
    phoneSessions.data.forEach(session => {
      console.log(`  Found: ${session.name} - Status: "${session.status}", Active: ${session.is_active}`);
    });
  } else {
    console.log('  ❌ No sessions found with that phone number');
  }
  
  console.log('\n📋 Chatbot Flow Details:');
  const flows = await db.all('SELECT * FROM chatbot_flows');
  if (flows.success && flows.data && flows.data.length > 0) {
    flows.data.forEach(flow => {
      console.log(`  Flow: "${flow.name}"`);
      console.log(`    Keywords: "${flow.trigger_keywords}"`);
      console.log(`    Active: ${flow.is_active}`);
      console.log(`    Session ID: ${flow.whatsapp_session_id || 'Any'}`);
    });
  } else {
    console.log('  ❌ No chatbot flows found');
  }
  
  console.log('\n💡 ANALYSIS:');
  if (!allSessions.success || !allSessions.data || allSessions.data.length === 0) {
    console.log('❌ No sessions exist in database at all');
  } else {
    const connectedSessions = allSessions.data.filter(s => s.status === 'connected' && s.is_active);
    if (connectedSessions.length === 0) {
      console.log('❌ Sessions exist but none are marked as connected and active');
      console.log('💡 This explains why messages aren\'t being processed');
      console.log('🔧 The UI might show connected but database status is different');
    } else {
      console.log('✅ Found connected sessions - issue might be elsewhere');
    }
  }
  
  process.exit(0);
}

checkDbSessions().catch(console.error);
