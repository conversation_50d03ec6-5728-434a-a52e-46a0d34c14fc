const pino = require('pino');

/**
 * Message Processing Service
 * Handles message processing, template rendering, and content formatting
 */
class MessageProcessorService {
  constructor(databaseService) {
    this.databaseService = databaseService;
    this.logger = pino({ level: 'info' });
  }

  /**
   * Process and render a template with variables
   */
  async processTemplate(templateId, variables = {}) {
    try {
      const templateResult = await this.databaseService.get(
        'SELECT * FROM message_templates WHERE id = ?',
        [templateId]
      );

      if (!templateResult) {
        throw new Error(`Template with ID ${templateId} not found`);
      }

      // Handle both direct result and wrapped result
      const template = templateResult.data || templateResult;

      if (!template) {
        throw new Error(`Template data with ID ${templateId} not found`);
      }

      let content = template.content;

      // Replace variables in template
      Object.keys(variables).forEach(key => {
        const regex = new RegExp(`{{${key}}}`, 'g');
        content = content.replace(regex, variables[key] || '');
      });

      // Replace common variables
      const now = new Date();
      content = content.replace(/{{date}}/g, now.toLocaleDateString());
      content = content.replace(/{{time}}/g, now.toLocaleTimeString());
      content = content.replace(/{{datetime}}/g, now.toLocaleString());

      // Build metadata based on template type
      const metadata = {};
      const templateType = template.type || 'text';

      // Handle different template types
      switch (templateType) {
        case 'image':
        case 'video':
        case 'audio':
        case 'document':
          if (template.attachments) {
            const attachments = JSON.parse(template.attachments);
            if (attachments.length > 0) {
              // For media templates, we don't modify the content here
              // The attachment will be handled separately in the WhatsApp service
              // Keep the original content as the caption/message text
              this.logger.info(`Processing ${templateType} template with attachment`);
            }
          }
          if (template.media_settings) {
            Object.assign(metadata, JSON.parse(template.media_settings));
          }
          break;

        case 'poll':
          if (template.poll_options) {
            metadata.pollOptions = JSON.parse(template.poll_options);
            metadata.name = template.name;
          }
          break;

        case 'contact':
          if (template.contact_info) {
            metadata.contactInfo = JSON.parse(template.contact_info);
          }
          break;

        case 'location':
          if (template.location_info) {
            metadata.locationInfo = JSON.parse(template.location_info);
          }
          break;

        case 'buttons':
        case 'interactive':
          if (template.buttons) {
            const buttons = JSON.parse(template.buttons);
            metadata.buttons = buttons;

            // Format content for Itsukichann/Baileys buttons format
            content = {
              text: content,
              footer: template.footer || '',
              buttons: buttons.map((btn, index) => ({
                buttonId: btn.id || `btn_${index}`,
                buttonText: { displayText: btn.text },
                type: 1
              }))
            };
          }
          if (template.interactive_settings) {
            Object.assign(metadata, JSON.parse(template.interactive_settings));
          }
          break;

        case 'list':
          if (template.list_sections) {
            metadata.sections = JSON.parse(template.list_sections);
          }
          break;

        case 'carousel':
          if (template.carousel_cards) {
            metadata.cards = JSON.parse(template.carousel_cards);
          }
          break;

        case 'cta_button':
          if (template.cta_data) {
            metadata.ctaData = JSON.parse(template.cta_data);
          }
          break;
      }

      return {
        success: true,
        content,
        type: templateType,
        metadata
      };
    } catch (error) {
      this.logger.error('Error processing template:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Format message content for different types
   */
  formatMessageContent(content, type = 'text', options = {}) {
    try {
      switch (type) {
        case 'text':
          return { text: content };

        case 'image':
          return {
            image: { url: content },
            caption: options.caption || content,
            ...(options.viewOnce && { viewOnce: true })
          };

        case 'document':
          return {
            document: { url: content },
            fileName: options.fileName || 'document.pdf',
            caption: options.caption || '',
            ...(options.viewOnce && { viewOnce: true })
          };

        case 'video':
          return {
            video: { url: content },
            caption: options.caption || content,
            ...(options.viewOnce && { viewOnce: true })
          };

        case 'audio':
          return {
            audio: { url: content },
            mimetype: options.mimetype || 'audio/mp4'
          };

        case 'location':
          if (options.locationInfo) {
            return {
              location: {
                degreesLatitude: parseFloat(options.locationInfo.latitude),
                degreesLongitude: parseFloat(options.locationInfo.longitude)
              }
            };
          } else {
            const coords = content.split(',');
            return {
              location: {
                degreesLatitude: parseFloat(coords[0]),
                degreesLongitude: parseFloat(coords[1])
              }
            };
          }

        case 'contact':
          if (options.contactInfo) {
            // Use existing vcard if available, otherwise generate one
            let vcard = options.contactInfo.vcard;

            if (!vcard) {
              // Generate vCard from contact info
              const contactName = options.contactInfo.name || options.contactInfo.displayName || 'Contact';
              const contactPhone = options.contactInfo.phone || '';
              const contactEmail = options.contactInfo.email || '';
              const contactOrg = options.contactInfo.organization || '';

              vcard = `BEGIN:VCARD
VERSION:3.0
FN:${contactName}
N:${contactName.split(' ').reverse().join(';')}
${contactPhone ? `TEL;TYPE=CELL:${contactPhone}` : ''}
${contactEmail ? `EMAIL:${contactEmail}` : ''}
${contactOrg ? `ORG:${contactOrg}` : ''}
END:VCARD`.replace(/\n\n/g, '\n').trim();
            }

            return {
              contacts: {
                displayName: options.contactInfo.name || options.contactInfo.displayName || 'Contact',
                contacts: [{ vcard: vcard }]
              }
            };
          } else {
            return {
              contacts: {
                displayName: options.displayName || 'Contact',
                contacts: [{ vcard: content }]
              }
            };
          }

        case 'poll':
          if (options.pollOptions) {
            return {
              poll: {
                name: options.name || content,
                values: options.pollOptions.map(option =>
                  typeof option === 'string' ? option : option.text
                ),
                selectableCount: options.selectableCount || 1
              }
            };
          } else {
            const pollData = JSON.parse(content);
            return {
              poll: {
                name: pollData.name,
                values: pollData.options,
                selectableCount: pollData.selectableCount || 1
              }
            };
          }

        case 'buttons':
        case 'interactive':
          if (options.buttons) {
            // Use Itsukichann/Baileys regular buttons format
            return {
              text: content,
              footer: options.footer || 'Choose an option:',
              buttons: options.buttons.map((btn, index) => ({
                buttonId: btn.id || `btn_${index}`,
                buttonText: {
                  displayText: btn.text || btn
                }
              }))
            };
          } else {
            const buttonData = JSON.parse(content);
            return {
              text: buttonData.text,
              footer: buttonData.footer || 'Choose an option:',
              buttons: buttonData.buttons.map((btn, index) => ({
                buttonId: `btn_${index}`,
                buttonText: {
                  displayText: btn
                }
              }))
            };
          }

        case 'list':
          if (options.sections) {
            // Use Itsukichann/Baileys list format
            return {
              text: content,
              footer: options.footer || 'Select an option:',
              title: options.title || '',
              buttonText: options.buttonText || 'Select Option',
              sections: options.sections
            };
          } else {
            const listData = JSON.parse(content);
            return {
              text: listData.text,
              footer: listData.footer || 'Select an option:',
              title: listData.title || '',
              buttonText: listData.buttonText || 'Select Option',
              sections: listData.sections
            };
          }

        case 'carousel':
          if (options.cards) {
            // Use Itsukichann/Baileys carousel format
            return {
              text: content,
              interactiveButtons: [{
                name: 'carousel',
                buttonParamsJson: JSON.stringify({
                  cards: options.cards.map(card => ({
                    header: {
                      title: card.header?.title || '',
                      subtitle: card.header?.subtitle || '',
                      hasMediaAttachment: false
                    },
                    body: {
                      text: card.body?.text || ''
                    },
                    footer: {
                      text: card.footer?.text || ''
                    },
                    nativeFlowMessage: {
                      buttons: (card.nativeFlowMessage?.buttons || []).map(button => ({
                        name: button.name || 'cta_url',
                        buttonParamsJson: button.buttonParamsJson
                      }))
                    }
                  }))
                })
              }]
            };
          } else {
            return { text: content };
          }

        case 'cta_button':
          if (options.ctaData) {
            return {
              body: { text: content },
              footer: options.ctaData.footer && options.ctaData.footer.text ? { text: options.ctaData.footer.text } : undefined,
              button: {
                text: options.ctaData.button.text,
                url: options.ctaData.button.url
              }
            };
          } else {
            const ctaData = JSON.parse(content);
            return {
              body: { text: ctaData.body?.text || ctaData.text || content },
              footer: ctaData.footer && ctaData.footer.text ? { text: ctaData.footer.text } : undefined,
              button: {
                text: ctaData.button.text,
                url: ctaData.button.url
              }
            };
          }

        default:
          return { text: content };
      }
    } catch (error) {
      this.logger.error('Error formatting message content:', error);
      return { text: content }; // Fallback to text
    }
  }

  /**
   * Extract phone number from WhatsApp JID
   */
  extractPhoneNumber(jid) {
    return jid.split('@')[0];
  }

  /**
   * Format phone number for WhatsApp
   */
  formatWhatsAppNumber(phoneNumber) {
    if (phoneNumber.includes('@')) {
      return phoneNumber;
    }
    return `${phoneNumber}@s.whatsapp.net`;
  }

  /**
   * Check if message matches keyword criteria
   */
  /**
   * Check if message matches keyword criteria
   * Note: Case sensitivity should be handled by the caller
   */
  matchesKeyword(messageText, keyword, matchType = 'contains') {
    const text = messageText.trim();
    const key = keyword.trim();

    switch (matchType) {
      case 'exact':
        return text === key;
      case 'starts_with':
        return text.startsWith(key);
      case 'ends_with':
        return text.endsWith(key);
      case 'contains':
      default:
        return text.includes(key);
    }
  }

  /**
   * Parse message content and extract relevant information
   */
  parseIncomingMessage(message) {
    try {
      const parsed = {
        id: message.key?.id,
        from: message.key?.remoteJid,
        fromMe: message.key?.fromMe || false,
        timestamp: message.messageTimestamp,
        type: 'text',
        text: '',
        media: null,
        quoted: message.message?.extendedTextMessage?.contextInfo?.quotedMessage || null
      };

      // Extract text content
      if (message.message?.conversation) {
        parsed.text = message.message.conversation;
      } else if (message.message?.extendedTextMessage?.text) {
        parsed.text = message.message.extendedTextMessage.text;
      } else if (message.message?.interactiveResponseMessage?.body?.text) {
        // Handle interactive button responses
        parsed.text = message.message.interactiveResponseMessage.body.text;
        parsed.type = 'interactive_response';
        this.logger.info(`📱 Parsed interactive button response: "${parsed.text}"`);
      } else if (message.message?.buttonsResponseMessage?.selectedButtonId) {
        // Handle legacy button responses (fallback)
        const buttonId = message.message.buttonsResponseMessage.selectedButtonId;
        const displayText = message.message.buttonsResponseMessage.selectedDisplayText;
        parsed.text = displayText || buttonId;
        parsed.type = 'button_response';
        this.logger.info(`📱 Parsed legacy button response: "${parsed.text}"`);
      } else if (message.message?.listResponseMessage?.singleSelectReply?.selectedRowId) {
        // Handle list responses
        const rowId = message.message.listResponseMessage.singleSelectReply.selectedRowId;
        const title = message.message.listResponseMessage.title;
        parsed.text = title || rowId;
        parsed.type = 'list_response';
        this.logger.info(`📱 Parsed list response: "${parsed.text}"`);
      } else if (message.message?.imageMessage?.caption) {
        parsed.type = 'image';
        parsed.text = message.message.imageMessage.caption;
        parsed.media = message.message.imageMessage;
      } else if (message.message?.videoMessage?.caption) {
        parsed.type = 'video';
        parsed.text = message.message.videoMessage.caption;
        parsed.media = message.message.videoMessage;
      } else if (message.message?.documentMessage?.caption) {
        parsed.type = 'document';
        parsed.text = message.message.documentMessage.caption;
        parsed.media = message.message.documentMessage;
      } else if (message.message?.audioMessage) {
        parsed.type = 'audio';
        parsed.media = message.message.audioMessage;
      } else if (message.message?.locationMessage) {
        parsed.type = 'location';
        parsed.media = message.message.locationMessage;
      } else if (message.message?.contactMessage) {
        parsed.type = 'contact';
        parsed.media = message.message.contactMessage;
      }

      return parsed;
    } catch (error) {
      this.logger.error('Error parsing incoming message:', error);
      return {
        id: null,
        from: null,
        fromMe: false,
        timestamp: Date.now(),
        type: 'text',
        text: '',
        media: null,
        quoted: null
      };
    }
  }

  /**
   * Validate message content for sending
   */
  validateMessageContent(content, type = 'text') {
    try {
      switch (type) {
        case 'text':
          return content && content.trim().length > 0;
        case 'image':
        case 'video':
        case 'audio':
        case 'document':
          return content && (content.startsWith('http') || content.startsWith('/'));
        case 'location':
          const coords = content.split(',');
          return coords.length === 2 && !isNaN(coords[0]) && !isNaN(coords[1]);
        case 'poll':
        case 'buttons':
        case 'list':
          try {
            JSON.parse(content);
            return true;
          } catch {
            return false;
          }
        default:
          return true;
      }
    } catch (error) {
      return false;
    }
  }
}

module.exports = MessageProcessorService;
