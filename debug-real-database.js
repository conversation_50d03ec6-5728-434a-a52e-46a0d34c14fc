const initSqlJs = require('sql.js');
const fs = require('fs');
const path = require('path');

async function debugRealDatabase() {
  console.log('🔍 Debugging Real Database...');
  
  const dbPath = path.join(__dirname, 'data', 'leadwave.db');
  console.log(`📂 Database path: ${dbPath}`);
  
  if (!fs.existsSync(dbPath)) {
    console.log('❌ Database file not found!');
    return;
  }
  
  try {
    // Initialize SQL.js
    const SQL = await initSqlJs();
    
    // Load the database
    const filebuffer = fs.readFileSync(dbPath);
    const db = new SQL.Database(filebuffer);
    
    console.log('\n📱 WhatsApp Sessions:');
    const sessions = db.exec('SELECT * FROM whatsapp_sessions');
    if (sessions.length > 0 && sessions[0].values.length > 0) {
      const columns = sessions[0].columns;
      sessions[0].values.forEach((row, index) => {
        console.log(`\n${index + 1}. Session:`);
        row.forEach((value, colIndex) => {
          console.log(`   ${columns[colIndex]}: ${value}`);
        });
      });
    } else {
      console.log('  ❌ No sessions found');
    }
    
    console.log('\n🤖 Chatbot Flows:');
    const flows = db.exec('SELECT * FROM chatbot_flows');
    if (flows.length > 0 && flows[0].values.length > 0) {
      const columns = flows[0].columns;
      flows[0].values.forEach((row, index) => {
        console.log(`\n${index + 1}. Flow:`);
        row.forEach((value, colIndex) => {
          console.log(`   ${columns[colIndex]}: ${value}`);
        });
      });
    } else {
      console.log('  ❌ No chatbot flows found');
    }
    
    console.log('\n📨 Recent Messages (last 10):');
    const messages = db.exec(`
      SELECT * FROM messages 
      WHERE datetime(created_at) > datetime('now', '-1 hour')
      ORDER BY created_at DESC 
      LIMIT 10
    `);
    if (messages.length > 0 && messages[0].values.length > 0) {
      const columns = messages[0].columns;
      console.log('  Recent messages:');
      messages[0].values.forEach((row, index) => {
        console.log(`\n  ${index + 1}. Message:`);
        row.forEach((value, colIndex) => {
          if (columns[colIndex] === 'content' || columns[colIndex] === 'sender_phone' || 
              columns[colIndex] === 'created_at' || columns[colIndex] === 'direction' ||
              columns[colIndex] === 'message_type') {
            console.log(`     ${columns[colIndex]}: ${value}`);
          }
        });
      });
    } else {
      console.log('  ❌ No recent messages found');
    }
    
    console.log('\n🗣️ Recent Chatbot Conversations:');
    const conversations = db.exec(`
      SELECT * FROM chatbot_conversations 
      WHERE datetime(created_at) > datetime('now', '-1 hour')
      ORDER BY created_at DESC 
      LIMIT 5
    `);
    if (conversations.length > 0 && conversations[0].values.length > 0) {
      const columns = conversations[0].columns;
      conversations[0].values.forEach((row, index) => {
        console.log(`\n  ${index + 1}. Conversation:`);
        row.forEach((value, colIndex) => {
          console.log(`     ${columns[colIndex]}: ${value}`);
        });
      });
    } else {
      console.log('  ❌ No recent chatbot conversations');
    }
    
    // Check for specific keywords in flows
    console.log('\n🔍 Checking for php/development keywords:');
    const keywordFlows = db.exec(`
      SELECT * FROM chatbot_flows 
      WHERE is_active = 1 
      AND (
        LOWER(trigger_keywords) LIKE '%php%' 
        OR LOWER(trigger_keywords) LIKE '%development%'
      )
    `);
    if (keywordFlows.length > 0 && keywordFlows[0].values.length > 0) {
      console.log('  ✅ Found flows with php/development keywords:');
      const columns = keywordFlows[0].columns;
      keywordFlows[0].values.forEach((row, index) => {
        const flowData = {};
        row.forEach((value, colIndex) => {
          flowData[columns[colIndex]] = value;
        });
        console.log(`    - "${flowData.name}": "${flowData.trigger_keywords}"`);
      });
    } else {
      console.log('  ❌ No flows found with php/development keywords');
    }
    
    console.log('\n💡 ANALYSIS:');
    
    // Check if sessions exist and are connected
    const connectedSessions = db.exec(`
      SELECT COUNT(*) as count FROM whatsapp_sessions 
      WHERE status = 'connected' AND is_active = 1
    `);
    const connectedCount = connectedSessions[0]?.values[0]?.[0] || 0;
    
    // Check if flows exist and are active
    const activeFlows = db.exec(`
      SELECT COUNT(*) as count FROM chatbot_flows 
      WHERE is_active = 1
    `);
    const activeFlowCount = activeFlows[0]?.values[0]?.[0] || 0;
    
    // Check if keyword flows exist
    const keywordFlowCount = keywordFlows.length > 0 ? keywordFlows[0].values.length : 0;
    
    console.log(`📊 Connected Sessions: ${connectedCount}`);
    console.log(`📊 Active Flows: ${activeFlowCount}`);
    console.log(`📊 PHP/Development Flows: ${keywordFlowCount}`);
    
    if (connectedCount === 0) {
      console.log('❌ ISSUE: No connected WhatsApp sessions');
    } else if (activeFlowCount === 0) {
      console.log('❌ ISSUE: No active chatbot flows');
    } else if (keywordFlowCount === 0) {
      console.log('❌ ISSUE: No flows with php/development keywords');
    } else {
      console.log('✅ Configuration looks good - issue might be in message processing');
    }
    
    db.close();
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

debugRealDatabase().catch(console.error);
