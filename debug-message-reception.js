const DatabaseService = require('./src/services/database.service');

async function debugMessageReception() {
  console.log('🔍 Debugging Message Reception...');
  
  const db = new DatabaseService();
  await db.initialize();
  
  // Check connected sessions
  console.log('\n📱 Checking Connected Sessions:');
  const sessions = await db.all('SELECT * FROM whatsapp_sessions WHERE status = "connected" AND is_active = 1');
  if (sessions.success && sessions.data && sessions.data.length > 0) {
    sessions.data.forEach(session => {
      console.log(`  ✅ Session: ${session.name} (${session.session_id})`);
      console.log(`     Phone: ${session.phone_number}`);
      console.log(`     Status: ${session.status}`);
    });
  } else {
    console.log('  ❌ No connected sessions found');
    return;
  }
  
  // Check recent messages in database
  console.log('\n📨 Recent Messages in Database:');
  const recentMessages = await db.all(`
    SELECT * FROM messages 
    WHERE datetime(created_at) > datetime('now', '-10 minutes')
    ORDER BY created_at DESC 
    LIMIT 10
  `);
  
  if (recentMessages.success && recentMessages.data && recentMessages.data.length > 0) {
    console.log('  Recent messages found:');
    recentMessages.data.forEach(msg => {
      console.log(`    - ${msg.created_at}: ${msg.sender_phone} -> "${msg.content}"`);
      console.log(`      Type: ${msg.message_type}, Session: ${msg.session_id}`);
    });
  } else {
    console.log('  ❌ No recent messages found in database');
  }
  
  // Check chatbot flows
  console.log('\n🤖 Active Chatbot Flows:');
  const flows = await db.all('SELECT * FROM chatbot_flows WHERE is_active = 1');
  if (flows.success && flows.data && flows.data.length > 0) {
    flows.data.forEach(flow => {
      console.log(`  ✅ Flow: "${flow.name}"`);
      console.log(`     Keywords: "${flow.trigger_keywords}"`);
      console.log(`     Session: ${flow.whatsapp_session_id || 'Any'}`);
    });
  } else {
    console.log('  ❌ No active chatbot flows');
  }
  
  console.log('\n🔄 Monitoring for New Messages...');
  console.log('📱 Now send a list selection and watch for activity...');
  console.log('🛑 Press Ctrl+C to stop monitoring');
  
  let lastMessageId = 0;
  
  // Get the highest message ID to start monitoring from
  const lastMsg = await db.get('SELECT MAX(id) as max_id FROM messages');
  if (lastMsg.success && lastMsg.data && lastMsg.data.max_id) {
    lastMessageId = lastMsg.data.max_id;
    console.log(`📊 Starting monitor from message ID: ${lastMessageId}`);
  }
  
  const monitor = setInterval(async () => {
    // Check for new messages
    const newMessages = await db.all(`
      SELECT * FROM messages 
      WHERE id > ? 
      ORDER BY id ASC
    `, [lastMessageId]);
    
    if (newMessages.success && newMessages.data && newMessages.data.length > 0) {
      console.log('\n🆕 NEW MESSAGE DETECTED:');
      newMessages.data.forEach(msg => {
        console.log(`  📨 ${msg.created_at}: ${msg.sender_phone}`);
        console.log(`     Content: "${msg.content}"`);
        console.log(`     Type: ${msg.message_type}`);
        console.log(`     Session: ${msg.session_id}`);
        console.log(`     Direction: ${msg.direction}`);
        lastMessageId = Math.max(lastMessageId, msg.id);
        
        // Check if this should trigger chatbot
        if (msg.direction === 'incoming' && msg.content) {
          const content = msg.content.toLowerCase();
          if (content.includes('php') || content.includes('development')) {
            console.log(`     🎯 SHOULD TRIGGER CHATBOT: Contains php/development keywords!`);
          } else {
            console.log(`     ⚠️ Content doesn't contain php/development keywords`);
          }
        }
      });
    }
    
    // Check for new chatbot conversations
    const newConversations = await db.all(`
      SELECT * FROM chatbot_conversations 
      WHERE datetime(created_at) > datetime('now', '-1 minute')
      ORDER BY created_at DESC
    `);
    
    if (newConversations.success && newConversations.data && newConversations.data.length > 0) {
      console.log('\n🤖 NEW CHATBOT CONVERSATION:');
      newConversations.data.forEach(conv => {
        console.log(`  🗣️ ${conv.created_at}: User ${conv.user_phone}`);
        console.log(`     Flow ID: ${conv.flow_id}`);
        console.log(`     Active: ${conv.is_active}`);
      });
    }
  }, 2000);
  
  // Handle Ctrl+C
  process.on('SIGINT', () => {
    console.log('\n\n🛑 Stopping monitor...');
    clearInterval(monitor);
    console.log('\n💡 DIAGNOSIS:');
    console.log('If no new messages appeared when you selected from the list:');
    console.log('  1. Messages are not being saved to database');
    console.log('  2. WhatsApp message events are not being processed');
    console.log('  3. There might be an issue with the message handling pipeline');
    process.exit(0);
  });
}

debugMessageReception().catch(console.error);
