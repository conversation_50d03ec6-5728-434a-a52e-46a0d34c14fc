const initSqlJs = require('sql.js');
const fs = require('fs');
const path = require('path');
const os = require('os');

async function checkCorrectDatabase() {
  console.log('🔍 Finding the CORRECT database with your data...');
  
  const appDataPath = process.env.APPDATA;
  const possibleDbs = [
    path.join(appDataPath, 'Electron', 'leadwave.db'),
    path.join(appDataPath, 'leadwave-desktop', 'leadwave.db'),
    path.join(appDataPath, 'leadwave-mamz-desktop', 'leadwave.db'),
    path.join(appDataPath, 'wazbit-desktop', 'leadwave.db'),
    path.join(appDataPath, 'wazbit-whatsapp-desktop', 'leadwave.db')
  ];
  
  const SQL = await initSqlJs();
  
  for (const dbPath of possibleDbs) {
    if (!fs.existsSync(dbPath)) {
      console.log(`❌ Not found: ${dbPath}`);
      continue;
    }
    
    try {
      console.log(`\n🔍 Checking: ${dbPath}`);
      const stats = fs.statSync(dbPath);
      console.log(`   Size: ${(stats.size / 1024).toFixed(2)} KB, Modified: ${stats.mtime.toISOString()}`);
      
      const filebuffer = fs.readFileSync(dbPath);
      const db = new SQL.Database(filebuffer);
      
      // Check for sessions
      const sessions = db.exec('SELECT * FROM whatsapp_sessions WHERE status = "connected"');
      const sessionCount = sessions.length > 0 ? sessions[0].values.length : 0;
      
      // Check for chatbot flows
      const flows = db.exec('SELECT * FROM chatbot_flows WHERE is_active = 1');
      const flowCount = flows.length > 0 ? flows[0].values.length : 0;
      
      console.log(`   📱 Connected sessions: ${sessionCount}`);
      console.log(`   🤖 Active flows: ${flowCount}`);
      
      if (sessionCount > 0 && flowCount > 0) {
        console.log(`\n🎯 FOUND THE CORRECT DATABASE: ${dbPath}`);
        
        // Show session details
        if (sessions.length > 0) {
          const columns = sessions[0].columns;
          sessions[0].values.forEach((row, index) => {
            console.log(`\n   Session ${index + 1}:`);
            row.forEach((value, colIndex) => {
              if (['name', 'phone_number', 'status'].includes(columns[colIndex])) {
                console.log(`     ${columns[colIndex]}: ${value}`);
              }
            });
          });
        }
        
        // Show flow details
        if (flows.length > 0) {
          const columns = flows[0].columns;
          flows[0].values.forEach((row, index) => {
            console.log(`\n   Flow ${index + 1}:`);
            row.forEach((value, colIndex) => {
              if (['name', 'trigger_keywords', 'is_active'].includes(columns[colIndex])) {
                console.log(`     ${columns[colIndex]}: ${value}`);
              }
            });
          });
        }
        
        // Now monitor this database for message activity
        console.log(`\n🔄 Monitoring ${dbPath} for message activity...`);
        console.log('📱 Now select from the list in WhatsApp and watch for activity...');
        console.log('🛑 Press Ctrl+C to stop monitoring');
        
        let lastMessageCount = 0;
        
        // Get initial message count (check both possible table names)
        let messageTable = 'messages';
        try {
          const initialMessages = db.exec('SELECT COUNT(*) as count FROM messages');
          lastMessageCount = initialMessages[0]?.values[0]?.[0] || 0;
        } catch (e) {
          try {
            const initialMessages = db.exec('SELECT COUNT(*) as count FROM message_history');
            lastMessageCount = initialMessages[0]?.values[0]?.[0] || 0;
            messageTable = 'message_history';
          } catch (e2) {
            console.log('   ⚠️ Could not find messages table');
            lastMessageCount = 0;
          }
        }
        
        console.log(`📊 Starting message count: ${lastMessageCount} (table: ${messageTable})`);
        
        const monitor = setInterval(() => {
          try {
            // Reload database to get fresh data
            const freshBuffer = fs.readFileSync(dbPath);
            const freshDb = new SQL.Database(freshBuffer);
            
            const currentMessages = freshDb.exec(`SELECT COUNT(*) as count FROM ${messageTable}`);
            const currentCount = currentMessages[0]?.values[0]?.[0] || 0;
            
            if (currentCount > lastMessageCount) {
              console.log(`\n🆕 NEW MESSAGE DETECTED! Count: ${lastMessageCount} → ${currentCount}`);
              
              // Get the new messages
              const newMessages = freshDb.exec(`
                SELECT * FROM ${messageTable} 
                ORDER BY id DESC 
                LIMIT ${currentCount - lastMessageCount}
              `);
              
              if (newMessages.length > 0) {
                const columns = newMessages[0].columns;
                newMessages[0].values.forEach(row => {
                  const msgData = {};
                  row.forEach((value, colIndex) => {
                    msgData[columns[colIndex]] = value;
                  });
                  console.log(`  📨 ${msgData.created_at || msgData.timestamp}: ${msgData.sender_phone || msgData.from_phone} → "${msgData.content || msgData.message}"`);
                  console.log(`     Type: ${msgData.message_type || msgData.type}, Direction: ${msgData.direction || 'unknown'}`);
                });
              }
              
              lastMessageCount = currentCount;
            }
            
            freshDb.close();
          } catch (error) {
            console.error('❌ Monitor error:', error.message);
          }
        }, 2000);
        
        // Handle Ctrl+C
        process.on('SIGINT', () => {
          console.log('\n\n🛑 Stopping monitor...');
          clearInterval(monitor);
          db.close();
          process.exit(0);
        });
        
        return; // Found the correct database, stop checking others
      }
      
      db.close();
      
    } catch (error) {
      console.log(`   ❌ Error: ${error.message}`);
    }
  }
  
  console.log('\n❌ Could not find the correct database with your session and flow data');
}

checkCorrectDatabase().catch(console.error);
