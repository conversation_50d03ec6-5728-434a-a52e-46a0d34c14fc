{"ast": null, "code": "var _jsxFileName = \"D:\\\\My Projects\\\\LeadWave\\\\src\\\\components\\\\modules\\\\Chatbot.js\";\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { ChatBubbleOvalLeftEllipsisIcon, PlusIcon, MagnifyingGlassIcon, PencilIcon, TrashIcon, PlayIcon, PauseIcon, EyeIcon, DocumentTextIcon, ArrowPathIcon, TagIcon, ClockIcon, CheckCircleIcon, XCircleIcon, ExclamationTriangleIcon, XMarkIcon, ArrowRightIcon, BoltIcon, PhotoIcon, PaperClipIcon, UserGroupIcon, ChartBarIcon, RectangleStackIcon, ListBulletIcon, ViewColumnsIcon, MapPinIcon, VideoCameraIcon, SpeakerWaveIcon, DocumentIcon, ChatBubbleLeftRightIcon, ArrowDownTrayIcon, ArrowUpTrayIcon } from '@heroicons/react/24/outline';\nimport { useNotifications } from '../../contexts/NotificationContext';\n\n// Template types with their configurations (matching Templates.js)\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TEMPLATE_TYPES = {\n  text: {\n    name: 'Text Message',\n    description: 'Simple text message with variables',\n    color: 'blue'\n  },\n  image: {\n    name: 'Message + Image',\n    description: 'Text message with image attachment',\n    color: 'green'\n  },\n  document: {\n    name: 'Message + Document',\n    description: 'Text message with document attachment',\n    color: 'purple'\n  },\n  contact: {\n    name: 'Message + Contact',\n    description: 'Text message with contact card',\n    color: 'indigo'\n  },\n  poll: {\n    name: 'Message + Poll',\n    description: 'Interactive poll with multiple options',\n    color: 'yellow'\n  },\n  buttons: {\n    name: 'Interactive Buttons',\n    description: 'Message with interactive buttons',\n    color: 'blue'\n  },\n  // Hidden template type - functionality preserved but not shown in UI\n  // cta_button: {\n  //   name: 'CTA Button',\n  //   description: 'Call-to-action button with URL link',\n  //   color: 'orange'\n  // },\n  list: {\n    name: 'Interactive List',\n    description: 'Message with selectable list options',\n    color: 'green'\n  },\n  mixed_buttons: {\n    name: 'Mixed Interactive Buttons',\n    description: 'Interactive buttons with multiple types (Quick Reply, CTA URL, CTA Phone, Copy Code)',\n    color: 'indigo'\n  },\n  location: {\n    name: 'Message + Location',\n    description: 'Text message with location pin',\n    color: 'red'\n  },\n  video: {\n    name: 'Message + Video',\n    description: 'Text message with video attachment',\n    color: 'pink'\n  },\n  audio: {\n    name: 'Message + Audio',\n    description: 'Text message with audio attachment',\n    color: 'orange'\n  }\n};\nconst Chatbot = () => {\n  const {\n    showSuccess,\n    showError,\n    showWarning,\n    confirm\n  } = useNotifications();\n  // Add error boundary to catch rendering errors\n  const [renderError, setRenderError] = useState(null);\n\n  // Debug function to safely render any value\n  const safeRender = (value, context = 'unknown') => {\n    if (value === null || value === undefined) {\n      return '';\n    }\n    if (typeof value === 'string' || typeof value === 'number' || typeof value === 'boolean') {\n      return value;\n    }\n    if (typeof value === 'object') {\n      console.error(`🚨 OBJECT RENDER ATTEMPT in ${context}:`, value);\n      console.trace('Stack trace for object render attempt');\n      if (value.name) return String(value.name);\n      if (value.display_text) return String(value.display_text);\n      if (value.title) return String(value.title);\n      if (value.text) return String(value.text);\n      return JSON.stringify(value);\n    }\n    return String(value);\n  };\n  const [flows, setFlows] = useState([]);\n  const [sessions, setSessions] = useState([]);\n  const [templates, setTemplates] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [statusFilter, setStatusFilter] = useState('all');\n  const [showCreateModal, setShowCreateModal] = useState(false);\n  const [showEditModal, setShowEditModal] = useState(false);\n  const [showFlowModal, setShowFlowModal] = useState(false);\n  const [selectedFlow, setSelectedFlow] = useState(null);\n  const [flowForm, setFlowForm] = useState({\n    name: '',\n    description: '',\n    sessionId: '',\n    triggerKeywords: '',\n    keywordMatchType: 'contains',\n    keywordCaseSensitive: false,\n    isActive: true,\n    cooldownMinutes: 0,\n    nodes: []\n  });\n  const [nodeForm, setNodeForm] = useState({\n    name: '',\n    message: '',\n    nodeType: 'message',\n    messageType: 'text',\n    // text, template\n    options: [],\n    nextNodeId: null,\n    templateId: '',\n    variables: {},\n    attachmentFile: null,\n    attachmentType: 'image',\n    // image, video, audio, document\n    extractVariable: '',\n    // Variable name to extract from user response\n    conditions: [],\n    // Conditional logic for condition nodes\n    // Action node fields\n    actionType: 'webhook',\n    webhookUrl: '',\n    delaySeconds: 5,\n    emailRecipients: '',\n    emailSubject: '',\n    emailBody: '',\n    // Added email body\n    emailTemplate: '',\n    // Added email template\n    saveDataFields: [],\n    // Added save data fields\n    apiEndpoint: '',\n    // Added API endpoint\n    apiMethod: 'POST',\n    // Added API method\n    apiHeaders: '',\n    // Added API headers\n    apiBody: '',\n    // Added API body\n    // Condition node fields\n    conditionType: 'user_response',\n    responseConditions: [],\n    conditionVariable: '',\n    conditionOperator: 'equals',\n    conditionValue: '',\n    randomPaths: [],\n    truePath: null,\n    // Added true path for conditions\n    falsePath: null // Added false path for conditions\n  });\n  const [showNodeModal, setShowNodeModal] = useState(false);\n  const [editingNodeIndex, setEditingNodeIndex] = useState(-1);\n\n  // Load data on component mount\n  useEffect(() => {\n    loadFlows();\n    loadSessions();\n    loadTemplates();\n  }, []);\n\n  // Ensure options is always an array of strings when nodeType changes\n  useEffect(() => {\n    if (nodeForm.nodeType === 'question') {\n      if (!Array.isArray(nodeForm.options)) {\n        setNodeForm(prev => ({\n          ...prev,\n          options: []\n        }));\n      }\n    }\n  }, [nodeForm.nodeType]);\n  const loadFlows = async () => {\n    try {\n      setLoading(true);\n      const response = await window.electronAPI.database.query(`SELECT cf.*, ws.device_name, COUNT(cn.id) as node_count\n         FROM chatbot_flows cf\n         LEFT JOIN whatsapp_sessions ws ON cf.session_id = ws.session_id\n         LEFT JOIN chatbot_nodes cn ON cf.id = cn.flow_id\n         GROUP BY cf.id\n         ORDER BY cf.created_at DESC`);\n      if (response.success) {\n        setFlows(response.data || []);\n      }\n    } catch (error) {\n      console.error('Error loading chatbot flows:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const loadSessions = async () => {\n    try {\n      const response = await window.electronAPI.whatsapp.getSessions();\n      console.log('Sessions response:', response);\n      if (response.success && Array.isArray(response.sessions)) {\n        // Filter for connected sessions - be more permissive to catch any working sessions\n        const connectedSessions = response.sessions.filter(session => {\n          const isConnected = session.realTimeStatus === 'connected' || session.status === 'connected' || session.isLoggedIn === true;\n          console.log(`Session ${typeof session.sessionId === 'string' ? session.sessionId : typeof session.name === 'string' ? session.name : String(session.sessionId || session.name || 'unknown')}:`, {\n            status: session.status,\n            realTimeStatus: session.realTimeStatus,\n            isLoggedIn: session.isLoggedIn,\n            isConnected\n          });\n          return isConnected;\n        });\n        console.log('All sessions:', response.sessions.length);\n        console.log('Connected sessions:', connectedSessions.length);\n        setSessions(connectedSessions);\n      } else {\n        console.warn('Invalid sessions response:', response);\n        setSessions([]);\n      }\n    } catch (error) {\n      console.error('Error loading sessions:', error);\n      setSessions([]);\n    }\n  };\n  const loadTemplates = async () => {\n    try {\n      const response = await window.electronAPI.database.query(`SELECT id, name, content, type, attachments, buttons, list_sections,\n         poll_options, contact_info, location_info,\n         media_settings, interactive_settings, cta_data, copy_data, flow_data, variables\n         FROM message_templates ORDER BY name ASC`);\n      if (response.success) {\n        setTemplates(response.data || []);\n      }\n    } catch (error) {\n      console.error('Error loading templates:', error);\n    }\n  };\n  const handleCreateFlow = async () => {\n    if (!flowForm.name.trim() || !flowForm.triggerKeywords.trim() || !flowForm.sessionId) {\n      showError('Validation Error', 'Please fill in flow name, trigger keywords, and select a WhatsApp session');\n      return;\n    }\n    if (flowForm.nodes.length === 0) {\n      showError('Validation Error', 'Please add at least one node to the flow');\n      return;\n    }\n    try {\n      console.log('🔧 Creating chatbot flow with data:', {\n        name: flowForm.name.trim(),\n        description: flowForm.description.trim() || null,\n        sessionId: flowForm.sessionId,\n        triggerKeywords: flowForm.triggerKeywords.trim(),\n        isActive: flowForm.isActive ? 1 : 0,\n        cooldownMinutes: flowForm.cooldownMinutes || 0,\n        nodeCount: flowForm.nodes.length\n      });\n\n      // Create the flow\n      const flowResult = await window.electronAPI.database.query(`INSERT INTO chatbot_flows (\n          name, description, session_id, trigger_keywords, is_active, cooldown_minutes, created_at, updated_at\n        ) VALUES (?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)`, [flowForm.name.trim(), flowForm.description.trim() || null, flowForm.sessionId, flowForm.triggerKeywords.trim(), flowForm.isActive ? 1 : 0, flowForm.cooldownMinutes || 0]);\n      console.log('🔧 Flow creation result:', flowResult);\n      if (flowResult.success) {\n        const flowId = flowResult.insertId;\n\n        // Create nodes with proper ID mapping\n        const nodeIdMap = new Map(); // Map from array index to actual database ID\n\n        // First pass: Create all nodes without next_node_id\n        for (let i = 0; i < flowForm.nodes.length; i++) {\n          const node = flowForm.nodes[i];\n          const nodeResult = await window.electronAPI.database.query(`INSERT INTO chatbot_nodes (\n              flow_id, name, message, node_type, options, next_node_id,\n              position, template_id, attachment_data, attachment_type, created_at, updated_at\n            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)`, [flowId, node.name, node.message, node.nodeType, JSON.stringify(node.options || []), null,\n          // Set to null initially\n          i + 1, node.templateId || null, node.attachmentData || null, node.attachmentType || null]);\n          if (nodeResult.success) {\n            nodeIdMap.set(i + 1, nodeResult.insertId); // Map position to actual DB ID\n          }\n        }\n\n        // Second pass: Update next_node_id with correct database IDs\n        for (let i = 0; i < flowForm.nodes.length; i++) {\n          const node = flowForm.nodes[i];\n          if (node.nextNodeId && !isNaN(node.nextNodeId) && node.nextNodeId <= flowForm.nodes.length) {\n            const actualNextNodeId = nodeIdMap.get(parseInt(node.nextNodeId));\n            if (actualNextNodeId) {\n              const currentNodeId = nodeIdMap.get(i + 1);\n              await window.electronAPI.database.query('UPDATE chatbot_nodes SET next_node_id = ? WHERE id = ?', [actualNextNodeId, currentNodeId]);\n            }\n          }\n        }\n        setShowCreateModal(false);\n        resetFlowForm();\n        await loadFlows();\n        showSuccess('Flow Created', 'Chatbot flow created successfully!');\n      } else {\n        showError('Creation Failed', 'Failed to create chatbot flow: ' + flowResult.error);\n      }\n    } catch (error) {\n      console.error('Error creating chatbot flow:', error);\n      showError('Creation Failed', 'Error creating chatbot flow. Please try again.');\n    }\n  };\n  const handleDeleteFlow = async flowId => {\n    const confirmed = await confirm('Are you sure you want to delete this chatbot flow and all its nodes?', 'Delete Chatbot Flow');\n    if (!confirmed) {\n      return;\n    }\n    try {\n      console.log(`🗑️ Deleting chatbot flow with ID: ${flowId}`);\n\n      // 1. End all active conversations for this flow\n      const endConversationsResult = await window.electronAPI.database.query('UPDATE chatbot_conversations SET is_active = 0, completed_at = CURRENT_TIMESTAMP WHERE flow_id = ? AND is_active = 1', [flowId]);\n      console.log(`🗑️ Ended ${endConversationsResult.changes || 0} active conversations for flow ${flowId}`);\n\n      // 2. Clear any cooldown records for this flow\n      await window.electronAPI.database.query('DELETE FROM chatbot_cooldowns WHERE flow_id = ?', [flowId]);\n\n      // 3. Delete nodes first\n      const deleteNodesResult = await window.electronAPI.database.query('DELETE FROM chatbot_nodes WHERE flow_id = ?', [flowId]);\n      console.log(`🗑️ Deleted ${deleteNodesResult.changes || 0} nodes for flow ${flowId}`);\n\n      // 4. Delete flow\n      const result = await window.electronAPI.database.query('DELETE FROM chatbot_flows WHERE id = ?', [flowId]);\n      if (result.success) {\n        console.log(`✅ Successfully deleted chatbot flow ${flowId} and cleaned up related data`);\n        await loadFlows();\n        showSuccess('Flow Deleted', 'Chatbot flow deleted successfully and all active conversations ended');\n      } else {\n        showError('Delete Failed', 'Failed to delete chatbot flow: ' + result.error);\n      }\n    } catch (error) {\n      console.error('Error deleting chatbot flow:', error);\n      showError('Delete Failed', 'Error deleting chatbot flow. Please try again.');\n    }\n  };\n  const toggleFlowStatus = async (flowId, currentStatus) => {\n    try {\n      // If disabling the flow, end all active conversations first\n      if (currentStatus) {\n        console.log(`🔄 Disabling flow ${flowId}, ending active conversations...`);\n        const endConversationsResult = await window.electronAPI.database.query('UPDATE chatbot_conversations SET is_active = 0, completed_at = CURRENT_TIMESTAMP WHERE flow_id = ? AND is_active = 1', [flowId]);\n        console.log(`🔄 Ended ${endConversationsResult.changes || 0} active conversations for flow ${flowId}`);\n      }\n      const result = await window.electronAPI.database.query('UPDATE chatbot_flows SET is_active = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?', [currentStatus ? 0 : 1, flowId]);\n      if (result.success) {\n        await loadFlows();\n        const statusText = currentStatus ? 'disabled' : 'enabled';\n        showSuccess('Status Updated', `Flow ${statusText} successfully${currentStatus ? ' and active conversations ended' : ''}`);\n      } else {\n        showError('Update Failed', 'Failed to update flow status: ' + result.error);\n      }\n    } catch (error) {\n      console.error('Error updating flow status:', error);\n      showError('Update Failed', 'Error updating flow status. Please try again.');\n    }\n  };\n  const handleEditFlow = async flow => {\n    try {\n      // Load the flow nodes\n      const nodesResult = await window.electronAPI.database.query('SELECT * FROM chatbot_nodes WHERE flow_id = ? ORDER BY position', [flow.id]);\n      if (nodesResult.success) {\n        // Create a map of database ID to position for proper conversion\n        const idToPositionMap = new Map();\n        nodesResult.data.forEach(node => {\n          idToPositionMap.set(node.id, node.position);\n        });\n        const nodes = nodesResult.data.map((node, index) => {\n          // Convert next_node_id from database ID to position-based reference\n          let nextNodeId = null;\n          if (node.next_node_id) {\n            // Find the position of the referenced node by its database ID\n            const referencedPosition = idToPositionMap.get(node.next_node_id);\n            if (referencedPosition) {\n              nextNodeId = referencedPosition;\n            }\n          }\n          return {\n            id: node.id,\n            name: node.name,\n            message: node.message,\n            nodeType: node.node_type,\n            options: node.options ? JSON.parse(node.options) : [],\n            nextNodeId: nextNodeId,\n            templateId: node.template_id,\n            attachmentData: node.attachment_data,\n            attachmentType: node.attachment_type,\n            extractVariable: node.options && typeof JSON.parse(node.options) === 'object' && !Array.isArray(JSON.parse(node.options)) ? JSON.parse(node.options).extract_variable || '' : '',\n            conditions: node.node_type === 'condition' && node.options ? JSON.parse(node.options) : []\n          };\n        });\n\n        // Populate the form with existing flow data\n        setFlowForm({\n          id: flow.id,\n          // Add ID for editing\n          name: flow.name,\n          description: flow.description || '',\n          sessionId: flow.session_id,\n          triggerKeywords: flow.trigger_keywords,\n          isActive: flow.is_active,\n          cooldownMinutes: flow.cooldown_minutes || 0,\n          nodes: nodes\n        });\n        setShowEditModal(true);\n      } else {\n        showError('Load Failed', 'Failed to load flow nodes: ' + nodesResult.error);\n      }\n    } catch (error) {\n      console.error('Error loading flow for editing:', error);\n      showError('Load Failed', 'Error loading flow for editing. Please try again.');\n    }\n  };\n  const handleUpdateFlow = async () => {\n    if (!flowForm.name.trim() || !flowForm.triggerKeywords.trim() || !flowForm.sessionId) {\n      showError('Validation Error', 'Please fill in flow name, trigger keywords, and select a WhatsApp session');\n      return;\n    }\n    if (flowForm.nodes.length === 0) {\n      showError('Validation Error', 'Please add at least one node to the flow');\n      return;\n    }\n    try {\n      // Update the flow\n      const flowResult = await window.electronAPI.database.query(`UPDATE chatbot_flows SET\n          name = ?, description = ?, session_id = ?, trigger_keywords = ?,\n          is_active = ?, cooldown_minutes = ?, updated_at = CURRENT_TIMESTAMP\n        WHERE id = ?`, [flowForm.name.trim(), flowForm.description.trim() || null, flowForm.sessionId, flowForm.triggerKeywords.trim(), flowForm.isActive ? 1 : 0, flowForm.cooldownMinutes || 0, flowForm.id]);\n      if (flowResult.success) {\n        // End any active conversations for this flow before updating nodes\n        await window.electronAPI.database.query('UPDATE chatbot_conversations SET is_active = 0, completed_at = CURRENT_TIMESTAMP WHERE flow_id = ? AND is_active = 1', [flowForm.id]);\n\n        // Delete existing nodes\n        await window.electronAPI.database.query('DELETE FROM chatbot_nodes WHERE flow_id = ?', [flowForm.id]);\n\n        // Create updated nodes with proper ID mapping\n        const nodeIdMap = new Map(); // Map from array index to actual database ID\n\n        // First pass: Create all nodes without next_node_id\n        for (let i = 0; i < flowForm.nodes.length; i++) {\n          const node = flowForm.nodes[i];\n          const nodeResult = await window.electronAPI.database.query(`INSERT INTO chatbot_nodes (\n              flow_id, name, message, node_type, options, next_node_id,\n              position, template_id, attachment_data, attachment_type, created_at, updated_at\n            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)`, [flowForm.id, node.name, node.message, node.nodeType, JSON.stringify(node.options || []), null,\n          // Set to null initially\n          i + 1, node.templateId || null, node.attachmentData || null, node.attachmentType || null]);\n          if (nodeResult.success) {\n            nodeIdMap.set(i + 1, nodeResult.insertId); // Map position to actual DB ID\n          }\n        }\n\n        // Second pass: Update next_node_id with correct database IDs\n        for (let i = 0; i < flowForm.nodes.length; i++) {\n          const node = flowForm.nodes[i];\n          if (node.nextNodeId && !isNaN(node.nextNodeId) && node.nextNodeId <= flowForm.nodes.length) {\n            const actualNextNodeId = nodeIdMap.get(parseInt(node.nextNodeId));\n            if (actualNextNodeId) {\n              const currentNodeId = nodeIdMap.get(i + 1);\n              await window.electronAPI.database.query('UPDATE chatbot_nodes SET next_node_id = ? WHERE id = ?', [actualNextNodeId, currentNodeId]);\n            }\n          }\n        }\n        showSuccess('Flow Updated', 'Chatbot flow updated successfully!');\n        setShowEditModal(false);\n        resetFlowForm();\n        await loadFlows();\n      } else {\n        showError('Update Failed', 'Failed to update chatbot flow: ' + flowResult.error);\n      }\n    } catch (error) {\n      console.error('Error updating chatbot flow:', error);\n      showError('Update Failed', 'Error updating chatbot flow. Please try again.');\n    }\n  };\n  const handleAddNode = async () => {\n    if (!nodeForm.name.trim() || !nodeForm.message.trim()) {\n      showError('Validation Error', 'Please fill in node name and message');\n      return;\n    }\n    let attachmentData = null;\n    let attachmentType = null;\n\n    // Process attachment if present\n    if (nodeForm.attachmentFile) {\n      try {\n        // Convert file to base64 for storage\n        const reader = new FileReader();\n        const fileData = await new Promise((resolve, reject) => {\n          reader.onload = () => resolve(reader.result);\n          reader.onerror = reject;\n          reader.readAsDataURL(nodeForm.attachmentFile);\n        });\n        attachmentData = fileData;\n        attachmentType = nodeForm.attachmentType;\n      } catch (error) {\n        showError('Attachment Error', 'Error processing attachment: ' + error.message);\n        return;\n      }\n    }\n\n    // Prepare options based on node type\n    let nodeOptions = {};\n    if (nodeForm.nodeType === 'question') {\n      // For question nodes, convert string options to Baileys interactive format\n      const filteredOptions = nodeForm.options.filter(opt => opt.trim());\n      const interactiveOptions = filteredOptions.map((option, index) => ({\n        display_text: option.trim(),\n        id: `option_${index + 1}`,\n        title: option.trim()\n      }));\n      nodeOptions = {\n        options: interactiveOptions,\n        extract_variable: nodeForm.extractVariable || null,\n        interaction_type: nodeForm.interactionType || 'buttons'\n      };\n    } else if (nodeForm.nodeType === 'action') {\n      // For action nodes, store action configuration\n      nodeOptions = {\n        action_type: nodeForm.actionType,\n        webhook_url: nodeForm.webhookUrl || null,\n        delay_seconds: nodeForm.delaySeconds || 5,\n        email_recipients: nodeForm.emailRecipients || null,\n        email_subject: nodeForm.emailSubject || null,\n        email_body: nodeForm.emailBody || null,\n        email_template: nodeForm.emailTemplate || null,\n        save_data_fields: nodeForm.saveDataFields || [],\n        api_endpoint: nodeForm.apiEndpoint || null,\n        api_method: nodeForm.apiMethod || 'POST',\n        api_headers: nodeForm.apiHeaders || null,\n        api_body: nodeForm.apiBody || null\n      };\n    } else if (nodeForm.nodeType === 'condition') {\n      // For condition nodes, store condition configuration\n      nodeOptions = {\n        condition_type: nodeForm.conditionType,\n        response_conditions: nodeForm.responseConditions || [],\n        condition_variable: nodeForm.conditionVariable || null,\n        condition_operator: nodeForm.conditionOperator || 'equals',\n        condition_value: nodeForm.conditionValue || null,\n        random_paths: nodeForm.randomPaths || [],\n        true_path: nodeForm.truePath || null,\n        false_path: nodeForm.falsePath || null\n      };\n    } else {\n      nodeOptions = nodeForm.options.filter(opt => opt.trim());\n    }\n    const newNode = {\n      id: Date.now(),\n      // Temporary ID for frontend\n      name: nodeForm.name.trim(),\n      message: nodeForm.message.trim(),\n      nodeType: nodeForm.nodeType,\n      options: nodeOptions,\n      nextNodeId: nodeForm.nextNodeId,\n      templateId: nodeForm.templateId || null,\n      attachmentData: attachmentData,\n      attachmentType: attachmentType,\n      extractVariable: nodeForm.extractVariable || null,\n      conditions: nodeForm.conditions || []\n    };\n    if (editingNodeIndex >= 0) {\n      // Edit existing node\n      const updatedNodes = [...flowForm.nodes];\n      updatedNodes[editingNodeIndex] = newNode;\n      setFlowForm(prev => ({\n        ...prev,\n        nodes: updatedNodes\n      }));\n      setEditingNodeIndex(-1);\n    } else {\n      // Add new node\n      setFlowForm(prev => ({\n        ...prev,\n        nodes: [...prev.nodes, newNode]\n      }));\n    }\n    setShowNodeModal(false);\n    resetNodeForm();\n  };\n  const handleEditNode = index => {\n    const node = flowForm.nodes[index];\n\n    // Handle different node types and their configurations\n    let nodeOptions = [];\n    let extractVariable = '';\n    let interactionType = 'buttons';\n    let actionConfig = {};\n    let conditionConfig = {};\n    if (node.nodeType === 'question') {\n      if (Array.isArray(node.options)) {\n        // Old format: just an array of strings\n        nodeOptions = node.options.map(option => typeof option === 'string' ? option : typeof option === 'object' && option ? option.display_text || option.title || option.name || option.text || '' : '');\n      } else if (typeof node.options === 'object' && node.options) {\n        // New format: object with options array and extract_variable\n        const optionsArray = node.options.options || [];\n        nodeOptions = optionsArray.map(option => typeof option === 'string' ? option : typeof option === 'object' && option ? option.display_text || option.title || option.name || option.text || '' : '');\n        extractVariable = node.options.extract_variable || '';\n        interactionType = node.options.interaction_type || 'buttons';\n      }\n    } else if (node.nodeType === 'action') {\n      // Load action configuration\n      if (typeof node.options === 'object' && node.options) {\n        actionConfig = {\n          actionType: node.options.action_type || 'webhook',\n          webhookUrl: node.options.webhook_url || '',\n          delaySeconds: node.options.delay_seconds || 5,\n          emailRecipients: node.options.email_recipients || '',\n          emailSubject: node.options.email_subject || '',\n          emailBody: node.options.email_body || '',\n          emailTemplate: node.options.email_template || '',\n          saveDataFields: node.options.save_data_fields || [],\n          apiEndpoint: node.options.api_endpoint || '',\n          apiMethod: node.options.api_method || 'POST',\n          apiHeaders: node.options.api_headers || '',\n          apiBody: node.options.api_body || ''\n        };\n      }\n    } else if (node.nodeType === 'condition') {\n      // Load condition configuration\n      if (typeof node.options === 'object' && node.options) {\n        conditionConfig = {\n          conditionType: node.options.condition_type || 'user_response',\n          responseConditions: node.options.response_conditions || [],\n          conditionVariable: node.options.condition_variable || '',\n          conditionOperator: node.options.condition_operator || 'equals',\n          conditionValue: node.options.condition_value || '',\n          randomPaths: node.options.random_paths || [],\n          truePath: node.options.true_path || null,\n          falsePath: node.options.false_path || null\n        };\n      }\n    } else {\n      // Message node or other types\n      if (Array.isArray(node.options)) {\n        nodeOptions = node.options;\n      }\n    }\n    setNodeForm({\n      name: node.name,\n      message: node.message,\n      nodeType: node.nodeType,\n      options: nodeOptions,\n      nextNodeId: node.nextNodeId,\n      templateId: node.templateId || '',\n      extractVariable: extractVariable,\n      interactionType: interactionType,\n      messageType: 'text',\n      variables: {},\n      attachmentFile: null,\n      attachmentType: 'image',\n      conditions: [],\n      // Action node fields\n      actionType: actionConfig.actionType || 'webhook',\n      webhookUrl: actionConfig.webhookUrl || '',\n      delaySeconds: actionConfig.delaySeconds || 5,\n      emailRecipients: actionConfig.emailRecipients || '',\n      emailSubject: actionConfig.emailSubject || '',\n      emailBody: actionConfig.emailBody || '',\n      emailTemplate: actionConfig.emailTemplate || '',\n      saveDataFields: actionConfig.saveDataFields || [],\n      apiEndpoint: actionConfig.apiEndpoint || '',\n      apiMethod: actionConfig.apiMethod || 'POST',\n      apiHeaders: actionConfig.apiHeaders || '',\n      apiBody: actionConfig.apiBody || '',\n      // Condition node fields\n      conditionType: conditionConfig.conditionType || 'user_response',\n      responseConditions: conditionConfig.responseConditions || [],\n      conditionVariable: conditionConfig.conditionVariable || '',\n      conditionOperator: conditionConfig.conditionOperator || 'equals',\n      conditionValue: conditionConfig.conditionValue || '',\n      randomPaths: conditionConfig.randomPaths || [],\n      truePath: conditionConfig.truePath || null,\n      falsePath: conditionConfig.falsePath || null\n    });\n    setEditingNodeIndex(index);\n    setShowNodeModal(true);\n  };\n  const handleDeleteNode = async index => {\n    const confirmed = await confirm('Are you sure you want to delete this node?', 'Delete Node');\n    if (confirmed) {\n      const updatedNodes = flowForm.nodes.filter((_, i) => i !== index);\n      setFlowForm(prev => ({\n        ...prev,\n        nodes: updatedNodes\n      }));\n    }\n  };\n  const handleTemplateSelect = templateId => {\n    const safeTemplates = Array.isArray(templates) ? templates : [];\n    const template = safeTemplates.find(t => t.id === parseInt(templateId));\n    if (template) {\n      // Parse template variables\n      let variables = [];\n      try {\n        variables = JSON.parse(template.variables || '[]');\n        if (!Array.isArray(variables)) {\n          variables = [];\n        }\n      } catch (error) {\n        console.error('Error parsing template variables:', error);\n        variables = [];\n      }\n      const variablesObj = {};\n      variables.forEach(variable => {\n        // Ensure variable is a string\n        const varName = typeof variable === 'string' ? variable : String(variable || '');\n        if (varName) {\n          variablesObj[varName] = `{{${varName}}}`;\n        }\n      });\n      setNodeForm(prev => ({\n        ...prev,\n        templateId: templateId,\n        message: template.content || '',\n        variables: variablesObj\n      }));\n    } else {\n      setNodeForm(prev => ({\n        ...prev,\n        templateId: '',\n        message: '',\n        variables: {}\n      }));\n    }\n  };\n  const resetFlowForm = () => {\n    setFlowForm({\n      id: null,\n      // Add ID field for editing\n      name: '',\n      description: '',\n      sessionId: '',\n      triggerKeywords: '',\n      isActive: true,\n      cooldownMinutes: 0,\n      nodes: []\n    });\n  };\n  const resetNodeForm = () => {\n    setNodeForm({\n      name: '',\n      message: '',\n      nodeType: 'message',\n      messageType: 'text',\n      options: [],\n      nextNodeId: null,\n      templateId: '',\n      variables: {},\n      attachmentFile: null,\n      attachmentType: 'image',\n      extractVariable: '',\n      interactionType: 'buttons',\n      // 'buttons' or 'text'\n      conditions: [],\n      // Action node fields\n      actionType: 'webhook',\n      webhookUrl: '',\n      delaySeconds: 5,\n      emailRecipients: '',\n      emailSubject: '',\n      emailBody: '',\n      emailTemplate: '',\n      saveDataFields: [],\n      apiEndpoint: '',\n      apiMethod: 'POST',\n      apiHeaders: '',\n      apiBody: '',\n      // Condition node fields\n      conditionType: 'user_response',\n      responseConditions: [],\n      conditionVariable: '',\n      conditionOperator: 'equals',\n      conditionValue: '',\n      randomPaths: [],\n      truePath: null,\n      falsePath: null\n    });\n  };\n  const addOption = () => {\n    const currentOptions = Array.isArray(nodeForm.options) ? nodeForm.options : [];\n    setNodeForm(prev => ({\n      ...prev,\n      options: [...currentOptions, '']\n    }));\n  };\n  const updateOption = (index, value) => {\n    const updatedOptions = [...(Array.isArray(nodeForm.options) ? nodeForm.options : [])];\n    updatedOptions[index] = String(value || ''); // Ensure it's always a string\n    setNodeForm(prev => ({\n      ...prev,\n      options: updatedOptions\n    }));\n  };\n  const removeOption = index => {\n    const currentOptions = Array.isArray(nodeForm.options) ? nodeForm.options : [];\n    const updatedOptions = currentOptions.filter((_, i) => i !== index);\n    setNodeForm(prev => ({\n      ...prev,\n      options: updatedOptions\n    }));\n  };\n\n  // Optimized onChange handlers to prevent re-renders\n  const handleSearchTermChange = useCallback(e => {\n    setSearchTerm(e.target.value);\n  }, []);\n  const handleStatusFilterChange = useCallback(e => {\n    setStatusFilter(e.target.value);\n  }, []);\n  const handleFlowFormNameChange = useCallback(e => {\n    setFlowForm(prev => ({\n      ...prev,\n      name: e.target.value\n    }));\n  }, []);\n  const handleFlowFormDescriptionChange = useCallback(e => {\n    setFlowForm(prev => ({\n      ...prev,\n      description: e.target.value\n    }));\n  }, []);\n  const handleFlowFormSessionChange = useCallback(e => {\n    setFlowForm(prev => ({\n      ...prev,\n      sessionId: e.target.value\n    }));\n  }, []);\n  const handleFlowFormKeywordsChange = useCallback(e => {\n    setFlowForm(prev => ({\n      ...prev,\n      triggerKeywords: e.target.value\n    }));\n  }, []);\n  const handleFlowFormCooldownChange = useCallback(e => {\n    setFlowForm(prev => ({\n      ...prev,\n      cooldownMinutes: parseInt(e.target.value) || 0\n    }));\n  }, []);\n  const handleFlowFormActiveChange = useCallback(e => {\n    setFlowForm(prev => ({\n      ...prev,\n      isActive: e.target.checked\n    }));\n  }, []);\n  const handleNodeFormNameChange = useCallback(e => {\n    setNodeForm(prev => ({\n      ...prev,\n      name: e.target.value\n    }));\n  }, []);\n  const handleNodeFormMessageChange = useCallback(e => {\n    setNodeForm(prev => ({\n      ...prev,\n      message: e.target.value\n    }));\n  }, []);\n  const handleNodeFormAttachmentTypeChange = useCallback(e => {\n    setNodeForm(prev => ({\n      ...prev,\n      attachmentType: e.target.value\n    }));\n  }, []);\n  const handleNodeFormAttachmentFileChange = useCallback(e => {\n    setNodeForm(prev => ({\n      ...prev,\n      attachmentFile: e.target.files[0]\n    }));\n  }, []);\n  const handleNodeFormExtractVariableChange = useCallback(e => {\n    setNodeForm(prev => ({\n      ...prev,\n      extractVariable: e.target.value\n    }));\n  }, []);\n  const handleNodeFormNextNodeChange = useCallback(e => {\n    setNodeForm(prev => ({\n      ...prev,\n      nextNodeId: e.target.value ? parseInt(e.target.value) : null\n    }));\n  }, []);\n\n  // Export chatbots functionality\n  const handleExportChatbots = async () => {\n    try {\n      // Get all chatbot flows with their nodes\n      const flowsResponse = await window.electronAPI.database.query('SELECT * FROM chatbot_flows ORDER BY created_at DESC');\n      if (!flowsResponse.success) {\n        showError('Export Failed', 'Failed to fetch chatbot flows: ' + flowsResponse.error);\n        return;\n      }\n      const flows = flowsResponse.data || [];\n\n      // Get AI chatbots\n      const aiChatbotsResponse = await window.electronAPI.database.query('SELECT * FROM ai_chatbots ORDER BY created_at DESC');\n      const aiChatbots = aiChatbotsResponse.success ? aiChatbotsResponse.data || [] : [];\n      const exportData = {\n        version: '1.0',\n        exportDate: new Date().toISOString(),\n        type: 'chatbot_complete_export',\n        flows: [],\n        aiChatbots: aiChatbots.map(bot => ({\n          ...bot,\n          // Remove sensitive data\n          api_key: '[REDACTED]'\n        }))\n      };\n\n      // Get nodes for each flow\n      for (const flow of flows) {\n        const nodesResponse = await window.electronAPI.database.query('SELECT * FROM chatbot_nodes WHERE flow_id = ? ORDER BY position ASC', [flow.id]);\n        const nodes = nodesResponse.success ? nodesResponse.data || [] : [];\n        exportData.flows.push({\n          ...flow,\n          nodes: nodes\n        });\n      }\n\n      // Create and download the file\n      const dataStr = JSON.stringify(exportData, null, 2);\n      const dataBlob = new Blob([dataStr], {\n        type: 'application/json'\n      });\n      const link = document.createElement('a');\n      link.href = URL.createObjectURL(dataBlob);\n      link.download = `chatbots-complete-${new Date().toISOString().split('T')[0]}.json`;\n      link.click();\n      showSuccess('Export Complete', `Successfully exported ${flows.length} chatbot flows and ${aiChatbots.length} AI chatbots!`);\n    } catch (error) {\n      console.error('Error exporting chatbots:', error);\n      showError('Export Failed', 'Error exporting chatbots. Please try again.');\n    }\n  };\n\n  // Clean up orphaned conversations\n  const handleCleanupOrphanedConversations = async () => {\n    try {\n      const shouldProceed = await confirm('This will end all active conversations for disabled or deleted chatbot flows. This can help fix issues with stuck conversations. Continue?', 'Cleanup Orphaned Conversations');\n      if (!shouldProceed) {\n        return;\n      }\n      const result = await window.electronAPI.invoke('chatbot:cleanup-orphaned-conversations');\n      if (result.success) {\n        showSuccess('Cleanup Complete', `Successfully cleaned up ${result.cleaned} orphaned conversations (${result.inactiveFlows} from inactive flows, ${result.deletedFlows} from deleted flows)`);\n      } else {\n        showError('Cleanup Failed', 'Failed to cleanup orphaned conversations: ' + result.error);\n      }\n    } catch (error) {\n      console.error('Error cleaning up orphaned conversations:', error);\n      showError('Cleanup Failed', 'Error cleaning up orphaned conversations. Please try again.');\n    }\n  };\n\n  // Import chatbots functionality\n  const handleImportChatbots = async () => {\n    try {\n      // Show in-app confirmation dialog\n      const shouldProceed = await confirm('This will import chatbot flows and AI chatbots from a JSON file. Existing data will not be affected. Continue?', 'Import Chatbot Data');\n      if (!shouldProceed) {\n        return;\n      }\n      const result = await window.electronAPI.showOpenDialog({\n        title: 'Import Chatbot Data',\n        filters: [{\n          name: 'JSON Files',\n          extensions: ['json']\n        }, {\n          name: 'All Files',\n          extensions: ['*']\n        }],\n        properties: ['openFile']\n      });\n      if (result.canceled || !result.filePaths || result.filePaths.length === 0) {\n        return;\n      }\n      const filePath = result.filePaths[0];\n\n      // Read file content using the exposed fs API\n      const fileResult = await window.electronAPI.fs.readFile(filePath);\n      if (!fileResult.success) {\n        showError('Import Failed', 'Failed to read file: ' + fileResult.error);\n        return;\n      }\n      let importData;\n      try {\n        importData = JSON.parse(fileResult.data);\n      } catch (parseError) {\n        showError('Import Failed', 'Invalid JSON file format. Please check the file and try again.');\n        return;\n      }\n\n      // Validate import data\n      if (!importData.flows && !importData.aiChatbots) {\n        showError('Import Failed', 'Invalid file format. Expected chatbot data.');\n        return;\n      }\n\n      // Show preview of what will be imported\n      const flowCount = importData.flows ? importData.flows.length : 0;\n      const aiChatbotCount = importData.aiChatbots ? importData.aiChatbots.length : 0;\n      let previewMessage = 'Import Preview:\\n\\n';\n      if (flowCount > 0) {\n        previewMessage += `• ${flowCount} chatbot flow(s)\\n`;\n      }\n      if (aiChatbotCount > 0) {\n        previewMessage += `• ${aiChatbotCount} AI chatbot(s)\\n`;\n      }\n      previewMessage += '\\nProceed with import?';\n      const shouldImport = await confirm(previewMessage, 'Confirm Import');\n      if (!shouldImport) {\n        return;\n      }\n      let flowSuccessCount = 0;\n      let flowErrorCount = 0;\n      let aiChatbotSuccessCount = 0;\n      let aiChatbotErrorCount = 0;\n\n      // Import chatbot flows\n      if (importData.flows && Array.isArray(importData.flows)) {\n        for (const flowData of importData.flows) {\n          try {\n            // Create the flow (excluding id, created_at, updated_at)\n            const flowResult = await window.electronAPI.database.query(`INSERT INTO chatbot_flows (\n                session_id, name, description, trigger_keywords, is_active,\n                welcome_message, fallback_message, cooldown_minutes,\n                conversation_count, last_triggered, created_at, updated_at\n              ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)`, [flowData.session_id || '', flowData.name || 'Imported Flow', flowData.description || null, flowData.trigger_keywords || '', flowData.is_active !== undefined ? flowData.is_active : 1, flowData.welcome_message || null, flowData.fallback_message || null, flowData.cooldown_minutes || 0, 0,\n            // Reset conversation count\n            null // Reset last triggered\n            ]);\n            if (flowResult.success && flowData.nodes && Array.isArray(flowData.nodes)) {\n              const newFlowId = flowResult.insertId;\n\n              // Import nodes for this flow\n              for (const nodeData of flowData.nodes) {\n                await window.electronAPI.database.query(`INSERT INTO chatbot_nodes (\n                    flow_id, name, message, node_type, options, next_node_id,\n                    position, template_id, attachment_data, attachment_type,\n                    created_at, updated_at\n                  ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)`, [newFlowId, nodeData.name || 'Imported Node', nodeData.message || '', nodeData.node_type || 'message', nodeData.options || null, nodeData.next_node_id || null, nodeData.position || 0, nodeData.template_id || null, nodeData.attachment_data || null, nodeData.attachment_type || null]);\n              }\n            }\n            flowSuccessCount++;\n          } catch (error) {\n            console.error('Error importing flow:', error);\n            flowErrorCount++;\n          }\n        }\n      }\n\n      // Import AI chatbots (note: API keys will need to be manually configured)\n      if (importData.aiChatbots && Array.isArray(importData.aiChatbots)) {\n        for (const aiChatbotData of importData.aiChatbots) {\n          try {\n            // Skip if API key is redacted and prompt user\n            if (aiChatbotData.api_key === '[REDACTED]') {\n              console.warn('Skipping AI chatbot import - API key was redacted for security');\n              continue;\n            }\n            await window.electronAPI.database.query(`INSERT INTO ai_chatbots (\n                name, description, provider, api_key, model, temperature, max_tokens,\n                system_prompt, language, is_active, session_ids, features,\n                personality, industry, response_delay, fallback_message,\n                max_conversation_length, enable_learning, confidence_threshold,\n                created_at, updated_at\n              ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)`, [(aiChatbotData.name || 'Imported AI Chatbot') + ' (Imported)', aiChatbotData.description || null, aiChatbotData.provider || 'openai', aiChatbotData.api_key || '', aiChatbotData.model || 'gpt-3.5-turbo', aiChatbotData.temperature || 0.7, aiChatbotData.max_tokens || 1000, aiChatbotData.system_prompt || null, aiChatbotData.language || 'en', 0,\n            // Set as inactive by default for security\n            aiChatbotData.session_ids || null, aiChatbotData.features || null, aiChatbotData.personality || 'professional', aiChatbotData.industry || 'general', aiChatbotData.response_delay || 1000, aiChatbotData.fallback_message || null, aiChatbotData.max_conversation_length || 50, aiChatbotData.enable_learning !== undefined ? aiChatbotData.enable_learning : 1, aiChatbotData.confidence_threshold || 0.7]);\n            aiChatbotSuccessCount++;\n          } catch (error) {\n            console.error('Error importing AI chatbot:', error);\n            aiChatbotErrorCount++;\n          }\n        }\n      }\n\n      // Reload flows\n      await loadFlows();\n      const totalSuccess = flowSuccessCount + aiChatbotSuccessCount;\n      const totalErrors = flowErrorCount + aiChatbotErrorCount;\n      let message = 'Import completed!\\n';\n      if (flowSuccessCount > 0) {\n        message += `Chatbot flows imported: ${flowSuccessCount}\\n`;\n      }\n      if (aiChatbotSuccessCount > 0) {\n        message += `AI chatbots imported: ${aiChatbotSuccessCount}\\n`;\n      }\n      if (totalErrors > 0) {\n        message += `Errors: ${totalErrors}\\n`;\n      }\n      if (importData.aiChatbots && importData.aiChatbots.some(bot => bot.api_key === '[REDACTED]')) {\n        message += '\\nNote: AI chatbots with redacted API keys were skipped. Please configure API keys manually.';\n      }\n      showSuccess('Import Complete', message);\n    } catch (error) {\n      console.error('Error importing chatbots:', error);\n      showError('Import Failed', 'Error importing chatbots. Please check the file format and try again.');\n    }\n  };\n\n  // Filter flows - ensure flows is always an array\n  const safeFlows = Array.isArray(flows) ? flows : [];\n  const filteredFlows = safeFlows.filter(flow => {\n    const flowName = typeof flow.name === 'string' ? flow.name : String(flow.name || '');\n    const triggerKeywords = typeof flow.trigger_keywords === 'string' ? flow.trigger_keywords : String(flow.trigger_keywords || '');\n    const matchesSearch = flowName.toLowerCase().includes(searchTerm.toLowerCase()) || triggerKeywords.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesStatus = statusFilter === 'all' || statusFilter === 'active' && flow.is_active || statusFilter === 'inactive' && !flow.is_active;\n    return matchesSearch && matchesStatus;\n  });\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-center h-64\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-6 h-6 border-2 border-blue-500 border-t-transparent rounded-full animate-spin\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1333,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-gray-600\",\n          children: \"Loading chatbot flows...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1334,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1332,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1331,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-2xl font-bold text-gray-900\",\n          children: \"Chatbot\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1345,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"Create intelligent conversational flows\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1346,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1344,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleExportChatbots,\n          className: \"btn-secondary flex items-center space-x-2\",\n          title: \"Export Chatbot Flows\",\n          children: [/*#__PURE__*/_jsxDEV(ArrowDownTrayIcon, {\n            className: \"w-5 h-5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1354,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Export\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1355,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1349,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleImportChatbots,\n          className: \"btn-secondary flex items-center space-x-2\",\n          title: \"Import Chatbot Flows\",\n          children: [/*#__PURE__*/_jsxDEV(ArrowUpTrayIcon, {\n            className: \"w-5 h-5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1362,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Import\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1363,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1357,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleCleanupOrphanedConversations,\n          className: \"btn-secondary flex items-center space-x-2\",\n          title: \"Clean up orphaned conversations from disabled/deleted flows\",\n          children: [/*#__PURE__*/_jsxDEV(TrashIcon, {\n            className: \"w-5 h-5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1370,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Cleanup\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1371,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1365,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: loadSessions,\n          className: \"btn-secondary flex items-center space-x-2\",\n          title: \"Refresh Sessions\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"\\uD83D\\uDD04\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1378,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Refresh\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1379,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1373,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setShowCreateModal(true),\n          className: \"btn-primary flex items-center space-x-2\",\n          disabled: sessions.length === 0,\n          children: [/*#__PURE__*/_jsxDEV(PlusIcon, {\n            className: \"w-5 h-5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1386,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Create Flow\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1387,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1381,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1348,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1343,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-col sm:flex-row gap-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative flex-1\",\n        children: [/*#__PURE__*/_jsxDEV(MagnifyingGlassIcon, {\n          className: \"w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1396,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          placeholder: \"Search flows...\",\n          value: searchTerm,\n          onChange: handleSearchTermChange,\n          className: \"input-field pl-10\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1397,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1395,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n        value: statusFilter,\n        onChange: handleStatusFilterChange,\n        className: \"input-field sm:w-48\",\n        children: [/*#__PURE__*/_jsxDEV(\"option\", {\n          value: \"all\",\n          children: \"All Flows\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1412,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n          value: \"active\",\n          children: \"Active\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1413,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n          value: \"inactive\",\n          children: \"Inactive\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1414,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1407,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1393,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-shrink-0\",\n            children: /*#__PURE__*/_jsxDEV(ChatBubbleOvalLeftEllipsisIcon, {\n              className: \"h-8 w-8 text-blue-500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1423,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1422,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"ml-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-medium text-gray-500\",\n              children: \"Total Flows\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1426,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-2xl font-bold text-gray-900\",\n              children: flows.length\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1427,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1425,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1421,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1420,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-shrink-0\",\n            children: /*#__PURE__*/_jsxDEV(PlayIcon, {\n              className: \"h-8 w-8 text-green-500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1435,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1434,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"ml-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-medium text-gray-500\",\n              children: \"Active Flows\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1438,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-2xl font-bold text-gray-900\",\n              children: safeFlows.filter(f => f.is_active).length\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1439,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1437,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1433,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1432,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-shrink-0\",\n            children: /*#__PURE__*/_jsxDEV(BoltIcon, {\n              className: \"h-8 w-8 text-purple-500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1449,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1448,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"ml-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-medium text-gray-500\",\n              children: \"Total Nodes\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1452,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-2xl font-bold text-gray-900\",\n              children: safeFlows.reduce((total, f) => total + (f.node_count || 0), 0)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1453,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1451,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1447,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1446,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-shrink-0\",\n            children: /*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n              className: \"h-8 w-8 text-yellow-500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1463,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1462,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"ml-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-medium text-gray-500\",\n              children: \"Conversations\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1466,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-2xl font-bold text-gray-900\",\n              children: safeFlows.reduce((total, f) => total + (f.conversation_count || 0), 0)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1467,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1465,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1461,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1460,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1419,\n      columnNumber: 7\n    }, this), sessions.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-2\",\n        children: [/*#__PURE__*/_jsxDEV(ExclamationTriangleIcon, {\n          className: \"w-5 h-5 text-yellow-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1479,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-sm font-medium text-yellow-800\",\n            children: \"WhatsApp Connection Required\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1481,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-yellow-700 mt-1\",\n            children: \"Connect a WhatsApp device first to create chatbot flows.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1482,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xs text-yellow-600 mt-2\",\n            children: [\"Debug: Sessions loaded: \", sessions.length, \" | Check browser console for details\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1485,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1480,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1478,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1477,\n      columnNumber: 9\n    }, this), filteredFlows.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center py-12\",\n      children: [/*#__PURE__*/_jsxDEV(ChatBubbleOvalLeftEllipsisIcon, {\n        className: \"w-16 h-16 text-gray-400 mx-auto mb-4\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1496,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-medium text-gray-900 mb-2\",\n        children: searchTerm || statusFilter !== 'all' ? 'No flows found' : 'No chatbot flows yet'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1497,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-600 mb-6\",\n        children: searchTerm || statusFilter !== 'all' ? 'Try adjusting your search or filter criteria' : 'Create your first chatbot flow to start automated conversations'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1500,\n        columnNumber: 11\n      }, this), !searchTerm && statusFilter === 'all' && sessions.length > 0 && /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => setShowCreateModal(true),\n        className: \"btn-primary flex items-center space-x-2 mx-auto\",\n        children: [/*#__PURE__*/_jsxDEV(PlusIcon, {\n          className: \"w-5 h-5\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1511,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Create Flow\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1512,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1507,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1495,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gray-50 px-6 py-4 border-b border-gray-200\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-12 gap-4 items-center text-sm font-medium text-gray-700\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-span-4\",\n            children: \"Flow\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1522,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-span-2\",\n            children: \"Status\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1523,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-span-2\",\n            children: \"Device\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1524,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-span-2\",\n            children: \"Nodes\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1525,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-span-1\",\n            children: \"Conversations\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1526,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-span-1 text-right\",\n            children: \"Actions\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1527,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1521,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1520,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"divide-y divide-gray-200\",\n        children: filteredFlows.map(flow => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"px-6 py-4 hover:bg-gray-50 transition-colors duration-150\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-12 gap-4 items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"col-span-4\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-start space-x-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `w-10 h-10 rounded-lg flex items-center justify-center flex-shrink-0 ${flow.is_active ? 'bg-purple-100' : 'bg-gray-100'}`,\n                  children: flow.is_active ? /*#__PURE__*/_jsxDEV(ChatBubbleOvalLeftEllipsisIcon, {\n                    className: \"w-5 h-5 text-purple-600\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1543,\n                    columnNumber: 27\n                  }, this) : /*#__PURE__*/_jsxDEV(ChatBubbleOvalLeftEllipsisIcon, {\n                    className: \"w-5 h-5 text-gray-600\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1545,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1539,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 min-w-0\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"font-semibold text-gray-900 truncate\",\n                    children: typeof flow.name === 'string' ? flow.name : String(flow.name || 'Unnamed Flow')\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1549,\n                    columnNumber: 25\n                  }, this), flow.description && /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-500 line-clamp-2 mt-1\",\n                    children: flow.description\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1553,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex flex-wrap gap-1 mt-2\",\n                    children: [(typeof flow.trigger_keywords === 'string' ? flow.trigger_keywords : String(flow.trigger_keywords || '')).split(',').slice(0, 3).map((keyword, index) => /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"inline-flex items-center px-2 py-1 rounded text-xs bg-blue-100 text-blue-800\",\n                      children: keyword.trim()\n                    }, index, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1560,\n                      columnNumber: 29\n                    }, this)), (typeof flow.trigger_keywords === 'string' ? flow.trigger_keywords : String(flow.trigger_keywords || '')).split(',').length > 3 && /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-xs text-gray-500\",\n                      children: [\"+\", (typeof flow.trigger_keywords === 'string' ? flow.trigger_keywords : String(flow.trigger_keywords || '')).split(',').length - 3, \" more\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1568,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1558,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1548,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1538,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1537,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"col-span-2\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${flow.is_active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}`,\n                children: [flow.is_active ? /*#__PURE__*/_jsxDEV(PlayIcon, {\n                  className: \"w-3 h-3 mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1582,\n                  columnNumber: 41\n                }, this) : /*#__PURE__*/_jsxDEV(PauseIcon, {\n                  className: \"w-3 h-3 mr-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1582,\n                  columnNumber: 81\n                }, this), flow.is_active ? 'Active' : 'Inactive']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1579,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1578,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"col-span-2\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-gray-900 font-medium\",\n                children: flow.device_name ? typeof flow.device_name === 'string' ? flow.device_name : String(flow.device_name) : 'No Device'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1589,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1588,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"col-span-2\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-purple-100 text-purple-800\",\n                children: [flow.node_count || 0, \" nodes\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1599,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1598,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"col-span-1\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-gray-900 font-medium\",\n                children: flow.conversation_count || 0\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1606,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1605,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"col-span-1\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-end space-x-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => {\n                    setSelectedFlow(flow);\n                    setShowFlowModal(true);\n                  },\n                  className: \"p-2 text-gray-400 hover:text-blue-500 hover:bg-blue-50 rounded-lg transition-colors\",\n                  title: \"View Flow\",\n                  children: /*#__PURE__*/_jsxDEV(EyeIcon, {\n                    className: \"w-4 h-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1622,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1614,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => handleEditFlow(flow),\n                  className: \"p-2 text-gray-400 hover:text-purple-500 hover:bg-purple-50 rounded-lg transition-colors\",\n                  title: \"Edit Flow\",\n                  children: /*#__PURE__*/_jsxDEV(PencilIcon, {\n                    className: \"w-4 h-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1629,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1624,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => toggleFlowStatus(flow.id, flow.is_active),\n                  className: `p-2 text-gray-400 hover:bg-opacity-10 rounded-lg transition-colors ${flow.is_active ? 'hover:text-yellow-500 hover:bg-yellow-50' : 'hover:text-green-500 hover:bg-green-50'}`,\n                  title: flow.is_active ? 'Pause Flow' : 'Activate Flow',\n                  children: flow.is_active ? /*#__PURE__*/_jsxDEV(PauseIcon, {\n                    className: \"w-4 h-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1640,\n                    columnNumber: 43\n                  }, this) : /*#__PURE__*/_jsxDEV(PlayIcon, {\n                    className: \"w-4 h-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1640,\n                    columnNumber: 79\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1631,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => handleDeleteFlow(flow.id),\n                  className: \"p-2 text-gray-400 hover:text-red-500 hover:bg-red-50 rounded-lg transition-colors\",\n                  title: \"Delete Flow\",\n                  children: /*#__PURE__*/_jsxDEV(TrashIcon, {\n                    className: \"w-4 h-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1647,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1642,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1613,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1612,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1535,\n            columnNumber: 17\n          }, this)\n        }, flow.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1534,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1532,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1517,\n      columnNumber: 9\n    }, this), showCreateModal && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900\",\n              children: \"Create Chatbot Flow\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1664,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                setShowCreateModal(false);\n                resetFlowForm();\n              },\n              className: \"p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg\",\n              children: /*#__PURE__*/_jsxDEV(XMarkIcon, {\n                className: \"w-5 h-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1672,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1665,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1663,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"font-medium text-gray-900\",\n                children: \"Flow Settings\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1679,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Flow Name *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1682,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: flowForm.name,\n                  onChange: handleFlowFormNameChange,\n                  placeholder: \"e.g., Customer Support Bot\",\n                  className: \"input-field\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1685,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1681,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Description\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1695,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                  value: flowForm.description,\n                  onChange: handleFlowFormDescriptionChange,\n                  placeholder: \"Describe what this chatbot flow does...\",\n                  rows: 3,\n                  className: \"input-field resize-none\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1698,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1694,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"WhatsApp Session *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1708,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  value: flowForm.sessionId,\n                  onChange: handleFlowFormSessionChange,\n                  className: \"input-field\",\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"Select a session\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1716,\n                    columnNumber: 23\n                  }, this), (Array.isArray(sessions) ? sessions : []).map(session => {\n                    // Ensure session is an object and has required properties\n                    if (!session || typeof session !== 'object') {\n                      return null;\n                    }\n                    const sessionId = session.session_id || `session_${Math.random()}`;\n                    const deviceName = typeof session.device_name === 'string' ? session.device_name : String(session.device_name || 'Unknown Device');\n                    const phoneNumber = typeof session.phone_number === 'string' || typeof session.phone_number === 'number' ? session.phone_number : String(session.phone_number || 'Unknown');\n                    return /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: sessionId,\n                      children: [deviceName, \" (+\", phoneNumber, \")\"]\n                    }, sessionId, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1728,\n                      columnNumber: 27\n                    }, this);\n                  }).filter(Boolean)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1711,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1707,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Trigger Keywords *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1737,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: flowForm.triggerKeywords,\n                  onChange: handleFlowFormKeywordsChange,\n                  placeholder: \"support, help, bot (comma separated)\",\n                  className: \"input-field\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1740,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs text-gray-500 mt-1\",\n                  children: \"Keywords that will trigger this chatbot flow\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1747,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1736,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: [/*#__PURE__*/_jsxDEV(ClockIcon, {\n                    className: \"w-4 h-4 inline mr-1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1754,\n                    columnNumber: 23\n                  }, this), \"Cooldown Period (minutes)\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1753,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  min: \"0\",\n                  value: flowForm.cooldownMinutes,\n                  onChange: handleFlowFormCooldownChange,\n                  placeholder: \"0\",\n                  className: \"input-field\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1757,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs text-gray-500 mt-1\",\n                  children: \"Minimum time between flow triggers for the same user (0 = no cooldown)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1765,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1752,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"checkbox\",\n                  id: \"flowIsActive\",\n                  checked: flowForm.isActive,\n                  onChange: handleFlowFormActiveChange,\n                  className: \"text-blue-600\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1771,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"flowIsActive\",\n                  className: \"ml-2 text-sm text-gray-700\",\n                  children: \"Flow is active\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1778,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1770,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1678,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"font-medium text-gray-900\",\n                  children: [\"Flow Nodes (\", flowForm.nodes.length, \")\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1787,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setShowNodeModal(true),\n                  className: \"btn-secondary flex items-center space-x-2 text-sm\",\n                  children: [/*#__PURE__*/_jsxDEV(PlusIcon, {\n                    className: \"w-4 h-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1792,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Add Node\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1793,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1788,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1786,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-2 max-h-64 overflow-y-auto\",\n                children: [flowForm.nodes.map((node, index) => {\n                  // Ensure node is an object and has required properties\n                  if (!node || typeof node !== 'object') {\n                    return null;\n                  }\n                  const nodeName = typeof node.name === 'string' ? node.name : 'Unnamed Node';\n                  const nodeType = typeof node.nodeType === 'string' ? node.nodeType : 'unknown';\n                  const nodeMessage = typeof node.message === 'string' ? node.message : 'No message';\n                  return /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-3 border border-gray-200 rounded-lg\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-start justify-between\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex-1\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex items-center space-x-2\",\n                          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"text-sm font-medium text-gray-900\",\n                            children: [index + 1, \". \", nodeName]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1813,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"inline-flex items-center px-2 py-1 rounded text-xs bg-blue-100 text-blue-800\",\n                            children: nodeType\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1814,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1812,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"text-xs text-gray-600 mt-1 line-clamp-2\",\n                          children: nodeMessage\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1818,\n                          columnNumber: 31\n                        }, this), (() => {\n                          // Handle both old array format and new Baileys interactive format\n                          let optionsToDisplay = [];\n                          if (Array.isArray(node.options)) {\n                            optionsToDisplay = node.options;\n                          } else if (typeof node.options === 'object' && node.options && node.options.options) {\n                            optionsToDisplay = node.options.options;\n                          }\n                          if (optionsToDisplay.length > 0) {\n                            return /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"flex flex-wrap gap-1 mt-2\",\n                              children: optionsToDisplay.map((option, optIndex) => {\n                                // Ensure we always render a string\n                                let displayText = 'Option';\n                                if (typeof option === 'string') {\n                                  displayText = option;\n                                } else if (typeof option === 'object' && option) {\n                                  // Handle Baileys interactive format\n                                  displayText = String(option.display_text || option.title || option.name || option.text || option.value || 'Option');\n                                } else {\n                                  displayText = String(option || 'Option');\n                                }\n                                return /*#__PURE__*/_jsxDEV(\"span\", {\n                                  className: \"text-xs px-1 py-0.5 bg-gray-100 rounded\",\n                                  children: displayText\n                                }, optIndex, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1844,\n                                  columnNumber: 41\n                                }, this);\n                              })\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1830,\n                              columnNumber: 35\n                            }, this);\n                          }\n                          return null;\n                        })()]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1811,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center space-x-1 ml-2\",\n                        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                          onClick: () => handleEditNode(index),\n                          className: \"p-1 text-gray-400 hover:text-blue-500 rounded\",\n                          children: /*#__PURE__*/_jsxDEV(PencilIcon, {\n                            className: \"w-3 h-3\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1860,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1856,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                          onClick: () => handleDeleteNode(index),\n                          className: \"p-1 text-gray-400 hover:text-red-500 rounded\",\n                          children: /*#__PURE__*/_jsxDEV(TrashIcon, {\n                            className: \"w-3 h-3\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1866,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1862,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1855,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1810,\n                      columnNumber: 27\n                    }, this)\n                  }, index, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1809,\n                    columnNumber: 25\n                  }, this);\n                }).filter(Boolean), flowForm.nodes.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-center py-8 text-gray-500\",\n                  children: [/*#__PURE__*/_jsxDEV(ChatBubbleOvalLeftEllipsisIcon, {\n                    className: \"w-8 h-8 mx-auto mb-2 text-gray-400\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1876,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm\",\n                    children: \"No nodes added yet\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1877,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-xs\",\n                    children: \"Add your first node to get started\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1878,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1875,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1797,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1785,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1676,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex space-x-3 mt-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                setShowCreateModal(false);\n                resetFlowForm();\n              },\n              className: \"btn-secondary flex-1\",\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1886,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleCreateFlow,\n              className: \"btn-primary flex-1\",\n              disabled: !flowForm.name.trim() || !flowForm.triggerKeywords.trim() || !flowForm.sessionId || flowForm.nodes.length === 0,\n              children: \"Create Flow\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1895,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1885,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1662,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1661,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1660,\n      columnNumber: 9\n    }, this), showEditModal && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900\",\n              children: \"Edit Chatbot Flow\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1919,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                setShowEditModal(false);\n                resetFlowForm();\n              },\n              className: \"p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg\",\n              children: /*#__PURE__*/_jsxDEV(XMarkIcon, {\n                className: \"w-5 h-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1927,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1920,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1918,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"font-medium text-gray-900\",\n                children: \"Flow Settings\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1934,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Flow Name *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1937,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: flowForm.name,\n                  onChange: handleFlowFormNameChange,\n                  placeholder: \"e.g., Customer Support Bot\",\n                  className: \"input-field\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1940,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1936,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Description\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1950,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                  value: flowForm.description,\n                  onChange: handleFlowFormDescriptionChange,\n                  placeholder: \"Describe what this chatbot flow does...\",\n                  rows: 3,\n                  className: \"input-field resize-none\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1953,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1949,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"WhatsApp Session *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1963,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  value: flowForm.sessionId,\n                  onChange: handleFlowFormSessionChange,\n                  className: \"input-field\",\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"Select a session\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1971,\n                    columnNumber: 23\n                  }, this), (Array.isArray(sessions) ? sessions : []).map(session => {\n                    // Ensure session is an object and has required properties\n                    if (!session || typeof session !== 'object') {\n                      return null;\n                    }\n                    const sessionId = session.session_id || `session_${Math.random()}`;\n                    const deviceName = typeof session.device_name === 'string' ? session.device_name : String(session.device_name || 'Unknown Device');\n                    const phoneNumber = typeof session.phone_number === 'string' || typeof session.phone_number === 'number' ? session.phone_number : String(session.phone_number || 'Unknown');\n                    return /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: sessionId,\n                      children: [deviceName, \" (+\", phoneNumber, \")\"]\n                    }, sessionId, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1983,\n                      columnNumber: 27\n                    }, this);\n                  })]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1966,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1962,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Trigger Keywords *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1992,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: flowForm.triggerKeywords,\n                  onChange: handleFlowFormKeywordsChange,\n                  placeholder: \"e.g., support, help, info (comma separated)\",\n                  className: \"input-field\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1995,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs text-gray-500 mt-1\",\n                  children: \"Separate multiple keywords with commas\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2002,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1991,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-4\",\n                children: /*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"checkbox\",\n                    checked: flowForm.isActive,\n                    onChange: handleFlowFormActiveChange,\n                    className: \"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2009,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"ml-2 text-sm text-gray-700\",\n                    children: \"Active\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2015,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2008,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2007,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Cooldown (minutes)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2020,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  value: flowForm.cooldownMinutes,\n                  onChange: handleFlowFormCooldownChange,\n                  placeholder: \"0\",\n                  min: \"0\",\n                  className: \"input-field\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2023,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs text-gray-500 mt-1\",\n                  children: \"Minimum time between flow triggers for the same user\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2031,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2019,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1933,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"font-medium text-gray-900\",\n                  children: [\"Flow Nodes (\", flowForm.nodes.length, \")\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2040,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setShowNodeModal(true),\n                  className: \"btn-secondary text-sm\",\n                  children: [/*#__PURE__*/_jsxDEV(PlusIcon, {\n                    className: \"w-4 h-4 mr-1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2045,\n                    columnNumber: 23\n                  }, this), \"Add Node\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2041,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2039,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-3 max-h-96 overflow-y-auto\",\n                children: flowForm.nodes.map((node, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-gray-50 rounded-lg p-4 border border-gray-200\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-start justify-between\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex-1\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center space-x-2 mb-2\",\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-xs font-medium text-gray-500\",\n                          children: [\"#\", index + 1]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2056,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-sm font-medium text-gray-900\",\n                          children: node.name\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2057,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: `inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${node.nodeType === 'message' ? 'bg-blue-100 text-blue-800' : node.nodeType === 'question' ? 'bg-green-100 text-green-800' : 'bg-purple-100 text-purple-800'}`,\n                          children: node.nodeType\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2058,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2055,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-sm text-gray-600 mb-2\",\n                        children: node.message\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2066,\n                        columnNumber: 29\n                      }, this), (() => {\n                        if (node.nodeType === 'question' && node.options) {\n                          let optionsToShow = [];\n                          if (Array.isArray(node.options)) {\n                            optionsToShow = node.options;\n                          } else if (typeof node.options === 'object' && node.options.options) {\n                            optionsToShow = node.options.options.map(opt => typeof opt === 'string' ? opt : opt.display_text || opt.title || opt.name || '');\n                          }\n                          if (optionsToShow.length > 0) {\n                            return /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"flex flex-wrap gap-1\",\n                              children: optionsToShow.map((option, optIndex) => /*#__PURE__*/_jsxDEV(\"span\", {\n                                className: \"inline-flex items-center px-2 py-1 rounded text-xs bg-gray-200 text-gray-700\",\n                                children: typeof option === 'string' ? option : String(option || '')\n                              }, optIndex, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 2082,\n                                columnNumber: 41\n                              }, this))\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 2080,\n                              columnNumber: 37\n                            }, this);\n                          }\n                        }\n                        return null;\n                      })()]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2054,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center space-x-1 ml-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: () => handleEditNode(index),\n                        className: \"p-1 text-gray-400 hover:text-blue-500 rounded\",\n                        children: /*#__PURE__*/_jsxDEV(PencilIcon, {\n                          className: \"w-3 h-3\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2098,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2094,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: () => handleDeleteNode(index),\n                        className: \"p-1 text-gray-400 hover:text-red-500 rounded\",\n                        children: /*#__PURE__*/_jsxDEV(TrashIcon, {\n                          className: \"w-3 h-3\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2104,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2100,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2093,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2053,\n                    columnNumber: 25\n                  }, this)\n                }, index, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2052,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2050,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2038,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1931,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-end space-x-3 mt-6 pt-6 border-t border-gray-200\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                setShowEditModal(false);\n                resetFlowForm();\n              },\n              className: \"btn-secondary\",\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2115,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleUpdateFlow,\n              className: \"btn-primary\",\n              disabled: !flowForm.name.trim() || !flowForm.triggerKeywords.trim() || !flowForm.sessionId || flowForm.nodes.length === 0,\n              children: \"Update Flow\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2124,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2114,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1917,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1916,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1915,\n      columnNumber: 9\n    }, this), showNodeModal && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-xl shadow-xl max-w-lg w-full max-h-[90vh] flex flex-col\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-6 flex-shrink-0\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900\",\n              children: editingNodeIndex >= 0 ? 'Edit Node' : 'Add Node'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2148,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                setShowNodeModal(false);\n                resetNodeForm();\n                setEditingNodeIndex(-1);\n              },\n              className: \"p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg\",\n              children: /*#__PURE__*/_jsxDEV(XMarkIcon, {\n                className: \"w-5 h-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2159,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2151,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2147,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2146,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-1 overflow-y-auto px-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Node Name *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2167,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                value: nodeForm.name,\n                onChange: handleNodeFormNameChange,\n                placeholder: \"e.g., Welcome Message\",\n                className: \"input-field\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2170,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2166,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Node Type\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2180,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: nodeForm.nodeType,\n                onChange: e => {\n                  // Reset options when changing node type to prevent object/string conflicts\n                  setNodeForm(prev => ({\n                    ...prev,\n                    nodeType: e.target.value,\n                    options: [],\n                    // Reset options to empty array\n                    extractVariable: '',\n                    // Reset extract variable\n                    conditions: [] // Reset conditions\n                  }));\n                },\n                className: \"input-field\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"message\",\n                  children: \"Message\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2197,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"question\",\n                  children: \"Question\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2198,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"action\",\n                  children: \"Action\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2199,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"condition\",\n                  children: \"Condition\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2200,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2183,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2179,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-3\",\n                children: \"Message Type\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2206,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-2 gap-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  onClick: () => setNodeForm(prev => ({\n                    ...prev,\n                    messageType: 'text',\n                    templateId: ''\n                  })),\n                  className: `p-3 border-2 rounded-lg text-left transition-colors ${nodeForm.messageType === 'text' ? 'border-blue-500 bg-blue-50 text-blue-700' : 'border-gray-200 hover:border-gray-300'}`,\n                  children: [/*#__PURE__*/_jsxDEV(ChatBubbleLeftRightIcon, {\n                    className: \"w-5 h-5 mb-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2219,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"font-medium\",\n                    children: \"Text Message\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2220,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-xs text-gray-500\",\n                    children: \"Custom text with attachments\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2221,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2210,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  onClick: () => setNodeForm(prev => ({\n                    ...prev,\n                    messageType: 'template'\n                  })),\n                  className: `p-3 border-2 rounded-lg text-left transition-colors ${nodeForm.messageType === 'template' ? 'border-blue-500 bg-blue-50 text-blue-700' : 'border-gray-200 hover:border-gray-300'}`,\n                  children: [/*#__PURE__*/_jsxDEV(DocumentTextIcon, {\n                    className: \"w-5 h-5 mb-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2232,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"font-medium\",\n                    children: \"Template\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2233,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-xs text-gray-500\",\n                    children: \"Use predefined template\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2234,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2223,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2209,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2205,\n              columnNumber: 17\n            }, this), nodeForm.messageType === 'template' && /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Select Template *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2242,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: nodeForm.templateId,\n                onChange: e => handleTemplateSelect(e.target.value),\n                className: \"input-field\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Choose a template...\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2250,\n                  columnNumber: 23\n                }, this), (Array.isArray(templates) ? templates : []).map(template => {\n                  // Ensure template is an object and has required properties\n                  if (!template || typeof template !== 'object') {\n                    return null;\n                  }\n                  const templateType = TEMPLATE_TYPES[template.type || 'text'];\n                  const templateName = typeof template.name === 'string' ? template.name : String(template.name || 'Unnamed Template');\n                  const typeName = typeof (templateType === null || templateType === void 0 ? void 0 : templateType.name) === 'string' ? templateType.name : String(template.type || 'Text');\n                  const templateId = template.id || `template_${Math.random()}`;\n                  return /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: templateId,\n                    children: [templateName, \" (\", typeName, \")\"]\n                  }, templateId, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2263,\n                    columnNumber: 27\n                  }, this);\n                }).filter(Boolean)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2245,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2241,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: [nodeForm.messageType === 'template' ? 'Message Preview' : 'Message Content', \" *\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2274,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                value: nodeForm.message,\n                onChange: handleNodeFormMessageChange,\n                placeholder: nodeForm.messageType === 'template' ? 'Select a template to see preview...' : 'Enter the message for this node...',\n                rows: 4,\n                className: \"input-field resize-none\",\n                readOnly: nodeForm.messageType === 'template'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2277,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2273,\n              columnNumber: 17\n            }, this), nodeForm.messageType === 'template' && nodeForm.templateId && Object.keys(nodeForm.variables).length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Template Variables\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2290,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-3\",\n                children: Object.keys(nodeForm.variables).map(variable => {\n                  // Ensure variable is always a string\n                  const safeVariable = typeof variable === 'string' ? variable : String(variable || '');\n                  return /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                      className: \"block text-xs font-medium text-gray-600 mb-1\",\n                      children: safeVariable\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2299,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"text\",\n                      value: typeof nodeForm.variables[safeVariable] === 'string' ? nodeForm.variables[safeVariable] : String(nodeForm.variables[safeVariable] || ''),\n                      onChange: e => setNodeForm(prev => ({\n                        ...prev,\n                        variables: {\n                          ...prev.variables,\n                          [safeVariable]: e.target.value\n                        }\n                      })),\n                      placeholder: `Enter value for {{${safeVariable}}}`,\n                      className: \"input-field\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2302,\n                      columnNumber: 29\n                    }, this)]\n                  }, safeVariable, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2298,\n                    columnNumber: 27\n                  }, this);\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2293,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2289,\n              columnNumber: 19\n            }, this), nodeForm.messageType === 'text' && /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Attachment (Optional)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2325,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"select\", {\n                    value: nodeForm.attachmentType,\n                    onChange: handleNodeFormAttachmentTypeChange,\n                    className: \"input-field w-32\",\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"image\",\n                      children: \"Image\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2335,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"video\",\n                      children: \"Video\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2336,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"audio\",\n                      children: \"Audio\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2337,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"document\",\n                      children: \"Document\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2338,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2330,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"file\",\n                    onChange: handleNodeFormAttachmentFileChange,\n                    accept: nodeForm.attachmentType === 'image' ? 'image/*' : nodeForm.attachmentType === 'video' ? 'video/*' : nodeForm.attachmentType === 'audio' ? 'audio/*' : '*/*',\n                    className: \"input-field flex-1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2340,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2329,\n                  columnNumber: 23\n                }, this), nodeForm.attachmentFile && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"space-y-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-2 text-sm text-gray-600\",\n                    children: [/*#__PURE__*/_jsxDEV(PaperClipIcon, {\n                      className: \"w-4 h-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2355,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: typeof nodeForm.attachmentFile.name === 'string' ? nodeForm.attachmentFile.name : String(nodeForm.attachmentFile.name || 'Unknown file')\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2356,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => setNodeForm(prev => ({\n                        ...prev,\n                        attachmentFile: null\n                      })),\n                      className: \"text-red-500 hover:text-red-700\",\n                      children: /*#__PURE__*/_jsxDEV(XMarkIcon, {\n                        className: \"w-4 h-4\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2361,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2357,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2354,\n                    columnNumber: 27\n                  }, this), nodeForm.nodeType === 'question' && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-3 bg-yellow-50 border border-yellow-200 rounded-lg\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-start space-x-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                        className: \"w-5 h-5 text-yellow-600 mt-0.5 flex-shrink-0\",\n                        fill: \"currentColor\",\n                        viewBox: \"0 0 20 20\",\n                        children: /*#__PURE__*/_jsxDEV(\"path\", {\n                          fillRule: \"evenodd\",\n                          d: \"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\",\n                          clipRule: \"evenodd\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2370,\n                          columnNumber: 35\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2369,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"text-sm font-medium text-yellow-800\",\n                          children: \"Attachment + Interactive Buttons Limitation\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2373,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"text-xs text-yellow-700 mt-1\",\n                          children: \"WhatsApp doesn't support interactive buttons with media attachments. The attachment will be sent, but buttons won't work. Consider using \\\"Text List\\\" instead of \\\"Interactive Buttons\\\" in the options below.\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2376,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2372,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2368,\n                      columnNumber: 31\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2367,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2353,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2328,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2324,\n              columnNumber: 19\n            }, this), nodeForm.nodeType === 'question' && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Options\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2392,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"space-y-2\",\n                  children: [(Array.isArray(nodeForm.options) ? nodeForm.options : []).map((option, index) => {\n                    // Ensure option is always a string\n                    let optionValue = '';\n                    if (typeof option === 'string') {\n                      optionValue = option;\n                    } else if (typeof option === 'object' && option !== null) {\n                      optionValue = String(option.display_text || option.title || option.name || option.text || option.value || '');\n                    } else {\n                      optionValue = String(option || '');\n                    }\n                    return /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center space-x-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                        type: \"text\",\n                        value: optionValue,\n                        onChange: e => updateOption(index, e.target.value),\n                        placeholder: `Option ${index + 1}`,\n                        className: \"input-field flex-1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2409,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: () => removeOption(index),\n                        className: \"p-2 text-red-500 hover:bg-red-50 rounded-lg\",\n                        children: /*#__PURE__*/_jsxDEV(TrashIcon, {\n                          className: \"w-4 h-4\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2420,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2416,\n                        columnNumber: 31\n                      }, this)]\n                    }, index, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2408,\n                      columnNumber: 29\n                    }, this);\n                  }), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: addOption,\n                    className: \"btn-secondary w-full text-sm\",\n                    children: \"Add Option\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2425,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2395,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2391,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Display Options As\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2436,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  value: nodeForm.interactionType || 'buttons',\n                  onChange: e => setNodeForm(prev => ({\n                    ...prev,\n                    interactionType: e.target.value\n                  })),\n                  className: \"input-field\",\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"buttons\",\n                    children: \"Interactive Buttons (\\u22643 options)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2444,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"text\",\n                    children: \"Text List\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2445,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2439,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs text-gray-500 mt-1\",\n                  children: \"Interactive buttons provide better user experience but are limited to 3 options. Text list works for any number of options.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2447,\n                  columnNumber: 23\n                }, this), nodeForm.interactionType === 'buttons' && nodeForm.attachmentFile && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mt-2 p-3 bg-yellow-50 border border-yellow-200 rounded-lg\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-start space-x-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                      className: \"w-5 h-5 text-yellow-600 mt-0.5 flex-shrink-0\",\n                      fill: \"currentColor\",\n                      viewBox: \"0 0 20 20\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\",\n                        clipRule: \"evenodd\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2456,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2455,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-sm font-medium text-yellow-800\",\n                        children: \"Interactive Buttons + Attachments Limitation\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2459,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-xs text-yellow-700 mt-1\",\n                        children: \"WhatsApp doesn't support interactive buttons with media attachments. Consider using \\\"Text List\\\" instead, or remove the attachment to use interactive buttons.\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2462,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2458,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2454,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2453,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2435,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Extract Variable (Optional)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2473,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: nodeForm.extractVariable,\n                  onChange: handleNodeFormExtractVariableChange,\n                  placeholder: \"e.g., name, email, phone\",\n                  className: \"input-field\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2476,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs text-gray-500 mt-1\",\n                  children: \"Variable name to store user's response. Use this variable in future messages to reference it.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2483,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2472,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2390,\n              columnNumber: 19\n            }, this), nodeForm.nodeType === 'action' && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Action Type\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2494,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  value: nodeForm.actionType || 'webhook',\n                  onChange: e => setNodeForm(prev => ({\n                    ...prev,\n                    actionType: e.target.value\n                  })),\n                  className: \"input-field\",\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"webhook\",\n                    children: \"Send Webhook\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2502,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"email\",\n                    children: \"Send Email\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2503,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"save_data\",\n                    children: \"Save Data\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2504,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"api_call\",\n                    children: \"API Call\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2505,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"delay\",\n                    children: \"Add Delay\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2506,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2497,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2493,\n                columnNumber: 21\n              }, this), nodeForm.actionType === 'webhook' && /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Webhook URL\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2512,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"url\",\n                  value: nodeForm.webhookUrl || '',\n                  onChange: e => setNodeForm(prev => ({\n                    ...prev,\n                    webhookUrl: e.target.value\n                  })),\n                  placeholder: \"https://your-webhook-url.com/endpoint\",\n                  className: \"input-field\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2515,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs text-gray-500 mt-1\",\n                  children: \"URL to send conversation data to when this action is triggered.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2522,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2511,\n                columnNumber: 23\n              }, this), nodeForm.actionType === 'delay' && /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Delay Duration (seconds)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2530,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  min: \"1\",\n                  max: \"300\",\n                  value: nodeForm.delaySeconds || 5,\n                  onChange: e => setNodeForm(prev => ({\n                    ...prev,\n                    delaySeconds: parseInt(e.target.value) || 5\n                  })),\n                  className: \"input-field\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2533,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs text-gray-500 mt-1\",\n                  children: \"How long to wait before proceeding to the next node (1-300 seconds).\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2541,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2529,\n                columnNumber: 23\n              }, this), nodeForm.actionType === 'email' && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                    children: \"Email Recipients\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2550,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"email\",\n                    value: nodeForm.emailRecipients || '',\n                    onChange: e => setNodeForm(prev => ({\n                      ...prev,\n                      emailRecipients: e.target.value\n                    })),\n                    placeholder: \"<EMAIL>, <EMAIL>\",\n                    className: \"input-field\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2553,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-xs text-gray-500 mt-1\",\n                    children: \"Comma-separated email addresses to send notification to.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2560,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2549,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                    children: \"Email Subject\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2565,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    value: nodeForm.emailSubject || '',\n                    onChange: e => setNodeForm(prev => ({\n                      ...prev,\n                      emailSubject: e.target.value\n                    })),\n                    placeholder: \"New chatbot conversation data\",\n                    className: \"input-field\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2568,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2564,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                    children: \"Email Body\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2577,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                    value: nodeForm.emailBody || '',\n                    onChange: e => setNodeForm(prev => ({\n                      ...prev,\n                      emailBody: e.target.value\n                    })),\n                    placeholder: \"Hello {{user_name}}, thank you for your interest in {{selected_product}}...\",\n                    className: \"input-field h-32\",\n                    rows: 4\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2580,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-xs text-gray-500 mt-1\",\n                    children: [\"Use \", `{{variable_name}}`, \" to insert dynamic data from conversation.\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2587,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2576,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                    children: \"Email Template (Optional)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2592,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                    value: nodeForm.emailTemplate || '',\n                    onChange: e => setNodeForm(prev => ({\n                      ...prev,\n                      emailTemplate: e.target.value\n                    })),\n                    className: \"input-field\",\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"\",\n                      children: \"No Template\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2600,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"welcome\",\n                      children: \"Welcome Email\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2601,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"followup\",\n                      children: \"Follow-up Email\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2602,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"notification\",\n                      children: \"Notification Email\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2603,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2595,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2591,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2548,\n                columnNumber: 23\n              }, this), nodeForm.actionType === 'api_call' && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                    children: \"API Endpoint\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2612,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"url\",\n                    value: nodeForm.apiEndpoint || '',\n                    onChange: e => setNodeForm(prev => ({\n                      ...prev,\n                      apiEndpoint: e.target.value\n                    })),\n                    placeholder: \"https://api.example.com/endpoint\",\n                    className: \"input-field\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2615,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2611,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                    children: \"HTTP Method\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2624,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                    value: nodeForm.apiMethod || 'POST',\n                    onChange: e => setNodeForm(prev => ({\n                      ...prev,\n                      apiMethod: e.target.value\n                    })),\n                    className: \"input-field\",\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"GET\",\n                      children: \"GET\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2632,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"POST\",\n                      children: \"POST\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2633,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"PUT\",\n                      children: \"PUT\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2634,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"PATCH\",\n                      children: \"PATCH\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2635,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"DELETE\",\n                      children: \"DELETE\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2636,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2627,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2623,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                    children: \"Headers (JSON)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2640,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                    value: nodeForm.apiHeaders || '',\n                    onChange: e => setNodeForm(prev => ({\n                      ...prev,\n                      apiHeaders: e.target.value\n                    })),\n                    placeholder: \"{\\\"Authorization\\\": \\\"Bearer token\\\", \\\"Content-Type\\\": \\\"application/json\\\"}\",\n                    className: \"input-field h-20\",\n                    rows: 2\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2643,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2639,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                    children: \"Request Body (JSON)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2652,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                    value: nodeForm.apiBody || '',\n                    onChange: e => setNodeForm(prev => ({\n                      ...prev,\n                      apiBody: e.target.value\n                    })),\n                    placeholder: \"{\\\"user_name\\\": \\\"{{user_name}}\\\", \\\"selected_product\\\": \\\"{{selected_product}}\\\"}\",\n                    className: \"input-field h-24\",\n                    rows: 3\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2655,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-xs text-gray-500 mt-1\",\n                    children: [\"Use \", `{{variable_name}}`, \" to insert dynamic data from conversation.\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2662,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2651,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2610,\n                columnNumber: 23\n              }, this), nodeForm.actionType === 'save_data' && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-3\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                    children: \"Data Fields to Save\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2672,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                    value: Array.isArray(nodeForm.saveDataFields) ? nodeForm.saveDataFields.join('\\n') : '',\n                    onChange: e => setNodeForm(prev => ({\n                      ...prev,\n                      saveDataFields: e.target.value.split('\\n').filter(field => field.trim())\n                    })),\n                    placeholder: \"user_name\\nuser_email\\nselected_product\\nphone_number\",\n                    className: \"input-field h-24\",\n                    rows: 4\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2675,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-xs text-gray-500 mt-1\",\n                    children: \"Enter one field name per line. These will be saved to the database.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2685,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2671,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2670,\n                columnNumber: 23\n              }, this), nodeForm.conditionType !== 'random' && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-3 border-t pt-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"text-sm font-medium text-gray-700\",\n                  children: \"Path Configuration\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2695,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"grid grid-cols-2 gap-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                      className: \"block text-sm font-medium text-green-700 mb-2\",\n                      children: \"\\u2705 True Path (Condition Met)\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2698,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                      value: nodeForm.truePath || '',\n                      onChange: e => setNodeForm(prev => ({\n                        ...prev,\n                        truePath: e.target.value || null\n                      })),\n                      className: \"input-field border-green-300 focus:border-green-500\",\n                      children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"\",\n                        children: \"Select Next Node\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2706,\n                        columnNumber: 31\n                      }, this), flowForm.nodes.map((node, nodeIndex) => /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: nodeIndex + 1,\n                        children: [nodeIndex + 1, \". \", node.name]\n                      }, nodeIndex, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2708,\n                        columnNumber: 33\n                      }, this))]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2701,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2697,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                      className: \"block text-sm font-medium text-red-700 mb-2\",\n                      children: \"\\u274C False Path (Condition Not Met)\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2715,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                      value: nodeForm.falsePath || '',\n                      onChange: e => setNodeForm(prev => ({\n                        ...prev,\n                        falsePath: e.target.value || null\n                      })),\n                      className: \"input-field border-red-300 focus:border-red-500\",\n                      children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"\",\n                        children: \"Select Next Node\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2723,\n                        columnNumber: 31\n                      }, this), flowForm.nodes.map((node, nodeIndex) => /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: nodeIndex + 1,\n                        children: [nodeIndex + 1, \". \", node.name]\n                      }, nodeIndex, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2725,\n                        columnNumber: 33\n                      }, this))]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2718,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2714,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2696,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs text-gray-500\",\n                  children: \"Choose different paths based on whether the condition is true or false.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2732,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2694,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2492,\n              columnNumber: 19\n            }, this), nodeForm.nodeType === 'condition' && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Condition Type\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2744,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  value: nodeForm.conditionType || 'user_response',\n                  onChange: e => setNodeForm(prev => ({\n                    ...prev,\n                    conditionType: e.target.value\n                  })),\n                  className: \"input-field\",\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"user_response\",\n                    children: \"Based on User Response\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2752,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"variable_value\",\n                    children: \"Based on Variable Value\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2753,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"time_based\",\n                    children: \"Time-based Condition\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2754,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"random\",\n                    children: \"Random Selection\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2755,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2747,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2743,\n                columnNumber: 21\n              }, this), nodeForm.conditionType === 'user_response' && /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Response Conditions\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2761,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"space-y-2\",\n                  children: [(nodeForm.responseConditions || []).map((condition, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex space-x-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"text\",\n                      value: condition.keyword || '',\n                      onChange: e => {\n                        const updated = [...(nodeForm.responseConditions || [])];\n                        updated[index] = {\n                          ...updated[index],\n                          keyword: e.target.value\n                        };\n                        setNodeForm(prev => ({\n                          ...prev,\n                          responseConditions: updated\n                        }));\n                      },\n                      placeholder: \"Keyword to match\",\n                      className: \"input-field flex-1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2767,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                      value: condition.nextNode || '',\n                      onChange: e => {\n                        const updated = [...(nodeForm.responseConditions || [])];\n                        updated[index] = {\n                          ...updated[index],\n                          nextNode: e.target.value\n                        };\n                        setNodeForm(prev => ({\n                          ...prev,\n                          responseConditions: updated\n                        }));\n                      },\n                      className: \"input-field w-40\",\n                      children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"\",\n                        children: \"Select Next Node\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2787,\n                        columnNumber: 33\n                      }, this), flowForm.nodes.map((node, nodeIndex) => /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: nodeIndex + 1,\n                        children: [nodeIndex + 1, \". \", node.name]\n                      }, nodeIndex, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2789,\n                        columnNumber: 35\n                      }, this))]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2778,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      type: \"button\",\n                      onClick: () => {\n                        const updated = (nodeForm.responseConditions || []).filter((_, i) => i !== index);\n                        setNodeForm(prev => ({\n                          ...prev,\n                          responseConditions: updated\n                        }));\n                      },\n                      className: \"px-3 py-2 text-red-600 hover:text-red-800\",\n                      children: \"\\xD7\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2794,\n                      columnNumber: 31\n                    }, this)]\n                  }, index, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2766,\n                    columnNumber: 29\n                  }, this)), /*#__PURE__*/_jsxDEV(\"button\", {\n                    type: \"button\",\n                    onClick: () => {\n                      const updated = [...(nodeForm.responseConditions || []), {\n                        keyword: '',\n                        nextNode: ''\n                      }];\n                      setNodeForm(prev => ({\n                        ...prev,\n                        responseConditions: updated\n                      }));\n                    },\n                    className: \"w-full px-3 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50\",\n                    children: \"Add Condition\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2806,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2764,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs text-gray-500 mt-1\",\n                  children: \"Define keywords and which node to go to when user response contains that keyword.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2817,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2760,\n                columnNumber: 23\n              }, this), nodeForm.conditionType === 'variable_value' && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                    children: \"Variable Name\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2826,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    value: nodeForm.conditionVariable || '',\n                    onChange: e => setNodeForm(prev => ({\n                      ...prev,\n                      conditionVariable: e.target.value\n                    })),\n                    placeholder: \"e.g., user_name, email\",\n                    className: \"input-field\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2829,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2825,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                    children: \"Condition\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2838,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                    value: nodeForm.conditionOperator || 'equals',\n                    onChange: e => setNodeForm(prev => ({\n                      ...prev,\n                      conditionOperator: e.target.value\n                    })),\n                    className: \"input-field\",\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"equals\",\n                      children: \"Equals\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2846,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"contains\",\n                      children: \"Contains\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2847,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"not_equals\",\n                      children: \"Not Equals\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2848,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"is_empty\",\n                      children: \"Is Empty\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2849,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"is_not_empty\",\n                      children: \"Is Not Empty\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2850,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2841,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2837,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                    children: \"Expected Value\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2854,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    value: nodeForm.conditionValue || '',\n                    onChange: e => setNodeForm(prev => ({\n                      ...prev,\n                      conditionValue: e.target.value\n                    })),\n                    placeholder: \"Value to compare against\",\n                    className: \"input-field\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2857,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2853,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2824,\n                columnNumber: 23\n              }, this), nodeForm.conditionType === 'random' && /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-2\",\n                  children: \"Random Paths\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2870,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"space-y-2\",\n                  children: [(nodeForm.randomPaths || []).map((path, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex space-x-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"number\",\n                      min: \"1\",\n                      max: \"100\",\n                      value: path.weight || 50,\n                      onChange: e => {\n                        const updated = [...(nodeForm.randomPaths || [])];\n                        updated[index] = {\n                          ...updated[index],\n                          weight: parseInt(e.target.value) || 50\n                        };\n                        setNodeForm(prev => ({\n                          ...prev,\n                          randomPaths: updated\n                        }));\n                      },\n                      placeholder: \"Weight %\",\n                      className: \"input-field w-20\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2876,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                      value: path.nextNode || '',\n                      onChange: e => {\n                        const updated = [...(nodeForm.randomPaths || [])];\n                        updated[index] = {\n                          ...updated[index],\n                          nextNode: e.target.value\n                        };\n                        setNodeForm(prev => ({\n                          ...prev,\n                          randomPaths: updated\n                        }));\n                      },\n                      className: \"input-field flex-1\",\n                      children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"\",\n                        children: \"Select Next Node\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2898,\n                        columnNumber: 33\n                      }, this), flowForm.nodes.map((node, nodeIndex) => /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: nodeIndex + 1,\n                        children: [nodeIndex + 1, \". \", node.name]\n                      }, nodeIndex, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2900,\n                        columnNumber: 35\n                      }, this))]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2889,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      type: \"button\",\n                      onClick: () => {\n                        const updated = (nodeForm.randomPaths || []).filter((_, i) => i !== index);\n                        setNodeForm(prev => ({\n                          ...prev,\n                          randomPaths: updated\n                        }));\n                      },\n                      className: \"px-3 py-2 text-red-600 hover:text-red-800\",\n                      children: \"\\xD7\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2905,\n                      columnNumber: 31\n                    }, this)]\n                  }, index, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2875,\n                    columnNumber: 29\n                  }, this)), /*#__PURE__*/_jsxDEV(\"button\", {\n                    type: \"button\",\n                    onClick: () => {\n                      const updated = [...(nodeForm.randomPaths || []), {\n                        weight: 50,\n                        nextNode: ''\n                      }];\n                      setNodeForm(prev => ({\n                        ...prev,\n                        randomPaths: updated\n                      }));\n                    },\n                    className: \"w-full px-3 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50\",\n                    children: \"Add Random Path\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2917,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2873,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs text-gray-500 mt-1\",\n                  children: \"Define multiple paths with weights for random selection.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2928,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2869,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2742,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Next Node (Optional)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2938,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: nodeForm.nextNodeId || '',\n                onChange: handleNodeFormNextNodeChange,\n                className: \"input-field\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Auto (Next in sequence)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2946,\n                  columnNumber: 21\n                }, this), flowForm.nodes.map((node, index) => {\n                  // Use position-based indexing (index + 1) for next node selection\n                  const nodePosition = index + 1;\n                  return /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: nodePosition,\n                    children: [nodePosition, \". \", typeof node.name === 'string' ? node.name : 'Unnamed Node']\n                  }, index, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2951,\n                    columnNumber: 25\n                  }, this);\n                })]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2941,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-gray-500 mt-1\",\n                children: \"Specify which node to go to next, or leave empty for sequential flow.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2957,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2937,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2165,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2164,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-6 border-t border-gray-200 flex-shrink-0\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                setShowNodeModal(false);\n                resetNodeForm();\n                setEditingNodeIndex(-1);\n              },\n              className: \"btn-secondary flex-1\",\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2968,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleAddNode,\n              className: \"btn-primary flex-1\",\n              disabled: !nodeForm.name.trim() || !nodeForm.message.trim(),\n              children: editingNodeIndex >= 0 ? 'Update Node' : 'Add Node'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2978,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2967,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 2966,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 2145,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 2144,\n      columnNumber: 9\n    }, this), showFlowModal && selectedFlow && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-gray-900\",\n              children: \"Flow Details\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2997,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowFlowModal(false),\n              className: \"p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg\",\n              children: /*#__PURE__*/_jsxDEV(XMarkIcon, {\n                className: \"w-5 h-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 3002,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2998,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2996,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"font-medium text-gray-900 mb-3\",\n                children: typeof selectedFlow.name === 'string' ? selectedFlow.name : String(selectedFlow.name || 'Unnamed Flow')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 3009,\n                columnNumber: 19\n              }, this), selectedFlow.description && /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-600 mb-4\",\n                children: selectedFlow.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 3011,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-2 gap-4 text-sm\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-gray-600\",\n                    children: \"Status:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 3016,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${selectedFlow.is_active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}`,\n                    children: selectedFlow.is_active ? 'Active' : 'Inactive'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 3017,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3015,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-gray-600\",\n                    children: \"Device:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 3024,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"ml-2 text-gray-900\",\n                    children: typeof selectedFlow.device_name === 'string' ? selectedFlow.device_name : String(selectedFlow.device_name || 'Unknown Device')\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 3025,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3023,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-gray-600\",\n                    children: \"Nodes:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 3028,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"ml-2 text-gray-900\",\n                    children: selectedFlow.node_count || 0\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 3029,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3027,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-gray-600\",\n                    children: \"Conversations:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 3032,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"ml-2 text-gray-900\",\n                    children: selectedFlow.conversation_count || 0\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 3033,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3031,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 3014,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 3008,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"font-medium text-gray-900 mb-2\",\n                children: \"Trigger Keywords\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 3040,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-wrap gap-2\",\n                children: (typeof selectedFlow.trigger_keywords === 'string' ? selectedFlow.trigger_keywords : String(selectedFlow.trigger_keywords || '')).split(',').map((keyword, index) => /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"inline-flex items-center px-2 py-1 rounded text-xs bg-blue-100 text-blue-800\",\n                  children: keyword.trim()\n                }, index, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 3043,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 3041,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 3039,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-sm text-gray-500\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                children: [\"Created: \", new Date(selectedFlow.created_at).toLocaleString()]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 3055,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [\"Updated: \", new Date(selectedFlow.updated_at).toLocaleString()]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 3056,\n                columnNumber: 19\n              }, this), selectedFlow.last_triggered && /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [\"Last triggered: \", new Date(selectedFlow.last_triggered).toLocaleString()]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 3058,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 3054,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 3006,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-end mt-6\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowFlowModal(false),\n              className: \"btn-secondary\",\n              children: \"Close\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 3064,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 3063,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2995,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2994,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 2993,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 1341,\n    columnNumber: 5\n  }, this);\n};\nexport default Chatbot;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "ChatBubbleOvalLeftEllipsisIcon", "PlusIcon", "MagnifyingGlassIcon", "PencilIcon", "TrashIcon", "PlayIcon", "PauseIcon", "EyeIcon", "DocumentTextIcon", "ArrowPathIcon", "TagIcon", "ClockIcon", "CheckCircleIcon", "XCircleIcon", "ExclamationTriangleIcon", "XMarkIcon", "ArrowRightIcon", "BoltIcon", "PhotoIcon", "PaperClipIcon", "UserGroupIcon", "ChartBarIcon", "RectangleStackIcon", "ListBulletIcon", "ViewColumnsIcon", "MapPinIcon", "VideoCameraIcon", "SpeakerWaveIcon", "DocumentIcon", "ChatBubbleLeftRightIcon", "ArrowDownTrayIcon", "ArrowUpTrayIcon", "useNotifications", "jsxDEV", "_jsxDEV", "TEMPLATE_TYPES", "text", "name", "description", "color", "image", "document", "contact", "poll", "buttons", "list", "mixed_buttons", "location", "video", "audio", "<PERSON><PERSON><PERSON>", "showSuccess", "showError", "showWarning", "confirm", "renderError", "setRenderError", "safeRender", "value", "context", "undefined", "console", "error", "trace", "String", "display_text", "title", "JSON", "stringify", "flows", "setFlows", "sessions", "setSessions", "templates", "setTemplates", "loading", "setLoading", "searchTerm", "setSearchTerm", "statusFilter", "setStatus<PERSON>ilter", "showCreateModal", "setShowCreateModal", "showEditModal", "setShowEditModal", "showFlowModal", "setShowFlowModal", "<PERSON><PERSON><PERSON>", "setSelectedFlow", "flowForm", "setFlowForm", "sessionId", "triggerKeywords", "keywordMatchType", "keywordCaseSensitive", "isActive", "cooldownMinutes", "nodes", "nodeForm", "setNodeForm", "message", "nodeType", "messageType", "options", "nextNodeId", "templateId", "variables", "attachmentFile", "attachmentType", "extractVariable", "conditions", "actionType", "webhookUrl", "delaySeconds", "emailRecipients", "emailSubject", "emailBody", "emailTemplate", "save<PERSON><PERSON><PERSON><PERSON>s", "apiEndpoint", "apiMethod", "apiHeaders", "apiBody", "conditionType", "responseConditions", "conditionVariable", "conditionOperator", "conditionValue", "randomPaths", "truePath", "falsePath", "showNodeModal", "setShowNodeModal", "editingNodeIndex", "setEditingNodeIndex", "loadFlows", "loadSessions", "loadTemplates", "Array", "isArray", "prev", "response", "window", "electronAPI", "database", "query", "success", "data", "whatsapp", "getSessions", "log", "connectedSessions", "filter", "session", "isConnected", "realTimeStatus", "status", "isLoggedIn", "length", "warn", "handleCreateFlow", "trim", "nodeCount", "flowResult", "flowId", "insertId", "nodeIdMap", "Map", "i", "node", "nodeResult", "attachmentData", "set", "isNaN", "actualNextNodeId", "get", "parseInt", "currentNodeId", "resetFlowForm", "handleDeleteFlow", "confirmed", "endConversationsResult", "changes", "deleteNodesResult", "result", "toggleFlowStatus", "currentStatus", "statusText", "handleEditFlow", "flow", "nodesResult", "id", "idToPositionMap", "for<PERSON>ach", "position", "map", "index", "next_node_id", "referencedPosition", "node_type", "parse", "template_id", "attachment_data", "attachment_type", "extract_variable", "session_id", "trigger_keywords", "is_active", "cooldown_minutes", "handleUpdateFlow", "handleAddNode", "reader", "FileReader", "fileData", "Promise", "resolve", "reject", "onload", "onerror", "readAsDataURL", "nodeOptions", "filteredOptions", "opt", "interactiveOptions", "option", "interaction_type", "interactionType", "action_type", "webhook_url", "delay_seconds", "email_recipients", "email_subject", "email_body", "email_template", "save_data_fields", "api_endpoint", "api_method", "api_headers", "api_body", "condition_type", "response_conditions", "condition_variable", "condition_operator", "condition_value", "random_paths", "true_path", "false_path", "newNode", "Date", "now", "updatedNodes", "resetNodeForm", "handleEditNode", "actionConfig", "conditionConfig", "optionsArray", "handleDeleteNode", "_", "handleTemplateSelect", "safeTemplates", "template", "find", "t", "variablesObj", "variable", "varName", "content", "addOption", "currentOptions", "updateOption", "updatedOptions", "removeOption", "handleSearchTermChange", "e", "target", "handleStatusFilterChange", "handleFlowFormNameChange", "handleFlowFormDescriptionChange", "handleFlowFormSessionChange", "handleFlowFormKeywordsChange", "handleFlowFormCooldownChange", "handleFlowFormActiveChange", "checked", "handleNodeFormNameChange", "handleNodeFormMessageChange", "handleNodeFormAttachmentTypeChange", "handleNodeFormAttachmentFileChange", "files", "handleNodeFormExtractVariableChange", "handleNodeFormNextNodeChange", "handleExportChatbots", "flowsResponse", "aiChatbotsResponse", "aiChatbots", "exportData", "version", "exportDate", "toISOString", "type", "bot", "api_key", "nodesResponse", "push", "dataStr", "dataBlob", "Blob", "link", "createElement", "href", "URL", "createObjectURL", "download", "split", "click", "handleCleanupOrphanedConversations", "shouldProceed", "invoke", "cleaned", "inactiveFlows", "deletedFlows", "handleImportChatbots", "showOpenDialog", "filters", "extensions", "properties", "canceled", "filePaths", "filePath", "fileResult", "fs", "readFile", "importData", "parseError", "flowCount", "aiChatbotCount", "previewMessage", "shouldImport", "flowSuccessCount", "flowErrorCount", "aiChatbotSuccessCount", "aiChatbotErrorCount", "flowData", "welcome_message", "fallback_message", "newFlowId", "nodeData", "aiChatbotData", "provider", "model", "temperature", "max_tokens", "system_prompt", "language", "session_ids", "features", "personality", "industry", "response_delay", "max_conversation_length", "enable_learning", "confidence_threshold", "totalSuccess", "totalErrors", "some", "safeFlows", "filteredFlows", "flowName", "matchesSearch", "toLowerCase", "includes", "matchesStatus", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "disabled", "placeholder", "onChange", "f", "reduce", "total", "node_count", "conversation_count", "slice", "keyword", "device_name", "rows", "Math", "random", "deviceName", "phoneNumber", "phone_number", "Boolean", "min", "htmlFor", "nodeName", "nodeMessage", "optionsToDisplay", "optIndex", "displayText", "optionsToShow", "templateType", "templateName", "typeName", "readOnly", "Object", "keys", "safeVariable", "accept", "fill", "viewBox", "fillRule", "d", "clipRule", "optionValue", "max", "join", "field", "nodeIndex", "condition", "updated", "nextNode", "path", "weight", "nodePosition", "created_at", "toLocaleString", "updated_at", "last_triggered"], "sources": ["D:/My Projects/LeadWave/src/components/modules/Chatbot.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\r\nimport {\r\n  ChatBubbleOvalLeftEllipsisIcon,\r\n  PlusIcon,\r\n  MagnifyingGlassIcon,\r\n  PencilIcon,\r\n  TrashIcon,\r\n  PlayIcon,\r\n  PauseIcon,\r\n  EyeIcon,\r\n  DocumentTextIcon,\r\n  ArrowPathIcon,\r\n  TagIcon,\r\n  ClockIcon,\r\n  CheckCircleIcon,\r\n  XCircleIcon,\r\n  ExclamationTriangleIcon,\r\n  XMarkIcon,\r\n  ArrowRightIcon,\r\n  BoltIcon,\r\n  PhotoIcon,\r\n  PaperClipIcon,\r\n  UserGroupIcon,\r\n  ChartBarIcon,\r\n  RectangleStackIcon,\r\n  ListBulletIcon,\r\n  ViewColumnsIcon,\r\n  MapPinIcon,\r\n  VideoCameraIcon,\r\n  SpeakerWaveIcon,\r\n  DocumentIcon,\r\n  ChatBubbleLeftRightIcon,\r\n  ArrowDownTrayIcon,\r\n  ArrowUpTrayIcon\r\n} from '@heroicons/react/24/outline';\r\nimport { useNotifications } from '../../contexts/NotificationContext';\r\n\r\n// Template types with their configurations (matching Templates.js)\r\nconst TEMPLATE_TYPES = {\r\n  text: {\r\n    name: 'Text Message',\r\n    description: 'Simple text message with variables',\r\n    color: 'blue'\r\n  },\r\n  image: {\r\n    name: 'Message + Image',\r\n    description: 'Text message with image attachment',\r\n    color: 'green'\r\n  },\r\n  document: {\r\n    name: 'Message + Document',\r\n    description: 'Text message with document attachment',\r\n    color: 'purple'\r\n  },\r\n  contact: {\r\n    name: 'Message + Contact',\r\n    description: 'Text message with contact card',\r\n    color: 'indigo'\r\n  },\r\n  poll: {\r\n    name: 'Message + Poll',\r\n    description: 'Interactive poll with multiple options',\r\n    color: 'yellow'\r\n  },\r\n  buttons: {\r\n    name: 'Interactive Buttons',\r\n    description: 'Message with interactive buttons',\r\n    color: 'blue'\r\n  },\r\n  // Hidden template type - functionality preserved but not shown in UI\r\n  // cta_button: {\r\n  //   name: 'CTA Button',\r\n  //   description: 'Call-to-action button with URL link',\r\n  //   color: 'orange'\r\n  // },\r\n  list: {\r\n    name: 'Interactive List',\r\n    description: 'Message with selectable list options',\r\n    color: 'green'\r\n  },\r\n\r\n  mixed_buttons: {\r\n    name: 'Mixed Interactive Buttons',\r\n    description: 'Interactive buttons with multiple types (Quick Reply, CTA URL, CTA Phone, Copy Code)',\r\n    color: 'indigo'\r\n  },\r\n  location: {\r\n    name: 'Message + Location',\r\n    description: 'Text message with location pin',\r\n    color: 'red'\r\n  },\r\n  video: {\r\n    name: 'Message + Video',\r\n    description: 'Text message with video attachment',\r\n    color: 'pink'\r\n  },\r\n  audio: {\r\n    name: 'Message + Audio',\r\n    description: 'Text message with audio attachment',\r\n    color: 'orange'\r\n  }\r\n};\r\n\r\nconst Chatbot = () => {\r\n  const { showSuccess, showError, showWarning, confirm } = useNotifications();\r\n  // Add error boundary to catch rendering errors\r\n  const [renderError, setRenderError] = useState(null);\r\n\r\n  // Debug function to safely render any value\r\n  const safeRender = (value, context = 'unknown') => {\r\n    if (value === null || value === undefined) {\r\n      return '';\r\n    }\r\n    if (typeof value === 'string' || typeof value === 'number' || typeof value === 'boolean') {\r\n      return value;\r\n    }\r\n    if (typeof value === 'object') {\r\n      console.error(`🚨 OBJECT RENDER ATTEMPT in ${context}:`, value);\r\n      console.trace('Stack trace for object render attempt');\r\n      if (value.name) return String(value.name);\r\n      if (value.display_text) return String(value.display_text);\r\n      if (value.title) return String(value.title);\r\n      if (value.text) return String(value.text);\r\n      return JSON.stringify(value);\r\n    }\r\n    return String(value);\r\n  };\r\n\r\n  const [flows, setFlows] = useState([]);\r\n  const [sessions, setSessions] = useState([]);\r\n  const [templates, setTemplates] = useState([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [searchTerm, setSearchTerm] = useState('');\r\n  const [statusFilter, setStatusFilter] = useState('all');\r\n  const [showCreateModal, setShowCreateModal] = useState(false);\r\n  const [showEditModal, setShowEditModal] = useState(false);\r\n  const [showFlowModal, setShowFlowModal] = useState(false);\r\n  const [selectedFlow, setSelectedFlow] = useState(null);\r\n  const [flowForm, setFlowForm] = useState({\r\n    name: '',\r\n    description: '',\r\n    sessionId: '',\r\n    triggerKeywords: '',\r\n    keywordMatchType: 'contains',\r\n    keywordCaseSensitive: false,\r\n    isActive: true,\r\n    cooldownMinutes: 0,\r\n    nodes: []\r\n  });\r\n  const [nodeForm, setNodeForm] = useState({\r\n    name: '',\r\n    message: '',\r\n    nodeType: 'message',\r\n    messageType: 'text', // text, template\r\n    options: [],\r\n    nextNodeId: null,\r\n    templateId: '',\r\n    variables: {},\r\n    attachmentFile: null,\r\n    attachmentType: 'image', // image, video, audio, document\r\n    extractVariable: '', // Variable name to extract from user response\r\n    conditions: [], // Conditional logic for condition nodes\r\n    // Action node fields\r\n    actionType: 'webhook',\r\n    webhookUrl: '',\r\n    delaySeconds: 5,\r\n    emailRecipients: '',\r\n    emailSubject: '',\r\n    emailBody: '', // Added email body\r\n    emailTemplate: '', // Added email template\r\n    saveDataFields: [], // Added save data fields\r\n    apiEndpoint: '', // Added API endpoint\r\n    apiMethod: 'POST', // Added API method\r\n    apiHeaders: '', // Added API headers\r\n    apiBody: '', // Added API body\r\n    // Condition node fields\r\n    conditionType: 'user_response',\r\n    responseConditions: [],\r\n    conditionVariable: '',\r\n    conditionOperator: 'equals',\r\n    conditionValue: '',\r\n    randomPaths: [],\r\n    truePath: null, // Added true path for conditions\r\n    falsePath: null // Added false path for conditions\r\n  });\r\n\r\n\r\n  const [showNodeModal, setShowNodeModal] = useState(false);\r\n  const [editingNodeIndex, setEditingNodeIndex] = useState(-1);\r\n\r\n  // Load data on component mount\r\n  useEffect(() => {\r\n    loadFlows();\r\n    loadSessions();\r\n    loadTemplates();\r\n  }, []);\r\n\r\n\r\n\r\n  // Ensure options is always an array of strings when nodeType changes\r\n  useEffect(() => {\r\n    if (nodeForm.nodeType === 'question') {\r\n      if (!Array.isArray(nodeForm.options)) {\r\n        setNodeForm(prev => ({ ...prev, options: [] }));\r\n      }\r\n    }\r\n  }, [nodeForm.nodeType]);\r\n\r\n  const loadFlows = async () => {\r\n    try {\r\n      setLoading(true);\r\n      const response = await window.electronAPI.database.query(\r\n        `SELECT cf.*, ws.device_name, COUNT(cn.id) as node_count\r\n         FROM chatbot_flows cf\r\n         LEFT JOIN whatsapp_sessions ws ON cf.session_id = ws.session_id\r\n         LEFT JOIN chatbot_nodes cn ON cf.id = cn.flow_id\r\n         GROUP BY cf.id\r\n         ORDER BY cf.created_at DESC`\r\n      );\r\n      if (response.success) {\r\n        setFlows(response.data || []);\r\n      }\r\n    } catch (error) {\r\n      console.error('Error loading chatbot flows:', error);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const loadSessions = async () => {\r\n    try {\r\n      const response = await window.electronAPI.whatsapp.getSessions();\r\n      console.log('Sessions response:', response);\r\n\r\n      if (response.success && Array.isArray(response.sessions)) {\r\n        // Filter for connected sessions - be more permissive to catch any working sessions\r\n        const connectedSessions = response.sessions.filter(session => {\r\n          const isConnected = session.realTimeStatus === 'connected' ||\r\n                             session.status === 'connected' ||\r\n                             session.isLoggedIn === true;\r\n\r\n          console.log(`Session ${typeof session.sessionId === 'string' ? session.sessionId : (typeof session.name === 'string' ? session.name : String(session.sessionId || session.name || 'unknown'))}:`, {\r\n            status: session.status,\r\n            realTimeStatus: session.realTimeStatus,\r\n            isLoggedIn: session.isLoggedIn,\r\n            isConnected\r\n          });\r\n\r\n          return isConnected;\r\n        });\r\n\r\n        console.log('All sessions:', response.sessions.length);\r\n        console.log('Connected sessions:', connectedSessions.length);\r\n        setSessions(connectedSessions);\r\n      } else {\r\n        console.warn('Invalid sessions response:', response);\r\n        setSessions([]);\r\n      }\r\n    } catch (error) {\r\n      console.error('Error loading sessions:', error);\r\n      setSessions([]);\r\n    }\r\n  };\r\n\r\n  const loadTemplates = async () => {\r\n    try {\r\n      const response = await window.electronAPI.database.query(\r\n        `SELECT id, name, content, type, attachments, buttons, list_sections,\r\n         poll_options, contact_info, location_info,\r\n         media_settings, interactive_settings, cta_data, copy_data, flow_data, variables\r\n         FROM message_templates ORDER BY name ASC`\r\n      );\r\n      if (response.success) {\r\n        setTemplates(response.data || []);\r\n      }\r\n    } catch (error) {\r\n      console.error('Error loading templates:', error);\r\n    }\r\n  };\r\n\r\n  const handleCreateFlow = async () => {\r\n    if (!flowForm.name.trim() || !flowForm.triggerKeywords.trim() || !flowForm.sessionId) {\r\n      showError('Validation Error', 'Please fill in flow name, trigger keywords, and select a WhatsApp session');\r\n      return;\r\n    }\r\n\r\n    if (flowForm.nodes.length === 0) {\r\n      showError('Validation Error', 'Please add at least one node to the flow');\r\n      return;\r\n    }\r\n\r\n    try {\r\n      console.log('🔧 Creating chatbot flow with data:', {\r\n        name: flowForm.name.trim(),\r\n        description: flowForm.description.trim() || null,\r\n        sessionId: flowForm.sessionId,\r\n        triggerKeywords: flowForm.triggerKeywords.trim(),\r\n        isActive: flowForm.isActive ? 1 : 0,\r\n        cooldownMinutes: flowForm.cooldownMinutes || 0,\r\n        nodeCount: flowForm.nodes.length\r\n      });\r\n\r\n      // Create the flow\r\n      const flowResult = await window.electronAPI.database.query(\r\n        `INSERT INTO chatbot_flows (\r\n          name, description, session_id, trigger_keywords, is_active, cooldown_minutes, created_at, updated_at\r\n        ) VALUES (?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)`,\r\n        [\r\n          flowForm.name.trim(),\r\n          flowForm.description.trim() || null,\r\n          flowForm.sessionId,\r\n          flowForm.triggerKeywords.trim(),\r\n          flowForm.isActive ? 1 : 0,\r\n          flowForm.cooldownMinutes || 0\r\n        ]\r\n      );\r\n\r\n      console.log('🔧 Flow creation result:', flowResult);\r\n\r\n      if (flowResult.success) {\r\n        const flowId = flowResult.insertId;\r\n\r\n        // Create nodes with proper ID mapping\r\n        const nodeIdMap = new Map(); // Map from array index to actual database ID\r\n\r\n        // First pass: Create all nodes without next_node_id\r\n        for (let i = 0; i < flowForm.nodes.length; i++) {\r\n          const node = flowForm.nodes[i];\r\n          const nodeResult = await window.electronAPI.database.query(\r\n            `INSERT INTO chatbot_nodes (\r\n              flow_id, name, message, node_type, options, next_node_id,\r\n              position, template_id, attachment_data, attachment_type, created_at, updated_at\r\n            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)`,\r\n            [\r\n              flowId,\r\n              node.name,\r\n              node.message,\r\n              node.nodeType,\r\n              JSON.stringify(node.options || []),\r\n              null, // Set to null initially\r\n              i + 1,\r\n              node.templateId || null,\r\n              node.attachmentData || null,\r\n              node.attachmentType || null\r\n            ]\r\n          );\r\n\r\n          if (nodeResult.success) {\r\n            nodeIdMap.set(i + 1, nodeResult.insertId); // Map position to actual DB ID\r\n          }\r\n        }\r\n\r\n        // Second pass: Update next_node_id with correct database IDs\r\n        for (let i = 0; i < flowForm.nodes.length; i++) {\r\n          const node = flowForm.nodes[i];\r\n          if (node.nextNodeId && !isNaN(node.nextNodeId) && node.nextNodeId <= flowForm.nodes.length) {\r\n            const actualNextNodeId = nodeIdMap.get(parseInt(node.nextNodeId));\r\n            if (actualNextNodeId) {\r\n              const currentNodeId = nodeIdMap.get(i + 1);\r\n              await window.electronAPI.database.query(\r\n                'UPDATE chatbot_nodes SET next_node_id = ? WHERE id = ?',\r\n                [actualNextNodeId, currentNodeId]\r\n              );\r\n            }\r\n          }\r\n        }\r\n\r\n        setShowCreateModal(false);\r\n        resetFlowForm();\r\n        await loadFlows();\r\n        showSuccess('Flow Created', 'Chatbot flow created successfully!');\r\n      } else {\r\n        showError('Creation Failed', 'Failed to create chatbot flow: ' + flowResult.error);\r\n      }\r\n    } catch (error) {\r\n      console.error('Error creating chatbot flow:', error);\r\n      showError('Creation Failed', 'Error creating chatbot flow. Please try again.');\r\n    }\r\n  };\r\n\r\n  const handleDeleteFlow = async (flowId) => {\r\n    const confirmed = await confirm('Are you sure you want to delete this chatbot flow and all its nodes?', 'Delete Chatbot Flow');\r\n    if (!confirmed) {\r\n      return;\r\n    }\r\n\r\n    try {\r\n      console.log(`🗑️ Deleting chatbot flow with ID: ${flowId}`);\r\n\r\n      // 1. End all active conversations for this flow\r\n      const endConversationsResult = await window.electronAPI.database.query(\r\n        'UPDATE chatbot_conversations SET is_active = 0, completed_at = CURRENT_TIMESTAMP WHERE flow_id = ? AND is_active = 1',\r\n        [flowId]\r\n      );\r\n      console.log(`🗑️ Ended ${endConversationsResult.changes || 0} active conversations for flow ${flowId}`);\r\n\r\n      // 2. Clear any cooldown records for this flow\r\n      await window.electronAPI.database.query(\r\n        'DELETE FROM chatbot_cooldowns WHERE flow_id = ?',\r\n        [flowId]\r\n      );\r\n\r\n      // 3. Delete nodes first\r\n      const deleteNodesResult = await window.electronAPI.database.query(\r\n        'DELETE FROM chatbot_nodes WHERE flow_id = ?',\r\n        [flowId]\r\n      );\r\n      console.log(`🗑️ Deleted ${deleteNodesResult.changes || 0} nodes for flow ${flowId}`);\r\n\r\n      // 4. Delete flow\r\n      const result = await window.electronAPI.database.query(\r\n        'DELETE FROM chatbot_flows WHERE id = ?',\r\n        [flowId]\r\n      );\r\n\r\n      if (result.success) {\r\n        console.log(`✅ Successfully deleted chatbot flow ${flowId} and cleaned up related data`);\r\n        await loadFlows();\r\n        showSuccess('Flow Deleted', 'Chatbot flow deleted successfully and all active conversations ended');\r\n      } else {\r\n        showError('Delete Failed', 'Failed to delete chatbot flow: ' + result.error);\r\n      }\r\n    } catch (error) {\r\n      console.error('Error deleting chatbot flow:', error);\r\n      showError('Delete Failed', 'Error deleting chatbot flow. Please try again.');\r\n    }\r\n  };\r\n\r\n  const toggleFlowStatus = async (flowId, currentStatus) => {\r\n    try {\r\n      // If disabling the flow, end all active conversations first\r\n      if (currentStatus) {\r\n        console.log(`🔄 Disabling flow ${flowId}, ending active conversations...`);\r\n        const endConversationsResult = await window.electronAPI.database.query(\r\n          'UPDATE chatbot_conversations SET is_active = 0, completed_at = CURRENT_TIMESTAMP WHERE flow_id = ? AND is_active = 1',\r\n          [flowId]\r\n        );\r\n        console.log(`🔄 Ended ${endConversationsResult.changes || 0} active conversations for flow ${flowId}`);\r\n      }\r\n\r\n      const result = await window.electronAPI.database.query(\r\n        'UPDATE chatbot_flows SET is_active = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',\r\n        [currentStatus ? 0 : 1, flowId]\r\n      );\r\n\r\n      if (result.success) {\r\n        await loadFlows();\r\n        const statusText = currentStatus ? 'disabled' : 'enabled';\r\n        showSuccess('Status Updated', `Flow ${statusText} successfully${currentStatus ? ' and active conversations ended' : ''}`);\r\n      } else {\r\n        showError('Update Failed', 'Failed to update flow status: ' + result.error);\r\n      }\r\n    } catch (error) {\r\n      console.error('Error updating flow status:', error);\r\n      showError('Update Failed', 'Error updating flow status. Please try again.');\r\n    }\r\n  };\r\n\r\n  const handleEditFlow = async (flow) => {\r\n    try {\r\n      // Load the flow nodes\r\n      const nodesResult = await window.electronAPI.database.query(\r\n        'SELECT * FROM chatbot_nodes WHERE flow_id = ? ORDER BY position',\r\n        [flow.id]\r\n      );\r\n\r\n      if (nodesResult.success) {\r\n        // Create a map of database ID to position for proper conversion\r\n        const idToPositionMap = new Map();\r\n        nodesResult.data.forEach(node => {\r\n          idToPositionMap.set(node.id, node.position);\r\n        });\r\n\r\n        const nodes = nodesResult.data.map((node, index) => {\r\n          // Convert next_node_id from database ID to position-based reference\r\n          let nextNodeId = null;\r\n          if (node.next_node_id) {\r\n            // Find the position of the referenced node by its database ID\r\n            const referencedPosition = idToPositionMap.get(node.next_node_id);\r\n            if (referencedPosition) {\r\n              nextNodeId = referencedPosition;\r\n            }\r\n          }\r\n\r\n          return {\r\n            id: node.id,\r\n            name: node.name,\r\n            message: node.message,\r\n            nodeType: node.node_type,\r\n            options: node.options ? JSON.parse(node.options) : [],\r\n            nextNodeId: nextNodeId,\r\n            templateId: node.template_id,\r\n            attachmentData: node.attachment_data,\r\n            attachmentType: node.attachment_type,\r\n            extractVariable: node.options && typeof JSON.parse(node.options) === 'object' && !Array.isArray(JSON.parse(node.options))\r\n              ? JSON.parse(node.options).extract_variable || ''\r\n              : '',\r\n            conditions: node.node_type === 'condition' && node.options ? JSON.parse(node.options) : []\r\n          };\r\n        });\r\n\r\n        // Populate the form with existing flow data\r\n        setFlowForm({\r\n          id: flow.id, // Add ID for editing\r\n          name: flow.name,\r\n          description: flow.description || '',\r\n          sessionId: flow.session_id,\r\n          triggerKeywords: flow.trigger_keywords,\r\n          isActive: flow.is_active,\r\n          cooldownMinutes: flow.cooldown_minutes || 0,\r\n          nodes: nodes\r\n        });\r\n\r\n        setShowEditModal(true);\r\n      } else {\r\n        showError('Load Failed', 'Failed to load flow nodes: ' + nodesResult.error);\r\n      }\r\n    } catch (error) {\r\n      console.error('Error loading flow for editing:', error);\r\n      showError('Load Failed', 'Error loading flow for editing. Please try again.');\r\n    }\r\n  };\r\n\r\n  const handleUpdateFlow = async () => {\r\n    if (!flowForm.name.trim() || !flowForm.triggerKeywords.trim() || !flowForm.sessionId) {\r\n      showError('Validation Error', 'Please fill in flow name, trigger keywords, and select a WhatsApp session');\r\n      return;\r\n    }\r\n\r\n    if (flowForm.nodes.length === 0) {\r\n      showError('Validation Error', 'Please add at least one node to the flow');\r\n      return;\r\n    }\r\n\r\n    try {\r\n      // Update the flow\r\n      const flowResult = await window.electronAPI.database.query(\r\n        `UPDATE chatbot_flows SET\r\n          name = ?, description = ?, session_id = ?, trigger_keywords = ?,\r\n          is_active = ?, cooldown_minutes = ?, updated_at = CURRENT_TIMESTAMP\r\n        WHERE id = ?`,\r\n        [\r\n          flowForm.name.trim(),\r\n          flowForm.description.trim() || null,\r\n          flowForm.sessionId,\r\n          flowForm.triggerKeywords.trim(),\r\n          flowForm.isActive ? 1 : 0,\r\n          flowForm.cooldownMinutes || 0,\r\n          flowForm.id\r\n        ]\r\n      );\r\n\r\n      if (flowResult.success) {\r\n        // End any active conversations for this flow before updating nodes\r\n        await window.electronAPI.database.query(\r\n          'UPDATE chatbot_conversations SET is_active = 0, completed_at = CURRENT_TIMESTAMP WHERE flow_id = ? AND is_active = 1',\r\n          [flowForm.id]\r\n        );\r\n\r\n        // Delete existing nodes\r\n        await window.electronAPI.database.query(\r\n          'DELETE FROM chatbot_nodes WHERE flow_id = ?',\r\n          [flowForm.id]\r\n        );\r\n\r\n        // Create updated nodes with proper ID mapping\r\n        const nodeIdMap = new Map(); // Map from array index to actual database ID\r\n\r\n        // First pass: Create all nodes without next_node_id\r\n        for (let i = 0; i < flowForm.nodes.length; i++) {\r\n          const node = flowForm.nodes[i];\r\n          const nodeResult = await window.electronAPI.database.query(\r\n            `INSERT INTO chatbot_nodes (\r\n              flow_id, name, message, node_type, options, next_node_id,\r\n              position, template_id, attachment_data, attachment_type, created_at, updated_at\r\n            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)`,\r\n            [\r\n              flowForm.id,\r\n              node.name,\r\n              node.message,\r\n              node.nodeType,\r\n              JSON.stringify(node.options || []),\r\n              null, // Set to null initially\r\n              i + 1,\r\n              node.templateId || null,\r\n              node.attachmentData || null,\r\n              node.attachmentType || null\r\n            ]\r\n          );\r\n\r\n          if (nodeResult.success) {\r\n            nodeIdMap.set(i + 1, nodeResult.insertId); // Map position to actual DB ID\r\n          }\r\n        }\r\n\r\n        // Second pass: Update next_node_id with correct database IDs\r\n        for (let i = 0; i < flowForm.nodes.length; i++) {\r\n          const node = flowForm.nodes[i];\r\n          if (node.nextNodeId && !isNaN(node.nextNodeId) && node.nextNodeId <= flowForm.nodes.length) {\r\n            const actualNextNodeId = nodeIdMap.get(parseInt(node.nextNodeId));\r\n            if (actualNextNodeId) {\r\n              const currentNodeId = nodeIdMap.get(i + 1);\r\n              await window.electronAPI.database.query(\r\n                'UPDATE chatbot_nodes SET next_node_id = ? WHERE id = ?',\r\n                [actualNextNodeId, currentNodeId]\r\n              );\r\n            }\r\n          }\r\n        }\r\n\r\n        showSuccess('Flow Updated', 'Chatbot flow updated successfully!');\r\n        setShowEditModal(false);\r\n        resetFlowForm();\r\n        await loadFlows();\r\n      } else {\r\n        showError('Update Failed', 'Failed to update chatbot flow: ' + flowResult.error);\r\n      }\r\n    } catch (error) {\r\n      console.error('Error updating chatbot flow:', error);\r\n      showError('Update Failed', 'Error updating chatbot flow. Please try again.');\r\n    }\r\n  };\r\n\r\n  const handleAddNode = async () => {\r\n    if (!nodeForm.name.trim() || !nodeForm.message.trim()) {\r\n      showError('Validation Error', 'Please fill in node name and message');\r\n      return;\r\n    }\r\n\r\n    let attachmentData = null;\r\n    let attachmentType = null;\r\n\r\n    // Process attachment if present\r\n    if (nodeForm.attachmentFile) {\r\n      try {\r\n        // Convert file to base64 for storage\r\n        const reader = new FileReader();\r\n        const fileData = await new Promise((resolve, reject) => {\r\n          reader.onload = () => resolve(reader.result);\r\n          reader.onerror = reject;\r\n          reader.readAsDataURL(nodeForm.attachmentFile);\r\n        });\r\n\r\n        attachmentData = fileData;\r\n        attachmentType = nodeForm.attachmentType;\r\n      } catch (error) {\r\n        showError('Attachment Error', 'Error processing attachment: ' + error.message);\r\n        return;\r\n      }\r\n    }\r\n\r\n    // Prepare options based on node type\r\n    let nodeOptions = {};\r\n    if (nodeForm.nodeType === 'question') {\r\n      // For question nodes, convert string options to Baileys interactive format\r\n      const filteredOptions = nodeForm.options.filter(opt => opt.trim());\r\n      const interactiveOptions = filteredOptions.map((option, index) => ({\r\n        display_text: option.trim(),\r\n        id: `option_${index + 1}`,\r\n        title: option.trim()\r\n      }));\r\n\r\n      nodeOptions = {\r\n        options: interactiveOptions,\r\n        extract_variable: nodeForm.extractVariable || null,\r\n        interaction_type: nodeForm.interactionType || 'buttons'\r\n      };\r\n    } else if (nodeForm.nodeType === 'action') {\r\n      // For action nodes, store action configuration\r\n      nodeOptions = {\r\n        action_type: nodeForm.actionType,\r\n        webhook_url: nodeForm.webhookUrl || null,\r\n        delay_seconds: nodeForm.delaySeconds || 5,\r\n        email_recipients: nodeForm.emailRecipients || null,\r\n        email_subject: nodeForm.emailSubject || null,\r\n        email_body: nodeForm.emailBody || null,\r\n        email_template: nodeForm.emailTemplate || null,\r\n        save_data_fields: nodeForm.saveDataFields || [],\r\n        api_endpoint: nodeForm.apiEndpoint || null,\r\n        api_method: nodeForm.apiMethod || 'POST',\r\n        api_headers: nodeForm.apiHeaders || null,\r\n        api_body: nodeForm.apiBody || null\r\n      };\r\n    } else if (nodeForm.nodeType === 'condition') {\r\n      // For condition nodes, store condition configuration\r\n      nodeOptions = {\r\n        condition_type: nodeForm.conditionType,\r\n        response_conditions: nodeForm.responseConditions || [],\r\n        condition_variable: nodeForm.conditionVariable || null,\r\n        condition_operator: nodeForm.conditionOperator || 'equals',\r\n        condition_value: nodeForm.conditionValue || null,\r\n        random_paths: nodeForm.randomPaths || [],\r\n        true_path: nodeForm.truePath || null,\r\n        false_path: nodeForm.falsePath || null\r\n      };\r\n    } else {\r\n      nodeOptions = nodeForm.options.filter(opt => opt.trim());\r\n    }\r\n\r\n    const newNode = {\r\n      id: Date.now(), // Temporary ID for frontend\r\n      name: nodeForm.name.trim(),\r\n      message: nodeForm.message.trim(),\r\n      nodeType: nodeForm.nodeType,\r\n      options: nodeOptions,\r\n      nextNodeId: nodeForm.nextNodeId,\r\n      templateId: nodeForm.templateId || null,\r\n      attachmentData: attachmentData,\r\n      attachmentType: attachmentType,\r\n      extractVariable: nodeForm.extractVariable || null,\r\n      conditions: nodeForm.conditions || []\r\n    };\r\n\r\n    if (editingNodeIndex >= 0) {\r\n      // Edit existing node\r\n      const updatedNodes = [...flowForm.nodes];\r\n      updatedNodes[editingNodeIndex] = newNode;\r\n      setFlowForm(prev => ({ ...prev, nodes: updatedNodes }));\r\n      setEditingNodeIndex(-1);\r\n    } else {\r\n      // Add new node\r\n      setFlowForm(prev => ({ ...prev, nodes: [...prev.nodes, newNode] }));\r\n    }\r\n\r\n    setShowNodeModal(false);\r\n    resetNodeForm();\r\n  };\r\n\r\n  const handleEditNode = (index) => {\r\n    const node = flowForm.nodes[index];\r\n\r\n    // Handle different node types and their configurations\r\n    let nodeOptions = [];\r\n    let extractVariable = '';\r\n    let interactionType = 'buttons';\r\n    let actionConfig = {};\r\n    let conditionConfig = {};\r\n\r\n    if (node.nodeType === 'question') {\r\n      if (Array.isArray(node.options)) {\r\n        // Old format: just an array of strings\r\n        nodeOptions = node.options.map(option =>\r\n          typeof option === 'string' ? option : (typeof option === 'object' && option ? (option.display_text || option.title || option.name || option.text || '') : '')\r\n        );\r\n      } else if (typeof node.options === 'object' && node.options) {\r\n        // New format: object with options array and extract_variable\r\n        const optionsArray = node.options.options || [];\r\n        nodeOptions = optionsArray.map(option =>\r\n          typeof option === 'string' ? option : (typeof option === 'object' && option ? (option.display_text || option.title || option.name || option.text || '') : '')\r\n        );\r\n        extractVariable = node.options.extract_variable || '';\r\n        interactionType = node.options.interaction_type || 'buttons';\r\n      }\r\n    } else if (node.nodeType === 'action') {\r\n      // Load action configuration\r\n      if (typeof node.options === 'object' && node.options) {\r\n        actionConfig = {\r\n          actionType: node.options.action_type || 'webhook',\r\n          webhookUrl: node.options.webhook_url || '',\r\n          delaySeconds: node.options.delay_seconds || 5,\r\n          emailRecipients: node.options.email_recipients || '',\r\n          emailSubject: node.options.email_subject || '',\r\n          emailBody: node.options.email_body || '',\r\n          emailTemplate: node.options.email_template || '',\r\n          saveDataFields: node.options.save_data_fields || [],\r\n          apiEndpoint: node.options.api_endpoint || '',\r\n          apiMethod: node.options.api_method || 'POST',\r\n          apiHeaders: node.options.api_headers || '',\r\n          apiBody: node.options.api_body || ''\r\n        };\r\n      }\r\n    } else if (node.nodeType === 'condition') {\r\n      // Load condition configuration\r\n      if (typeof node.options === 'object' && node.options) {\r\n        conditionConfig = {\r\n          conditionType: node.options.condition_type || 'user_response',\r\n          responseConditions: node.options.response_conditions || [],\r\n          conditionVariable: node.options.condition_variable || '',\r\n          conditionOperator: node.options.condition_operator || 'equals',\r\n          conditionValue: node.options.condition_value || '',\r\n          randomPaths: node.options.random_paths || [],\r\n          truePath: node.options.true_path || null,\r\n          falsePath: node.options.false_path || null\r\n        };\r\n      }\r\n    } else {\r\n      // Message node or other types\r\n      if (Array.isArray(node.options)) {\r\n        nodeOptions = node.options;\r\n      }\r\n    }\r\n\r\n    setNodeForm({\r\n      name: node.name,\r\n      message: node.message,\r\n      nodeType: node.nodeType,\r\n      options: nodeOptions,\r\n      nextNodeId: node.nextNodeId,\r\n      templateId: node.templateId || '',\r\n      extractVariable: extractVariable,\r\n      interactionType: interactionType,\r\n      messageType: 'text',\r\n      variables: {},\r\n      attachmentFile: null,\r\n      attachmentType: 'image',\r\n      conditions: [],\r\n      // Action node fields\r\n      actionType: actionConfig.actionType || 'webhook',\r\n      webhookUrl: actionConfig.webhookUrl || '',\r\n      delaySeconds: actionConfig.delaySeconds || 5,\r\n      emailRecipients: actionConfig.emailRecipients || '',\r\n      emailSubject: actionConfig.emailSubject || '',\r\n      emailBody: actionConfig.emailBody || '',\r\n      emailTemplate: actionConfig.emailTemplate || '',\r\n      saveDataFields: actionConfig.saveDataFields || [],\r\n      apiEndpoint: actionConfig.apiEndpoint || '',\r\n      apiMethod: actionConfig.apiMethod || 'POST',\r\n      apiHeaders: actionConfig.apiHeaders || '',\r\n      apiBody: actionConfig.apiBody || '',\r\n      // Condition node fields\r\n      conditionType: conditionConfig.conditionType || 'user_response',\r\n      responseConditions: conditionConfig.responseConditions || [],\r\n      conditionVariable: conditionConfig.conditionVariable || '',\r\n      conditionOperator: conditionConfig.conditionOperator || 'equals',\r\n      conditionValue: conditionConfig.conditionValue || '',\r\n      randomPaths: conditionConfig.randomPaths || [],\r\n      truePath: conditionConfig.truePath || null,\r\n      falsePath: conditionConfig.falsePath || null\r\n    });\r\n    setEditingNodeIndex(index);\r\n    setShowNodeModal(true);\r\n  };\r\n\r\n  const handleDeleteNode = async (index) => {\r\n    const confirmed = await confirm('Are you sure you want to delete this node?', 'Delete Node');\r\n    if (confirmed) {\r\n      const updatedNodes = flowForm.nodes.filter((_, i) => i !== index);\r\n      setFlowForm(prev => ({ ...prev, nodes: updatedNodes }));\r\n    }\r\n  };\r\n\r\n  const handleTemplateSelect = (templateId) => {\r\n    const safeTemplates = Array.isArray(templates) ? templates : [];\r\n    const template = safeTemplates.find(t => t.id === parseInt(templateId));\r\n    if (template) {\r\n      // Parse template variables\r\n      let variables = [];\r\n      try {\r\n        variables = JSON.parse(template.variables || '[]');\r\n        if (!Array.isArray(variables)) {\r\n          variables = [];\r\n        }\r\n      } catch (error) {\r\n        console.error('Error parsing template variables:', error);\r\n        variables = [];\r\n      }\r\n\r\n      const variablesObj = {};\r\n      variables.forEach(variable => {\r\n        // Ensure variable is a string\r\n        const varName = typeof variable === 'string' ? variable : String(variable || '');\r\n        if (varName) {\r\n          variablesObj[varName] = `{{${varName}}}`;\r\n        }\r\n      });\r\n\r\n      setNodeForm(prev => ({\r\n        ...prev,\r\n        templateId: templateId,\r\n        message: template.content || '',\r\n        variables: variablesObj\r\n      }));\r\n    } else {\r\n      setNodeForm(prev => ({\r\n        ...prev,\r\n        templateId: '',\r\n        message: '',\r\n        variables: {}\r\n      }));\r\n    }\r\n  };\r\n\r\n  const resetFlowForm = () => {\r\n    setFlowForm({\r\n      id: null, // Add ID field for editing\r\n      name: '',\r\n      description: '',\r\n      sessionId: '',\r\n      triggerKeywords: '',\r\n      isActive: true,\r\n      cooldownMinutes: 0,\r\n      nodes: []\r\n    });\r\n  };\r\n\r\n  const resetNodeForm = () => {\r\n    setNodeForm({\r\n      name: '',\r\n      message: '',\r\n      nodeType: 'message',\r\n      messageType: 'text',\r\n      options: [],\r\n      nextNodeId: null,\r\n      templateId: '',\r\n      variables: {},\r\n      attachmentFile: null,\r\n      attachmentType: 'image',\r\n      extractVariable: '',\r\n      interactionType: 'buttons', // 'buttons' or 'text'\r\n      conditions: [],\r\n      // Action node fields\r\n      actionType: 'webhook',\r\n      webhookUrl: '',\r\n      delaySeconds: 5,\r\n      emailRecipients: '',\r\n      emailSubject: '',\r\n      emailBody: '',\r\n      emailTemplate: '',\r\n      saveDataFields: [],\r\n      apiEndpoint: '',\r\n      apiMethod: 'POST',\r\n      apiHeaders: '',\r\n      apiBody: '',\r\n      // Condition node fields\r\n      conditionType: 'user_response',\r\n      responseConditions: [],\r\n      conditionVariable: '',\r\n      conditionOperator: 'equals',\r\n      conditionValue: '',\r\n      randomPaths: [],\r\n      truePath: null,\r\n      falsePath: null\r\n    });\r\n  };\r\n\r\n  const addOption = () => {\r\n    const currentOptions = Array.isArray(nodeForm.options) ? nodeForm.options : [];\r\n    setNodeForm(prev => ({ ...prev, options: [...currentOptions, ''] }));\r\n  };\r\n\r\n  const updateOption = (index, value) => {\r\n    const updatedOptions = [...(Array.isArray(nodeForm.options) ? nodeForm.options : [])];\r\n    updatedOptions[index] = String(value || ''); // Ensure it's always a string\r\n    setNodeForm(prev => ({ ...prev, options: updatedOptions }));\r\n  };\r\n\r\n  const removeOption = (index) => {\r\n    const currentOptions = Array.isArray(nodeForm.options) ? nodeForm.options : [];\r\n    const updatedOptions = currentOptions.filter((_, i) => i !== index);\r\n    setNodeForm(prev => ({ ...prev, options: updatedOptions }));\r\n  };\r\n\r\n  // Optimized onChange handlers to prevent re-renders\r\n  const handleSearchTermChange = useCallback((e) => {\r\n    setSearchTerm(e.target.value);\r\n  }, []);\r\n\r\n  const handleStatusFilterChange = useCallback((e) => {\r\n    setStatusFilter(e.target.value);\r\n  }, []);\r\n\r\n  const handleFlowFormNameChange = useCallback((e) => {\r\n    setFlowForm(prev => ({ ...prev, name: e.target.value }));\r\n  }, []);\r\n\r\n  const handleFlowFormDescriptionChange = useCallback((e) => {\r\n    setFlowForm(prev => ({ ...prev, description: e.target.value }));\r\n  }, []);\r\n\r\n  const handleFlowFormSessionChange = useCallback((e) => {\r\n    setFlowForm(prev => ({ ...prev, sessionId: e.target.value }));\r\n  }, []);\r\n\r\n  const handleFlowFormKeywordsChange = useCallback((e) => {\r\n    setFlowForm(prev => ({ ...prev, triggerKeywords: e.target.value }));\r\n  }, []);\r\n\r\n  const handleFlowFormCooldownChange = useCallback((e) => {\r\n    setFlowForm(prev => ({ ...prev, cooldownMinutes: parseInt(e.target.value) || 0 }));\r\n  }, []);\r\n\r\n  const handleFlowFormActiveChange = useCallback((e) => {\r\n    setFlowForm(prev => ({ ...prev, isActive: e.target.checked }));\r\n  }, []);\r\n\r\n  const handleNodeFormNameChange = useCallback((e) => {\r\n    setNodeForm(prev => ({ ...prev, name: e.target.value }));\r\n  }, []);\r\n\r\n  const handleNodeFormMessageChange = useCallback((e) => {\r\n    setNodeForm(prev => ({ ...prev, message: e.target.value }));\r\n  }, []);\r\n\r\n  const handleNodeFormAttachmentTypeChange = useCallback((e) => {\r\n    setNodeForm(prev => ({ ...prev, attachmentType: e.target.value }));\r\n  }, []);\r\n\r\n  const handleNodeFormAttachmentFileChange = useCallback((e) => {\r\n    setNodeForm(prev => ({ ...prev, attachmentFile: e.target.files[0] }));\r\n  }, []);\r\n\r\n  const handleNodeFormExtractVariableChange = useCallback((e) => {\r\n    setNodeForm(prev => ({ ...prev, extractVariable: e.target.value }));\r\n  }, []);\r\n\r\n  const handleNodeFormNextNodeChange = useCallback((e) => {\r\n    setNodeForm(prev => ({ ...prev, nextNodeId: e.target.value ? parseInt(e.target.value) : null }));\r\n  }, []);\r\n\r\n  // Export chatbots functionality\r\n  const handleExportChatbots = async () => {\r\n    try {\r\n      // Get all chatbot flows with their nodes\r\n      const flowsResponse = await window.electronAPI.database.query(\r\n        'SELECT * FROM chatbot_flows ORDER BY created_at DESC'\r\n      );\r\n\r\n      if (!flowsResponse.success) {\r\n        showError('Export Failed', 'Failed to fetch chatbot flows: ' + flowsResponse.error);\r\n        return;\r\n      }\r\n\r\n      const flows = flowsResponse.data || [];\r\n\r\n      // Get AI chatbots\r\n      const aiChatbotsResponse = await window.electronAPI.database.query(\r\n        'SELECT * FROM ai_chatbots ORDER BY created_at DESC'\r\n      );\r\n\r\n      const aiChatbots = aiChatbotsResponse.success ? (aiChatbotsResponse.data || []) : [];\r\n\r\n      const exportData = {\r\n        version: '1.0',\r\n        exportDate: new Date().toISOString(),\r\n        type: 'chatbot_complete_export',\r\n        flows: [],\r\n        aiChatbots: aiChatbots.map(bot => ({\r\n          ...bot,\r\n          // Remove sensitive data\r\n          api_key: '[REDACTED]'\r\n        }))\r\n      };\r\n\r\n      // Get nodes for each flow\r\n      for (const flow of flows) {\r\n        const nodesResponse = await window.electronAPI.database.query(\r\n          'SELECT * FROM chatbot_nodes WHERE flow_id = ? ORDER BY position ASC',\r\n          [flow.id]\r\n        );\r\n\r\n        const nodes = nodesResponse.success ? (nodesResponse.data || []) : [];\r\n\r\n        exportData.flows.push({\r\n          ...flow,\r\n          nodes: nodes\r\n        });\r\n      }\r\n\r\n      // Create and download the file\r\n      const dataStr = JSON.stringify(exportData, null, 2);\r\n      const dataBlob = new Blob([dataStr], { type: 'application/json' });\r\n\r\n      const link = document.createElement('a');\r\n      link.href = URL.createObjectURL(dataBlob);\r\n      link.download = `chatbots-complete-${new Date().toISOString().split('T')[0]}.json`;\r\n      link.click();\r\n\r\n      showSuccess(\r\n        'Export Complete',\r\n        `Successfully exported ${flows.length} chatbot flows and ${aiChatbots.length} AI chatbots!`\r\n      );\r\n    } catch (error) {\r\n      console.error('Error exporting chatbots:', error);\r\n      showError('Export Failed', 'Error exporting chatbots. Please try again.');\r\n    }\r\n  };\r\n\r\n  // Clean up orphaned conversations\r\n  const handleCleanupOrphanedConversations = async () => {\r\n    try {\r\n      const shouldProceed = await confirm(\r\n        'This will end all active conversations for disabled or deleted chatbot flows. This can help fix issues with stuck conversations. Continue?',\r\n        'Cleanup Orphaned Conversations'\r\n      );\r\n\r\n      if (!shouldProceed) {\r\n        return;\r\n      }\r\n\r\n      const result = await window.electronAPI.invoke('chatbot:cleanup-orphaned-conversations');\r\n\r\n      if (result.success) {\r\n        showSuccess(\r\n          'Cleanup Complete',\r\n          `Successfully cleaned up ${result.cleaned} orphaned conversations (${result.inactiveFlows} from inactive flows, ${result.deletedFlows} from deleted flows)`\r\n        );\r\n      } else {\r\n        showError('Cleanup Failed', 'Failed to cleanup orphaned conversations: ' + result.error);\r\n      }\r\n    } catch (error) {\r\n      console.error('Error cleaning up orphaned conversations:', error);\r\n      showError('Cleanup Failed', 'Error cleaning up orphaned conversations. Please try again.');\r\n    }\r\n  };\r\n\r\n  // Import chatbots functionality\r\n  const handleImportChatbots = async () => {\r\n    try {\r\n      // Show in-app confirmation dialog\r\n      const shouldProceed = await confirm(\r\n        'This will import chatbot flows and AI chatbots from a JSON file. Existing data will not be affected. Continue?',\r\n        'Import Chatbot Data'\r\n      );\r\n\r\n      if (!shouldProceed) {\r\n        return;\r\n      }\r\n\r\n      const result = await window.electronAPI.showOpenDialog({\r\n        title: 'Import Chatbot Data',\r\n        filters: [\r\n          { name: 'JSON Files', extensions: ['json'] },\r\n          { name: 'All Files', extensions: ['*'] }\r\n        ],\r\n        properties: ['openFile']\r\n      });\r\n\r\n      if (result.canceled || !result.filePaths || result.filePaths.length === 0) {\r\n        return;\r\n      }\r\n\r\n      const filePath = result.filePaths[0];\r\n\r\n      // Read file content using the exposed fs API\r\n      const fileResult = await window.electronAPI.fs.readFile(filePath);\r\n      if (!fileResult.success) {\r\n        showError('Import Failed', 'Failed to read file: ' + fileResult.error);\r\n        return;\r\n      }\r\n\r\n      let importData;\r\n      try {\r\n        importData = JSON.parse(fileResult.data);\r\n      } catch (parseError) {\r\n        showError('Import Failed', 'Invalid JSON file format. Please check the file and try again.');\r\n        return;\r\n      }\r\n\r\n      // Validate import data\r\n      if (!importData.flows && !importData.aiChatbots) {\r\n        showError('Import Failed', 'Invalid file format. Expected chatbot data.');\r\n        return;\r\n      }\r\n\r\n      // Show preview of what will be imported\r\n      const flowCount = importData.flows ? importData.flows.length : 0;\r\n      const aiChatbotCount = importData.aiChatbots ? importData.aiChatbots.length : 0;\r\n\r\n      let previewMessage = 'Import Preview:\\n\\n';\r\n      if (flowCount > 0) {\r\n        previewMessage += `• ${flowCount} chatbot flow(s)\\n`;\r\n      }\r\n      if (aiChatbotCount > 0) {\r\n        previewMessage += `• ${aiChatbotCount} AI chatbot(s)\\n`;\r\n      }\r\n      previewMessage += '\\nProceed with import?';\r\n\r\n      const shouldImport = await confirm(previewMessage, 'Confirm Import');\r\n\r\n      if (!shouldImport) {\r\n        return;\r\n      }\r\n\r\n      let flowSuccessCount = 0;\r\n      let flowErrorCount = 0;\r\n      let aiChatbotSuccessCount = 0;\r\n      let aiChatbotErrorCount = 0;\r\n\r\n      // Import chatbot flows\r\n      if (importData.flows && Array.isArray(importData.flows)) {\r\n        for (const flowData of importData.flows) {\r\n          try {\r\n            // Create the flow (excluding id, created_at, updated_at)\r\n            const flowResult = await window.electronAPI.database.query(\r\n              `INSERT INTO chatbot_flows (\r\n                session_id, name, description, trigger_keywords, is_active,\r\n                welcome_message, fallback_message, cooldown_minutes,\r\n                conversation_count, last_triggered, created_at, updated_at\r\n              ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)`,\r\n              [\r\n                flowData.session_id || '',\r\n                flowData.name || 'Imported Flow',\r\n                flowData.description || null,\r\n                flowData.trigger_keywords || '',\r\n                flowData.is_active !== undefined ? flowData.is_active : 1,\r\n                flowData.welcome_message || null,\r\n                flowData.fallback_message || null,\r\n                flowData.cooldown_minutes || 0,\r\n                0, // Reset conversation count\r\n                null // Reset last triggered\r\n              ]\r\n            );\r\n\r\n            if (flowResult.success && flowData.nodes && Array.isArray(flowData.nodes)) {\r\n              const newFlowId = flowResult.insertId;\r\n\r\n              // Import nodes for this flow\r\n              for (const nodeData of flowData.nodes) {\r\n                await window.electronAPI.database.query(\r\n                  `INSERT INTO chatbot_nodes (\r\n                    flow_id, name, message, node_type, options, next_node_id,\r\n                    position, template_id, attachment_data, attachment_type,\r\n                    created_at, updated_at\r\n                  ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)`,\r\n                  [\r\n                    newFlowId,\r\n                    nodeData.name || 'Imported Node',\r\n                    nodeData.message || '',\r\n                    nodeData.node_type || 'message',\r\n                    nodeData.options || null,\r\n                    nodeData.next_node_id || null,\r\n                    nodeData.position || 0,\r\n                    nodeData.template_id || null,\r\n                    nodeData.attachment_data || null,\r\n                    nodeData.attachment_type || null\r\n                  ]\r\n                );\r\n              }\r\n            }\r\n\r\n            flowSuccessCount++;\r\n          } catch (error) {\r\n            console.error('Error importing flow:', error);\r\n            flowErrorCount++;\r\n          }\r\n        }\r\n      }\r\n\r\n      // Import AI chatbots (note: API keys will need to be manually configured)\r\n      if (importData.aiChatbots && Array.isArray(importData.aiChatbots)) {\r\n        for (const aiChatbotData of importData.aiChatbots) {\r\n          try {\r\n            // Skip if API key is redacted and prompt user\r\n            if (aiChatbotData.api_key === '[REDACTED]') {\r\n              console.warn('Skipping AI chatbot import - API key was redacted for security');\r\n              continue;\r\n            }\r\n\r\n            await window.electronAPI.database.query(\r\n              `INSERT INTO ai_chatbots (\r\n                name, description, provider, api_key, model, temperature, max_tokens,\r\n                system_prompt, language, is_active, session_ids, features,\r\n                personality, industry, response_delay, fallback_message,\r\n                max_conversation_length, enable_learning, confidence_threshold,\r\n                created_at, updated_at\r\n              ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)`,\r\n              [\r\n                (aiChatbotData.name || 'Imported AI Chatbot') + ' (Imported)',\r\n                aiChatbotData.description || null,\r\n                aiChatbotData.provider || 'openai',\r\n                aiChatbotData.api_key || '',\r\n                aiChatbotData.model || 'gpt-3.5-turbo',\r\n                aiChatbotData.temperature || 0.7,\r\n                aiChatbotData.max_tokens || 1000,\r\n                aiChatbotData.system_prompt || null,\r\n                aiChatbotData.language || 'en',\r\n                0, // Set as inactive by default for security\r\n                aiChatbotData.session_ids || null,\r\n                aiChatbotData.features || null,\r\n                aiChatbotData.personality || 'professional',\r\n                aiChatbotData.industry || 'general',\r\n                aiChatbotData.response_delay || 1000,\r\n                aiChatbotData.fallback_message || null,\r\n                aiChatbotData.max_conversation_length || 50,\r\n                aiChatbotData.enable_learning !== undefined ? aiChatbotData.enable_learning : 1,\r\n                aiChatbotData.confidence_threshold || 0.7\r\n              ]\r\n            );\r\n\r\n            aiChatbotSuccessCount++;\r\n          } catch (error) {\r\n            console.error('Error importing AI chatbot:', error);\r\n            aiChatbotErrorCount++;\r\n          }\r\n        }\r\n      }\r\n\r\n      // Reload flows\r\n      await loadFlows();\r\n\r\n      const totalSuccess = flowSuccessCount + aiChatbotSuccessCount;\r\n      const totalErrors = flowErrorCount + aiChatbotErrorCount;\r\n\r\n      let message = 'Import completed!\\n';\r\n      if (flowSuccessCount > 0) {\r\n        message += `Chatbot flows imported: ${flowSuccessCount}\\n`;\r\n      }\r\n      if (aiChatbotSuccessCount > 0) {\r\n        message += `AI chatbots imported: ${aiChatbotSuccessCount}\\n`;\r\n      }\r\n      if (totalErrors > 0) {\r\n        message += `Errors: ${totalErrors}\\n`;\r\n      }\r\n      if (importData.aiChatbots && importData.aiChatbots.some(bot => bot.api_key === '[REDACTED]')) {\r\n        message += '\\nNote: AI chatbots with redacted API keys were skipped. Please configure API keys manually.';\r\n      }\r\n\r\n      showSuccess('Import Complete', message);\r\n\r\n    } catch (error) {\r\n      console.error('Error importing chatbots:', error);\r\n      showError('Import Failed', 'Error importing chatbots. Please check the file format and try again.');\r\n    }\r\n  };\r\n\r\n  // Filter flows - ensure flows is always an array\r\n  const safeFlows = Array.isArray(flows) ? flows : [];\r\n  const filteredFlows = safeFlows.filter(flow => {\r\n    const flowName = typeof flow.name === 'string' ? flow.name : String(flow.name || '');\r\n    const triggerKeywords = typeof flow.trigger_keywords === 'string' ? flow.trigger_keywords : String(flow.trigger_keywords || '');\r\n    const matchesSearch = flowName.toLowerCase().includes(searchTerm.toLowerCase()) ||\r\n                         triggerKeywords.toLowerCase().includes(searchTerm.toLowerCase());\r\n    const matchesStatus = statusFilter === 'all' ||\r\n                         (statusFilter === 'active' && flow.is_active) ||\r\n                         (statusFilter === 'inactive' && !flow.is_active);\r\n    return matchesSearch && matchesStatus;\r\n  });\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"flex items-center justify-center h-64\">\r\n        <div className=\"flex items-center space-x-2\">\r\n          <div className=\"w-6 h-6 border-2 border-blue-500 border-t-transparent rounded-full animate-spin\"></div>\r\n          <span className=\"text-gray-600\">Loading chatbot flows...</span>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* Header */}\r\n      <div className=\"flex items-center justify-between\">\r\n        <div>\r\n          <h1 className=\"text-2xl font-bold text-gray-900\">Chatbot</h1>\r\n          <p className=\"text-gray-600\">Create intelligent conversational flows</p>\r\n        </div>\r\n        <div className=\"flex items-center space-x-2\">\r\n          <button\r\n            onClick={handleExportChatbots}\r\n            className=\"btn-secondary flex items-center space-x-2\"\r\n            title=\"Export Chatbot Flows\"\r\n          >\r\n            <ArrowDownTrayIcon className=\"w-5 h-5\" />\r\n            <span>Export</span>\r\n          </button>\r\n          <button\r\n            onClick={handleImportChatbots}\r\n            className=\"btn-secondary flex items-center space-x-2\"\r\n            title=\"Import Chatbot Flows\"\r\n          >\r\n            <ArrowUpTrayIcon className=\"w-5 h-5\" />\r\n            <span>Import</span>\r\n          </button>\r\n          <button\r\n            onClick={handleCleanupOrphanedConversations}\r\n            className=\"btn-secondary flex items-center space-x-2\"\r\n            title=\"Clean up orphaned conversations from disabled/deleted flows\"\r\n          >\r\n            <TrashIcon className=\"w-5 h-5\" />\r\n            <span>Cleanup</span>\r\n          </button>\r\n          <button\r\n            onClick={loadSessions}\r\n            className=\"btn-secondary flex items-center space-x-2\"\r\n            title=\"Refresh Sessions\"\r\n          >\r\n            <span>🔄</span>\r\n            <span>Refresh</span>\r\n          </button>\r\n          <button\r\n            onClick={() => setShowCreateModal(true)}\r\n            className=\"btn-primary flex items-center space-x-2\"\r\n            disabled={sessions.length === 0}\r\n          >\r\n            <PlusIcon className=\"w-5 h-5\" />\r\n            <span>Create Flow</span>\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Filters */}\r\n      <div className=\"flex flex-col sm:flex-row gap-4\">\r\n        {/* Search */}\r\n        <div className=\"relative flex-1\">\r\n          <MagnifyingGlassIcon className=\"w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\" />\r\n          <input\r\n            type=\"text\"\r\n            placeholder=\"Search flows...\"\r\n            value={searchTerm}\r\n            onChange={handleSearchTermChange}\r\n            className=\"input-field pl-10\"\r\n          />\r\n        </div>\r\n\r\n        {/* Status Filter */}\r\n        <select\r\n          value={statusFilter}\r\n          onChange={handleStatusFilterChange}\r\n          className=\"input-field sm:w-48\"\r\n        >\r\n          <option value=\"all\">All Flows</option>\r\n          <option value=\"active\">Active</option>\r\n          <option value=\"inactive\">Inactive</option>\r\n        </select>\r\n      </div>\r\n\r\n      {/* Stats */}\r\n      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6\">\r\n        <div className=\"card\">\r\n          <div className=\"flex items-center\">\r\n            <div className=\"flex-shrink-0\">\r\n              <ChatBubbleOvalLeftEllipsisIcon className=\"h-8 w-8 text-blue-500\" />\r\n            </div>\r\n            <div className=\"ml-4\">\r\n              <p className=\"text-sm font-medium text-gray-500\">Total Flows</p>\r\n              <p className=\"text-2xl font-bold text-gray-900\">{flows.length}</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"card\">\r\n          <div className=\"flex items-center\">\r\n            <div className=\"flex-shrink-0\">\r\n              <PlayIcon className=\"h-8 w-8 text-green-500\" />\r\n            </div>\r\n            <div className=\"ml-4\">\r\n              <p className=\"text-sm font-medium text-gray-500\">Active Flows</p>\r\n              <p className=\"text-2xl font-bold text-gray-900\">\r\n                {safeFlows.filter(f => f.is_active).length}\r\n              </p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"card\">\r\n          <div className=\"flex items-center\">\r\n            <div className=\"flex-shrink-0\">\r\n              <BoltIcon className=\"h-8 w-8 text-purple-500\" />\r\n            </div>\r\n            <div className=\"ml-4\">\r\n              <p className=\"text-sm font-medium text-gray-500\">Total Nodes</p>\r\n              <p className=\"text-2xl font-bold text-gray-900\">\r\n                {safeFlows.reduce((total, f) => total + (f.node_count || 0), 0)}\r\n              </p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"card\">\r\n          <div className=\"flex items-center\">\r\n            <div className=\"flex-shrink-0\">\r\n              <CheckCircleIcon className=\"h-8 w-8 text-yellow-500\" />\r\n            </div>\r\n            <div className=\"ml-4\">\r\n              <p className=\"text-sm font-medium text-gray-500\">Conversations</p>\r\n              <p className=\"text-2xl font-bold text-gray-900\">\r\n                {safeFlows.reduce((total, f) => total + (f.conversation_count || 0), 0)}\r\n              </p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Prerequisites Check */}\r\n      {sessions.length === 0 && (\r\n        <div className=\"bg-yellow-50 border border-yellow-200 rounded-lg p-4\">\r\n          <div className=\"flex items-center space-x-2\">\r\n            <ExclamationTriangleIcon className=\"w-5 h-5 text-yellow-500\" />\r\n            <div>\r\n              <h3 className=\"text-sm font-medium text-yellow-800\">WhatsApp Connection Required</h3>\r\n              <p className=\"text-sm text-yellow-700 mt-1\">\r\n                Connect a WhatsApp device first to create chatbot flows.\r\n              </p>\r\n              <p className=\"text-xs text-yellow-600 mt-2\">\r\n                Debug: Sessions loaded: {sessions.length} | Check browser console for details\r\n              </p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Flows List */}\r\n      {filteredFlows.length === 0 ? (\r\n        <div className=\"text-center py-12\">\r\n          <ChatBubbleOvalLeftEllipsisIcon className=\"w-16 h-16 text-gray-400 mx-auto mb-4\" />\r\n          <h3 className=\"text-lg font-medium text-gray-900 mb-2\">\r\n            {searchTerm || statusFilter !== 'all' ? 'No flows found' : 'No chatbot flows yet'}\r\n          </h3>\r\n          <p className=\"text-gray-600 mb-6\">\r\n            {searchTerm || statusFilter !== 'all'\r\n              ? 'Try adjusting your search or filter criteria'\r\n              : 'Create your first chatbot flow to start automated conversations'\r\n            }\r\n          </p>\r\n          {!searchTerm && statusFilter === 'all' && sessions.length > 0 && (\r\n            <button\r\n              onClick={() => setShowCreateModal(true)}\r\n              className=\"btn-primary flex items-center space-x-2 mx-auto\"\r\n            >\r\n              <PlusIcon className=\"w-5 h-5\" />\r\n              <span>Create Flow</span>\r\n            </button>\r\n          )}\r\n        </div>\r\n      ) : (\r\n        <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden\">\r\n          {/* Professional List View */}\r\n          {/* Table Header */}\r\n          <div className=\"bg-gray-50 px-6 py-4 border-b border-gray-200\">\r\n            <div className=\"grid grid-cols-12 gap-4 items-center text-sm font-medium text-gray-700\">\r\n              <div className=\"col-span-4\">Flow</div>\r\n              <div className=\"col-span-2\">Status</div>\r\n              <div className=\"col-span-2\">Device</div>\r\n              <div className=\"col-span-2\">Nodes</div>\r\n              <div className=\"col-span-1\">Conversations</div>\r\n              <div className=\"col-span-1 text-right\">Actions</div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Flows List */}\r\n          <div className=\"divide-y divide-gray-200\">\r\n            {filteredFlows.map((flow) => (\r\n              <div key={flow.id} className=\"px-6 py-4 hover:bg-gray-50 transition-colors duration-150\">\r\n                <div className=\"grid grid-cols-12 gap-4 items-center\">\r\n                  {/* Flow Name & Description */}\r\n                  <div className=\"col-span-4\">\r\n                    <div className=\"flex items-start space-x-3\">\r\n                      <div className={`w-10 h-10 rounded-lg flex items-center justify-center flex-shrink-0 ${\r\n                        flow.is_active ? 'bg-purple-100' : 'bg-gray-100'\r\n                      }`}>\r\n                        {flow.is_active ? (\r\n                          <ChatBubbleOvalLeftEllipsisIcon className=\"w-5 h-5 text-purple-600\" />\r\n                        ) : (\r\n                          <ChatBubbleOvalLeftEllipsisIcon className=\"w-5 h-5 text-gray-600\" />\r\n                        )}\r\n                      </div>\r\n                      <div className=\"flex-1 min-w-0\">\r\n                        <h3 className=\"font-semibold text-gray-900 truncate\">\r\n                          {typeof flow.name === 'string' ? flow.name : String(flow.name || 'Unnamed Flow')}\r\n                        </h3>\r\n                        {flow.description && (\r\n                          <p className=\"text-sm text-gray-500 line-clamp-2 mt-1\">\r\n                            {flow.description}\r\n                          </p>\r\n                        )}\r\n                        {/* Trigger Keywords */}\r\n                        <div className=\"flex flex-wrap gap-1 mt-2\">\r\n                          {(typeof flow.trigger_keywords === 'string' ? flow.trigger_keywords : String(flow.trigger_keywords || '')).split(',').slice(0, 3).map((keyword, index) => (\r\n                            <span\r\n                              key={index}\r\n                              className=\"inline-flex items-center px-2 py-1 rounded text-xs bg-blue-100 text-blue-800\"\r\n                            >\r\n                              {keyword.trim()}\r\n                            </span>\r\n                          ))}\r\n                          {(typeof flow.trigger_keywords === 'string' ? flow.trigger_keywords : String(flow.trigger_keywords || '')).split(',').length > 3 && (\r\n                            <span className=\"text-xs text-gray-500\">\r\n                              +{(typeof flow.trigger_keywords === 'string' ? flow.trigger_keywords : String(flow.trigger_keywords || '')).split(',').length - 3} more\r\n                            </span>\r\n                          )}\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n\r\n                  {/* Status */}\r\n                  <div className=\"col-span-2\">\r\n                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${\r\n                      flow.is_active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'\r\n                    }`}>\r\n                      {flow.is_active ? <PlayIcon className=\"w-3 h-3 mr-1\" /> : <PauseIcon className=\"w-3 h-3 mr-1\" />}\r\n                      {flow.is_active ? 'Active' : 'Inactive'}\r\n                    </span>\r\n                  </div>\r\n\r\n                  {/* Device */}\r\n                  <div className=\"col-span-2\">\r\n                    <div className=\"text-sm text-gray-900 font-medium\">\r\n                      {flow.device_name ?\r\n                        (typeof flow.device_name === 'string' ? flow.device_name : String(flow.device_name)) :\r\n                        'No Device'\r\n                      }\r\n                    </div>\r\n                  </div>\r\n\r\n                  {/* Nodes Count */}\r\n                  <div className=\"col-span-2\">\r\n                    <span className=\"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-purple-100 text-purple-800\">\r\n                      {flow.node_count || 0} nodes\r\n                    </span>\r\n                  </div>\r\n\r\n                  {/* Conversations Count */}\r\n                  <div className=\"col-span-1\">\r\n                    <div className=\"text-sm text-gray-900 font-medium\">\r\n                      {flow.conversation_count || 0}\r\n                    </div>\r\n                  </div>\r\n\r\n                  {/* Actions */}\r\n                  <div className=\"col-span-1\">\r\n                    <div className=\"flex items-center justify-end space-x-1\">\r\n                      <button\r\n                        onClick={() => {\r\n                          setSelectedFlow(flow);\r\n                          setShowFlowModal(true);\r\n                        }}\r\n                        className=\"p-2 text-gray-400 hover:text-blue-500 hover:bg-blue-50 rounded-lg transition-colors\"\r\n                        title=\"View Flow\"\r\n                      >\r\n                        <EyeIcon className=\"w-4 h-4\" />\r\n                      </button>\r\n                      <button\r\n                        onClick={() => handleEditFlow(flow)}\r\n                        className=\"p-2 text-gray-400 hover:text-purple-500 hover:bg-purple-50 rounded-lg transition-colors\"\r\n                        title=\"Edit Flow\"\r\n                      >\r\n                        <PencilIcon className=\"w-4 h-4\" />\r\n                      </button>\r\n                      <button\r\n                        onClick={() => toggleFlowStatus(flow.id, flow.is_active)}\r\n                        className={`p-2 text-gray-400 hover:bg-opacity-10 rounded-lg transition-colors ${\r\n                          flow.is_active\r\n                            ? 'hover:text-yellow-500 hover:bg-yellow-50'\r\n                            : 'hover:text-green-500 hover:bg-green-50'\r\n                        }`}\r\n                        title={flow.is_active ? 'Pause Flow' : 'Activate Flow'}\r\n                      >\r\n                        {flow.is_active ? <PauseIcon className=\"w-4 h-4\" /> : <PlayIcon className=\"w-4 h-4\" />}\r\n                      </button>\r\n                      <button\r\n                        onClick={() => handleDeleteFlow(flow.id)}\r\n                        className=\"p-2 text-gray-400 hover:text-red-500 hover:bg-red-50 rounded-lg transition-colors\"\r\n                        title=\"Delete Flow\"\r\n                      >\r\n                        <TrashIcon className=\"w-4 h-4\" />\r\n                      </button>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Create Flow Modal */}\r\n      {showCreateModal && (\r\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\r\n          <div className=\"bg-white rounded-xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto\">\r\n            <div className=\"p-6\">\r\n              <div className=\"flex items-center justify-between mb-6\">\r\n                <h3 className=\"text-lg font-semibold text-gray-900\">Create Chatbot Flow</h3>\r\n                <button\r\n                  onClick={() => {\r\n                    setShowCreateModal(false);\r\n                    resetFlowForm();\r\n                  }}\r\n                  className=\"p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg\"\r\n                >\r\n                  <XMarkIcon className=\"w-5 h-5\" />\r\n                </button>\r\n              </div>\r\n\r\n              <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\r\n                {/* Left Column - Flow Settings */}\r\n                <div className=\"space-y-4\">\r\n                  <h4 className=\"font-medium text-gray-900\">Flow Settings</h4>\r\n                  \r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                      Flow Name *\r\n                    </label>\r\n                    <input\r\n                      type=\"text\"\r\n                      value={flowForm.name}\r\n                      onChange={handleFlowFormNameChange}\r\n                      placeholder=\"e.g., Customer Support Bot\"\r\n                      className=\"input-field\"\r\n                    />\r\n                  </div>\r\n\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                      Description\r\n                    </label>\r\n                    <textarea\r\n                      value={flowForm.description}\r\n                      onChange={handleFlowFormDescriptionChange}\r\n                      placeholder=\"Describe what this chatbot flow does...\"\r\n                      rows={3}\r\n                      className=\"input-field resize-none\"\r\n                    />\r\n                  </div>\r\n\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                      WhatsApp Session *\r\n                    </label>\r\n                    <select\r\n                      value={flowForm.sessionId}\r\n                      onChange={handleFlowFormSessionChange}\r\n                      className=\"input-field\"\r\n                    >\r\n                      <option value=\"\">Select a session</option>\r\n                      {(Array.isArray(sessions) ? sessions : []).map(session => {\r\n                        // Ensure session is an object and has required properties\r\n                        if (!session || typeof session !== 'object') {\r\n                          return null;\r\n                        }\r\n\r\n                        const sessionId = session.session_id || `session_${Math.random()}`;\r\n                        const deviceName = typeof session.device_name === 'string' ? session.device_name : String(session.device_name || 'Unknown Device');\r\n                        const phoneNumber = typeof session.phone_number === 'string' || typeof session.phone_number === 'number' ? session.phone_number : String(session.phone_number || 'Unknown');\r\n\r\n                        return (\r\n                          <option key={sessionId} value={sessionId}>\r\n                            {deviceName} (+{phoneNumber})\r\n                          </option>\r\n                        );\r\n                      }).filter(Boolean)}\r\n                    </select>\r\n                  </div>\r\n\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                      Trigger Keywords *\r\n                    </label>\r\n                    <input\r\n                      type=\"text\"\r\n                      value={flowForm.triggerKeywords}\r\n                      onChange={handleFlowFormKeywordsChange}\r\n                      placeholder=\"support, help, bot (comma separated)\"\r\n                      className=\"input-field\"\r\n                    />\r\n                    <p className=\"text-xs text-gray-500 mt-1\">\r\n                      Keywords that will trigger this chatbot flow\r\n                    </p>\r\n                  </div>\r\n\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                      <ClockIcon className=\"w-4 h-4 inline mr-1\" />\r\n                      Cooldown Period (minutes)\r\n                    </label>\r\n                    <input\r\n                      type=\"number\"\r\n                      min=\"0\"\r\n                      value={flowForm.cooldownMinutes}\r\n                      onChange={handleFlowFormCooldownChange}\r\n                      placeholder=\"0\"\r\n                      className=\"input-field\"\r\n                    />\r\n                    <p className=\"text-xs text-gray-500 mt-1\">\r\n                      Minimum time between flow triggers for the same user (0 = no cooldown)\r\n                    </p>\r\n                  </div>\r\n\r\n                  <div className=\"flex items-center\">\r\n                    <input\r\n                      type=\"checkbox\"\r\n                      id=\"flowIsActive\"\r\n                      checked={flowForm.isActive}\r\n                      onChange={handleFlowFormActiveChange}\r\n                      className=\"text-blue-600\"\r\n                    />\r\n                    <label htmlFor=\"flowIsActive\" className=\"ml-2 text-sm text-gray-700\">\r\n                      Flow is active\r\n                    </label>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Right Column - Flow Nodes */}\r\n                <div className=\"space-y-4\">\r\n                  <div className=\"flex items-center justify-between\">\r\n                    <h4 className=\"font-medium text-gray-900\">Flow Nodes ({flowForm.nodes.length})</h4>\r\n                    <button\r\n                      onClick={() => setShowNodeModal(true)}\r\n                      className=\"btn-secondary flex items-center space-x-2 text-sm\"\r\n                    >\r\n                      <PlusIcon className=\"w-4 h-4\" />\r\n                      <span>Add Node</span>\r\n                    </button>\r\n                  </div>\r\n\r\n                  <div className=\"space-y-2 max-h-64 overflow-y-auto\">\r\n                    {flowForm.nodes.map((node, index) => {\r\n                      // Ensure node is an object and has required properties\r\n                      if (!node || typeof node !== 'object') {\r\n                        return null;\r\n                      }\r\n\r\n                      const nodeName = typeof node.name === 'string' ? node.name : 'Unnamed Node';\r\n                      const nodeType = typeof node.nodeType === 'string' ? node.nodeType : 'unknown';\r\n                      const nodeMessage = typeof node.message === 'string' ? node.message : 'No message';\r\n\r\n                      return (\r\n                        <div key={index} className=\"p-3 border border-gray-200 rounded-lg\">\r\n                          <div className=\"flex items-start justify-between\">\r\n                            <div className=\"flex-1\">\r\n                              <div className=\"flex items-center space-x-2\">\r\n                                <span className=\"text-sm font-medium text-gray-900\">{index + 1}. {nodeName}</span>\r\n                                <span className=\"inline-flex items-center px-2 py-1 rounded text-xs bg-blue-100 text-blue-800\">\r\n                                  {nodeType}\r\n                                </span>\r\n                              </div>\r\n                              <p className=\"text-xs text-gray-600 mt-1 line-clamp-2\">{nodeMessage}</p>\r\n                            {(() => {\r\n                              // Handle both old array format and new Baileys interactive format\r\n                              let optionsToDisplay = [];\r\n                              if (Array.isArray(node.options)) {\r\n                                optionsToDisplay = node.options;\r\n                              } else if (typeof node.options === 'object' && node.options && node.options.options) {\r\n                                optionsToDisplay = node.options.options;\r\n                              }\r\n\r\n                              if (optionsToDisplay.length > 0) {\r\n                                return (\r\n                                  <div className=\"flex flex-wrap gap-1 mt-2\">\r\n                                    {optionsToDisplay.map((option, optIndex) => {\r\n                                      // Ensure we always render a string\r\n                                      let displayText = 'Option';\r\n                                      if (typeof option === 'string') {\r\n                                        displayText = option;\r\n                                      } else if (typeof option === 'object' && option) {\r\n                                        // Handle Baileys interactive format\r\n                                        displayText = String(option.display_text || option.title || option.name || option.text || option.value || 'Option');\r\n                                      } else {\r\n                                        displayText = String(option || 'Option');\r\n                                      }\r\n\r\n                                      return (\r\n                                        <span key={optIndex} className=\"text-xs px-1 py-0.5 bg-gray-100 rounded\">\r\n                                          {displayText}\r\n                                        </span>\r\n                                      );\r\n                                    })}\r\n                                  </div>\r\n                                );\r\n                              }\r\n                              return null;\r\n                            })()}\r\n                          </div>\r\n                          <div className=\"flex items-center space-x-1 ml-2\">\r\n                            <button\r\n                              onClick={() => handleEditNode(index)}\r\n                              className=\"p-1 text-gray-400 hover:text-blue-500 rounded\"\r\n                            >\r\n                              <PencilIcon className=\"w-3 h-3\" />\r\n                            </button>\r\n                            <button\r\n                              onClick={() => handleDeleteNode(index)}\r\n                              className=\"p-1 text-gray-400 hover:text-red-500 rounded\"\r\n                            >\r\n                              <TrashIcon className=\"w-3 h-3\" />\r\n                            </button>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                      );\r\n                    }).filter(Boolean)}\r\n                    \r\n                    {flowForm.nodes.length === 0 && (\r\n                      <div className=\"text-center py-8 text-gray-500\">\r\n                        <ChatBubbleOvalLeftEllipsisIcon className=\"w-8 h-8 mx-auto mb-2 text-gray-400\" />\r\n                        <p className=\"text-sm\">No nodes added yet</p>\r\n                        <p className=\"text-xs\">Add your first node to get started</p>\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <div className=\"flex space-x-3 mt-6\">\r\n                <button\r\n                  onClick={() => {\r\n                    setShowCreateModal(false);\r\n                    resetFlowForm();\r\n                  }}\r\n                  className=\"btn-secondary flex-1\"\r\n                >\r\n                  Cancel\r\n                </button>\r\n                <button\r\n                  onClick={handleCreateFlow}\r\n                  className=\"btn-primary flex-1\"\r\n                  disabled={\r\n                    !flowForm.name.trim() || \r\n                    !flowForm.triggerKeywords.trim() || \r\n                    !flowForm.sessionId ||\r\n                    flowForm.nodes.length === 0\r\n                  }\r\n                >\r\n                  Create Flow\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Edit Flow Modal */}\r\n      {showEditModal && (\r\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\r\n          <div className=\"bg-white rounded-xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto\">\r\n            <div className=\"p-6\">\r\n              <div className=\"flex items-center justify-between mb-6\">\r\n                <h3 className=\"text-lg font-semibold text-gray-900\">Edit Chatbot Flow</h3>\r\n                <button\r\n                  onClick={() => {\r\n                    setShowEditModal(false);\r\n                    resetFlowForm();\r\n                  }}\r\n                  className=\"p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg\"\r\n                >\r\n                  <XMarkIcon className=\"w-5 h-5\" />\r\n                </button>\r\n              </div>\r\n\r\n              <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\r\n                {/* Left Column - Flow Settings */}\r\n                <div className=\"space-y-4\">\r\n                  <h4 className=\"font-medium text-gray-900\">Flow Settings</h4>\r\n\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                      Flow Name *\r\n                    </label>\r\n                    <input\r\n                      type=\"text\"\r\n                      value={flowForm.name}\r\n                      onChange={handleFlowFormNameChange}\r\n                      placeholder=\"e.g., Customer Support Bot\"\r\n                      className=\"input-field\"\r\n                    />\r\n                  </div>\r\n\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                      Description\r\n                    </label>\r\n                    <textarea\r\n                      value={flowForm.description}\r\n                      onChange={handleFlowFormDescriptionChange}\r\n                      placeholder=\"Describe what this chatbot flow does...\"\r\n                      rows={3}\r\n                      className=\"input-field resize-none\"\r\n                    />\r\n                  </div>\r\n\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                      WhatsApp Session *\r\n                    </label>\r\n                    <select\r\n                      value={flowForm.sessionId}\r\n                      onChange={handleFlowFormSessionChange}\r\n                      className=\"input-field\"\r\n                    >\r\n                      <option value=\"\">Select a session</option>\r\n                      {(Array.isArray(sessions) ? sessions : []).map(session => {\r\n                        // Ensure session is an object and has required properties\r\n                        if (!session || typeof session !== 'object') {\r\n                          return null;\r\n                        }\r\n\r\n                        const sessionId = session.session_id || `session_${Math.random()}`;\r\n                        const deviceName = typeof session.device_name === 'string' ? session.device_name : String(session.device_name || 'Unknown Device');\r\n                        const phoneNumber = typeof session.phone_number === 'string' || typeof session.phone_number === 'number' ? session.phone_number : String(session.phone_number || 'Unknown');\r\n\r\n                        return (\r\n                          <option key={sessionId} value={sessionId}>\r\n                            {deviceName} (+{phoneNumber})\r\n                          </option>\r\n                        );\r\n                      })}\r\n                    </select>\r\n                  </div>\r\n\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                      Trigger Keywords *\r\n                    </label>\r\n                    <input\r\n                      type=\"text\"\r\n                      value={flowForm.triggerKeywords}\r\n                      onChange={handleFlowFormKeywordsChange}\r\n                      placeholder=\"e.g., support, help, info (comma separated)\"\r\n                      className=\"input-field\"\r\n                    />\r\n                    <p className=\"text-xs text-gray-500 mt-1\">\r\n                      Separate multiple keywords with commas\r\n                    </p>\r\n                  </div>\r\n\r\n                  <div className=\"flex items-center space-x-4\">\r\n                    <label className=\"flex items-center\">\r\n                      <input\r\n                        type=\"checkbox\"\r\n                        checked={flowForm.isActive}\r\n                        onChange={handleFlowFormActiveChange}\r\n                        className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\r\n                      />\r\n                      <span className=\"ml-2 text-sm text-gray-700\">Active</span>\r\n                    </label>\r\n                  </div>\r\n\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                      Cooldown (minutes)\r\n                    </label>\r\n                    <input\r\n                      type=\"number\"\r\n                      value={flowForm.cooldownMinutes}\r\n                      onChange={handleFlowFormCooldownChange}\r\n                      placeholder=\"0\"\r\n                      min=\"0\"\r\n                      className=\"input-field\"\r\n                    />\r\n                    <p className=\"text-xs text-gray-500 mt-1\">\r\n                      Minimum time between flow triggers for the same user\r\n                    </p>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Right Column - Flow Nodes */}\r\n                <div className=\"space-y-4\">\r\n                  <div className=\"flex items-center justify-between\">\r\n                    <h4 className=\"font-medium text-gray-900\">Flow Nodes ({flowForm.nodes.length})</h4>\r\n                    <button\r\n                      onClick={() => setShowNodeModal(true)}\r\n                      className=\"btn-secondary text-sm\"\r\n                    >\r\n                      <PlusIcon className=\"w-4 h-4 mr-1\" />\r\n                      Add Node\r\n                    </button>\r\n                  </div>\r\n\r\n                  <div className=\"space-y-3 max-h-96 overflow-y-auto\">\r\n                    {flowForm.nodes.map((node, index) => (\r\n                      <div key={index} className=\"bg-gray-50 rounded-lg p-4 border border-gray-200\">\r\n                        <div className=\"flex items-start justify-between\">\r\n                          <div className=\"flex-1\">\r\n                            <div className=\"flex items-center space-x-2 mb-2\">\r\n                              <span className=\"text-xs font-medium text-gray-500\">#{index + 1}</span>\r\n                              <span className=\"text-sm font-medium text-gray-900\">{node.name}</span>\r\n                              <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${\r\n                                node.nodeType === 'message' ? 'bg-blue-100 text-blue-800' :\r\n                                node.nodeType === 'question' ? 'bg-green-100 text-green-800' :\r\n                                'bg-purple-100 text-purple-800'\r\n                              }`}>\r\n                                {node.nodeType}\r\n                              </span>\r\n                            </div>\r\n                            <p className=\"text-sm text-gray-600 mb-2\">{node.message}</p>\r\n                            {(() => {\r\n                              if (node.nodeType === 'question' && node.options) {\r\n                                let optionsToShow = [];\r\n                                if (Array.isArray(node.options)) {\r\n                                  optionsToShow = node.options;\r\n                                } else if (typeof node.options === 'object' && node.options.options) {\r\n                                  optionsToShow = node.options.options.map(opt =>\r\n                                    typeof opt === 'string' ? opt : (opt.display_text || opt.title || opt.name || '')\r\n                                  );\r\n                                }\r\n\r\n                                if (optionsToShow.length > 0) {\r\n                                  return (\r\n                                    <div className=\"flex flex-wrap gap-1\">\r\n                                      {optionsToShow.map((option, optIndex) => (\r\n                                        <span key={optIndex} className=\"inline-flex items-center px-2 py-1 rounded text-xs bg-gray-200 text-gray-700\">\r\n                                          {typeof option === 'string' ? option : String(option || '')}\r\n                                        </span>\r\n                                      ))}\r\n                                    </div>\r\n                                  );\r\n                                }\r\n                              }\r\n                              return null;\r\n                            })()}\r\n                          </div>\r\n                          <div className=\"flex items-center space-x-1 ml-2\">\r\n                            <button\r\n                              onClick={() => handleEditNode(index)}\r\n                              className=\"p-1 text-gray-400 hover:text-blue-500 rounded\"\r\n                            >\r\n                              <PencilIcon className=\"w-3 h-3\" />\r\n                            </button>\r\n                            <button\r\n                              onClick={() => handleDeleteNode(index)}\r\n                              className=\"p-1 text-gray-400 hover:text-red-500 rounded\"\r\n                            >\r\n                              <TrashIcon className=\"w-3 h-3\" />\r\n                            </button>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    ))}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <div className=\"flex justify-end space-x-3 mt-6 pt-6 border-t border-gray-200\">\r\n                <button\r\n                  onClick={() => {\r\n                    setShowEditModal(false);\r\n                    resetFlowForm();\r\n                  }}\r\n                  className=\"btn-secondary\"\r\n                >\r\n                  Cancel\r\n                </button>\r\n                <button\r\n                  onClick={handleUpdateFlow}\r\n                  className=\"btn-primary\"\r\n                  disabled={\r\n                    !flowForm.name.trim() ||\r\n                    !flowForm.triggerKeywords.trim() ||\r\n                    !flowForm.sessionId ||\r\n                    flowForm.nodes.length === 0\r\n                  }\r\n                >\r\n                  Update Flow\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Add/Edit Node Modal */}\r\n      {showNodeModal && (\r\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\r\n          <div className=\"bg-white rounded-xl shadow-xl max-w-lg w-full max-h-[90vh] flex flex-col\">\r\n            <div className=\"p-6 flex-shrink-0\">\r\n              <div className=\"flex items-center justify-between mb-6\">\r\n                <h3 className=\"text-lg font-semibold text-gray-900\">\r\n                  {editingNodeIndex >= 0 ? 'Edit Node' : 'Add Node'}\r\n                </h3>\r\n                <button\r\n                  onClick={() => {\r\n                    setShowNodeModal(false);\r\n                    resetNodeForm();\r\n                    setEditingNodeIndex(-1);\r\n                  }}\r\n                  className=\"p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg\"\r\n                >\r\n                  <XMarkIcon className=\"w-5 h-5\" />\r\n                </button>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"flex-1 overflow-y-auto px-6\">\r\n              <div className=\"space-y-4\">\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                    Node Name *\r\n                  </label>\r\n                  <input\r\n                    type=\"text\"\r\n                    value={nodeForm.name}\r\n                    onChange={handleNodeFormNameChange}\r\n                    placeholder=\"e.g., Welcome Message\"\r\n                    className=\"input-field\"\r\n                  />\r\n                </div>\r\n\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                    Node Type\r\n                  </label>\r\n                  <select\r\n                    value={nodeForm.nodeType}\r\n                    onChange={(e) => {\r\n                      // Reset options when changing node type to prevent object/string conflicts\r\n                      setNodeForm(prev => ({\r\n                        ...prev,\r\n                        nodeType: e.target.value,\r\n                        options: [], // Reset options to empty array\r\n                        extractVariable: '', // Reset extract variable\r\n                        conditions: [] // Reset conditions\r\n                      }));\r\n                    }}\r\n                    className=\"input-field\"\r\n                  >\r\n                    <option value=\"message\">Message</option>\r\n                    <option value=\"question\">Question</option>\r\n                    <option value=\"action\">Action</option>\r\n                    <option value=\"condition\">Condition</option>\r\n                  </select>\r\n                </div>\r\n\r\n                {/* Message Type Selection */}\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 mb-3\">\r\n                    Message Type\r\n                  </label>\r\n                  <div className=\"grid grid-cols-2 gap-3\">\r\n                    <button\r\n                      type=\"button\"\r\n                      onClick={() => setNodeForm(prev => ({ ...prev, messageType: 'text', templateId: '' }))}\r\n                      className={`p-3 border-2 rounded-lg text-left transition-colors ${\r\n                        nodeForm.messageType === 'text'\r\n                          ? 'border-blue-500 bg-blue-50 text-blue-700'\r\n                          : 'border-gray-200 hover:border-gray-300'\r\n                      }`}\r\n                    >\r\n                      <ChatBubbleLeftRightIcon className=\"w-5 h-5 mb-2\" />\r\n                      <div className=\"font-medium\">Text Message</div>\r\n                      <div className=\"text-xs text-gray-500\">Custom text with attachments</div>\r\n                    </button>\r\n                    <button\r\n                      type=\"button\"\r\n                      onClick={() => setNodeForm(prev => ({ ...prev, messageType: 'template' }))}\r\n                      className={`p-3 border-2 rounded-lg text-left transition-colors ${\r\n                        nodeForm.messageType === 'template'\r\n                          ? 'border-blue-500 bg-blue-50 text-blue-700'\r\n                          : 'border-gray-200 hover:border-gray-300'\r\n                      }`}\r\n                    >\r\n                      <DocumentTextIcon className=\"w-5 h-5 mb-2\" />\r\n                      <div className=\"font-medium\">Template</div>\r\n                      <div className=\"text-xs text-gray-500\">Use predefined template</div>\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Template Selection (only if template type is selected) */}\r\n                {nodeForm.messageType === 'template' && (\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                      Select Template *\r\n                    </label>\r\n                    <select\r\n                      value={nodeForm.templateId}\r\n                      onChange={(e) => handleTemplateSelect(e.target.value)}\r\n                      className=\"input-field\"\r\n                    >\r\n                      <option value=\"\">Choose a template...</option>\r\n                      {(Array.isArray(templates) ? templates : []).map(template => {\r\n                        // Ensure template is an object and has required properties\r\n                        if (!template || typeof template !== 'object') {\r\n                          return null;\r\n                        }\r\n\r\n                        const templateType = TEMPLATE_TYPES[template.type || 'text'];\r\n                        const templateName = typeof template.name === 'string' ? template.name : String(template.name || 'Unnamed Template');\r\n                        const typeName = typeof templateType?.name === 'string' ? templateType.name : String(template.type || 'Text');\r\n                        const templateId = template.id || `template_${Math.random()}`;\r\n\r\n                        return (\r\n                          <option key={templateId} value={templateId}>\r\n                            {templateName} ({typeName})\r\n                          </option>\r\n                        );\r\n                      }).filter(Boolean)}\r\n                    </select>\r\n                  </div>\r\n                )}\r\n\r\n                {/* Message Content */}\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                    {nodeForm.messageType === 'template' ? 'Message Preview' : 'Message Content'} *\r\n                  </label>\r\n                  <textarea\r\n                    value={nodeForm.message}\r\n                    onChange={handleNodeFormMessageChange}\r\n                    placeholder={nodeForm.messageType === 'template' ? 'Select a template to see preview...' : 'Enter the message for this node...'}\r\n                    rows={4}\r\n                    className=\"input-field resize-none\"\r\n                    readOnly={nodeForm.messageType === 'template'}\r\n                  />\r\n                </div>\r\n\r\n                {/* Template Variables (only if template is selected and has variables) */}\r\n                {nodeForm.messageType === 'template' && nodeForm.templateId && Object.keys(nodeForm.variables).length > 0 && (\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                      Template Variables\r\n                    </label>\r\n                    <div className=\"space-y-3\">\r\n                      {Object.keys(nodeForm.variables).map(variable => {\r\n                        // Ensure variable is always a string\r\n                        const safeVariable = typeof variable === 'string' ? variable : String(variable || '');\r\n                        return (\r\n                          <div key={safeVariable}>\r\n                            <label className=\"block text-xs font-medium text-gray-600 mb-1\">\r\n                              {safeVariable}\r\n                            </label>\r\n                            <input\r\n                              type=\"text\"\r\n                              value={typeof nodeForm.variables[safeVariable] === 'string' ? nodeForm.variables[safeVariable] : String(nodeForm.variables[safeVariable] || '')}\r\n                              onChange={(e) => setNodeForm(prev => ({\r\n                                ...prev,\r\n                                variables: {\r\n                                  ...prev.variables,\r\n                                  [safeVariable]: e.target.value\r\n                                }\r\n                              }))}\r\n                              placeholder={`Enter value for {{${safeVariable}}}`}\r\n                              className=\"input-field\"\r\n                            />\r\n                          </div>\r\n                        );\r\n                      })}\r\n                    </div>\r\n                  </div>\r\n                )}\r\n\r\n                {/* File Attachment (only for text messages) */}\r\n                {nodeForm.messageType === 'text' && (\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                      Attachment (Optional)\r\n                    </label>\r\n                    <div className=\"space-y-3\">\r\n                      <div className=\"flex items-center space-x-4\">\r\n                        <select\r\n                          value={nodeForm.attachmentType}\r\n                          onChange={handleNodeFormAttachmentTypeChange}\r\n                          className=\"input-field w-32\"\r\n                        >\r\n                          <option value=\"image\">Image</option>\r\n                          <option value=\"video\">Video</option>\r\n                          <option value=\"audio\">Audio</option>\r\n                          <option value=\"document\">Document</option>\r\n                        </select>\r\n                        <input\r\n                          type=\"file\"\r\n                          onChange={handleNodeFormAttachmentFileChange}\r\n                          accept={\r\n                            nodeForm.attachmentType === 'image' ? 'image/*' :\r\n                            nodeForm.attachmentType === 'video' ? 'video/*' :\r\n                            nodeForm.attachmentType === 'audio' ? 'audio/*' :\r\n                            '*/*'\r\n                          }\r\n                          className=\"input-field flex-1\"\r\n                        />\r\n                      </div>\r\n                      {nodeForm.attachmentFile && (\r\n                        <div className=\"space-y-2\">\r\n                          <div className=\"flex items-center space-x-2 text-sm text-gray-600\">\r\n                            <PaperClipIcon className=\"w-4 h-4\" />\r\n                            <span>{typeof nodeForm.attachmentFile.name === 'string' ? nodeForm.attachmentFile.name : String(nodeForm.attachmentFile.name || 'Unknown file')}</span>\r\n                            <button\r\n                              onClick={() => setNodeForm(prev => ({ ...prev, attachmentFile: null }))}\r\n                              className=\"text-red-500 hover:text-red-700\"\r\n                            >\r\n                              <XMarkIcon className=\"w-4 h-4\" />\r\n                            </button>\r\n                          </div>\r\n\r\n                          {/* Warning for attachments with interactive buttons - Show for Question nodes */}\r\n                          {nodeForm.nodeType === 'question' && (\r\n                            <div className=\"p-3 bg-yellow-50 border border-yellow-200 rounded-lg\">\r\n                              <div className=\"flex items-start space-x-2\">\r\n                                <svg className=\"w-5 h-5 text-yellow-600 mt-0.5 flex-shrink-0\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n                                  <path fillRule=\"evenodd\" d=\"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\r\n                                </svg>\r\n                                <div>\r\n                                  <p className=\"text-sm font-medium text-yellow-800\">\r\n                                    Attachment + Interactive Buttons Limitation\r\n                                  </p>\r\n                                  <p className=\"text-xs text-yellow-700 mt-1\">\r\n                                    WhatsApp doesn't support interactive buttons with media attachments. The attachment will be sent, but buttons won't work. Consider using \"Text List\" instead of \"Interactive Buttons\" in the options below.\r\n                                  </p>\r\n                                </div>\r\n                              </div>\r\n                            </div>\r\n                          )}\r\n                        </div>\r\n                      )}\r\n                    </div>\r\n                  </div>\r\n                )}\r\n\r\n                {nodeForm.nodeType === 'question' && (\r\n                  <div className=\"space-y-4\">\r\n                    <div>\r\n                      <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                        Options\r\n                      </label>\r\n                      <div className=\"space-y-2\">\r\n                        {(Array.isArray(nodeForm.options) ? nodeForm.options : []).map((option, index) => {\r\n                          // Ensure option is always a string\r\n                          let optionValue = '';\r\n                          if (typeof option === 'string') {\r\n                            optionValue = option;\r\n                          } else if (typeof option === 'object' && option !== null) {\r\n                            optionValue = String(option.display_text || option.title || option.name || option.text || option.value || '');\r\n                          } else {\r\n                            optionValue = String(option || '');\r\n                          }\r\n\r\n                          return (\r\n                            <div key={index} className=\"flex items-center space-x-2\">\r\n                              <input\r\n                                type=\"text\"\r\n                                value={optionValue}\r\n                                onChange={(e) => updateOption(index, e.target.value)}\r\n                                placeholder={`Option ${index + 1}`}\r\n                                className=\"input-field flex-1\"\r\n                              />\r\n                              <button\r\n                                onClick={() => removeOption(index)}\r\n                                className=\"p-2 text-red-500 hover:bg-red-50 rounded-lg\"\r\n                              >\r\n                                <TrashIcon className=\"w-4 h-4\" />\r\n                              </button>\r\n                            </div>\r\n                          );\r\n                        })}\r\n                        <button\r\n                          onClick={addOption}\r\n                          className=\"btn-secondary w-full text-sm\"\r\n                        >\r\n                          Add Option\r\n                        </button>\r\n                      </div>\r\n                    </div>\r\n\r\n                    {/* Interaction Type */}\r\n                    <div>\r\n                      <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                        Display Options As\r\n                      </label>\r\n                      <select\r\n                        value={nodeForm.interactionType || 'buttons'}\r\n                        onChange={(e) => setNodeForm(prev => ({ ...prev, interactionType: e.target.value }))}\r\n                        className=\"input-field\"\r\n                      >\r\n                        <option value=\"buttons\">Interactive Buttons (≤3 options)</option>\r\n                        <option value=\"text\">Text List</option>\r\n                      </select>\r\n                      <p className=\"text-xs text-gray-500 mt-1\">\r\n                        Interactive buttons provide better user experience but are limited to 3 options. Text list works for any number of options.\r\n                      </p>\r\n\r\n                      {/* Warning for buttons with attachments */}\r\n                      {nodeForm.interactionType === 'buttons' && nodeForm.attachmentFile && (\r\n                        <div className=\"mt-2 p-3 bg-yellow-50 border border-yellow-200 rounded-lg\">\r\n                          <div className=\"flex items-start space-x-2\">\r\n                            <svg className=\"w-5 h-5 text-yellow-600 mt-0.5 flex-shrink-0\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n                              <path fillRule=\"evenodd\" d=\"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\r\n                            </svg>\r\n                            <div>\r\n                              <p className=\"text-sm font-medium text-yellow-800\">\r\n                                Interactive Buttons + Attachments Limitation\r\n                              </p>\r\n                              <p className=\"text-xs text-yellow-700 mt-1\">\r\n                                WhatsApp doesn't support interactive buttons with media attachments. Consider using \"Text List\" instead, or remove the attachment to use interactive buttons.\r\n                              </p>\r\n                            </div>\r\n                          </div>\r\n                        </div>\r\n                      )}\r\n                    </div>\r\n\r\n                    {/* Variable Extraction */}\r\n                    <div>\r\n                      <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                        Extract Variable (Optional)\r\n                      </label>\r\n                      <input\r\n                        type=\"text\"\r\n                        value={nodeForm.extractVariable}\r\n                        onChange={handleNodeFormExtractVariableChange}\r\n                        placeholder=\"e.g., name, email, phone\"\r\n                        className=\"input-field\"\r\n                      />\r\n                      <p className=\"text-xs text-gray-500 mt-1\">\r\n                        Variable name to store user's response. Use this variable in future messages to reference it.\r\n                      </p>\r\n                    </div>\r\n                  </div>\r\n                )}\r\n\r\n                {/* Action Node Configuration */}\r\n                {nodeForm.nodeType === 'action' && (\r\n                  <div className=\"space-y-4\">\r\n                    <div>\r\n                      <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                        Action Type\r\n                      </label>\r\n                      <select\r\n                        value={nodeForm.actionType || 'webhook'}\r\n                        onChange={(e) => setNodeForm(prev => ({ ...prev, actionType: e.target.value }))}\r\n                        className=\"input-field\"\r\n                      >\r\n                        <option value=\"webhook\">Send Webhook</option>\r\n                        <option value=\"email\">Send Email</option>\r\n                        <option value=\"save_data\">Save Data</option>\r\n                        <option value=\"api_call\">API Call</option>\r\n                        <option value=\"delay\">Add Delay</option>\r\n                      </select>\r\n                    </div>\r\n\r\n                    {nodeForm.actionType === 'webhook' && (\r\n                      <div>\r\n                        <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                          Webhook URL\r\n                        </label>\r\n                        <input\r\n                          type=\"url\"\r\n                          value={nodeForm.webhookUrl || ''}\r\n                          onChange={(e) => setNodeForm(prev => ({ ...prev, webhookUrl: e.target.value }))}\r\n                          placeholder=\"https://your-webhook-url.com/endpoint\"\r\n                          className=\"input-field\"\r\n                        />\r\n                        <p className=\"text-xs text-gray-500 mt-1\">\r\n                          URL to send conversation data to when this action is triggered.\r\n                        </p>\r\n                      </div>\r\n                    )}\r\n\r\n                    {nodeForm.actionType === 'delay' && (\r\n                      <div>\r\n                        <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                          Delay Duration (seconds)\r\n                        </label>\r\n                        <input\r\n                          type=\"number\"\r\n                          min=\"1\"\r\n                          max=\"300\"\r\n                          value={nodeForm.delaySeconds || 5}\r\n                          onChange={(e) => setNodeForm(prev => ({ ...prev, delaySeconds: parseInt(e.target.value) || 5 }))}\r\n                          className=\"input-field\"\r\n                        />\r\n                        <p className=\"text-xs text-gray-500 mt-1\">\r\n                          How long to wait before proceeding to the next node (1-300 seconds).\r\n                        </p>\r\n                      </div>\r\n                    )}\r\n\r\n                    {nodeForm.actionType === 'email' && (\r\n                      <div className=\"space-y-3\">\r\n                        <div>\r\n                          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                            Email Recipients\r\n                          </label>\r\n                          <input\r\n                            type=\"email\"\r\n                            value={nodeForm.emailRecipients || ''}\r\n                            onChange={(e) => setNodeForm(prev => ({ ...prev, emailRecipients: e.target.value }))}\r\n                            placeholder=\"<EMAIL>, <EMAIL>\"\r\n                            className=\"input-field\"\r\n                          />\r\n                          <p className=\"text-xs text-gray-500 mt-1\">\r\n                            Comma-separated email addresses to send notification to.\r\n                          </p>\r\n                        </div>\r\n                        <div>\r\n                          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                            Email Subject\r\n                          </label>\r\n                          <input\r\n                            type=\"text\"\r\n                            value={nodeForm.emailSubject || ''}\r\n                            onChange={(e) => setNodeForm(prev => ({ ...prev, emailSubject: e.target.value }))}\r\n                            placeholder=\"New chatbot conversation data\"\r\n                            className=\"input-field\"\r\n                          />\r\n                        </div>\r\n                        <div>\r\n                          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                            Email Body\r\n                          </label>\r\n                          <textarea\r\n                            value={nodeForm.emailBody || ''}\r\n                            onChange={(e) => setNodeForm(prev => ({ ...prev, emailBody: e.target.value }))}\r\n                            placeholder=\"Hello {{user_name}}, thank you for your interest in {{selected_product}}...\"\r\n                            className=\"input-field h-32\"\r\n                            rows={4}\r\n                          />\r\n                          <p className=\"text-xs text-gray-500 mt-1\">\r\n                            Use {`{{variable_name}}`} to insert dynamic data from conversation.\r\n                          </p>\r\n                        </div>\r\n                        <div>\r\n                          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                            Email Template (Optional)\r\n                          </label>\r\n                          <select\r\n                            value={nodeForm.emailTemplate || ''}\r\n                            onChange={(e) => setNodeForm(prev => ({ ...prev, emailTemplate: e.target.value }))}\r\n                            className=\"input-field\"\r\n                          >\r\n                            <option value=\"\">No Template</option>\r\n                            <option value=\"welcome\">Welcome Email</option>\r\n                            <option value=\"followup\">Follow-up Email</option>\r\n                            <option value=\"notification\">Notification Email</option>\r\n                          </select>\r\n                        </div>\r\n                      </div>\r\n                    )}\r\n\r\n                    {nodeForm.actionType === 'api_call' && (\r\n                      <div className=\"space-y-3\">\r\n                        <div>\r\n                          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                            API Endpoint\r\n                          </label>\r\n                          <input\r\n                            type=\"url\"\r\n                            value={nodeForm.apiEndpoint || ''}\r\n                            onChange={(e) => setNodeForm(prev => ({ ...prev, apiEndpoint: e.target.value }))}\r\n                            placeholder=\"https://api.example.com/endpoint\"\r\n                            className=\"input-field\"\r\n                          />\r\n                        </div>\r\n                        <div>\r\n                          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                            HTTP Method\r\n                          </label>\r\n                          <select\r\n                            value={nodeForm.apiMethod || 'POST'}\r\n                            onChange={(e) => setNodeForm(prev => ({ ...prev, apiMethod: e.target.value }))}\r\n                            className=\"input-field\"\r\n                          >\r\n                            <option value=\"GET\">GET</option>\r\n                            <option value=\"POST\">POST</option>\r\n                            <option value=\"PUT\">PUT</option>\r\n                            <option value=\"PATCH\">PATCH</option>\r\n                            <option value=\"DELETE\">DELETE</option>\r\n                          </select>\r\n                        </div>\r\n                        <div>\r\n                          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                            Headers (JSON)\r\n                          </label>\r\n                          <textarea\r\n                            value={nodeForm.apiHeaders || ''}\r\n                            onChange={(e) => setNodeForm(prev => ({ ...prev, apiHeaders: e.target.value }))}\r\n                            placeholder='{\"Authorization\": \"Bearer token\", \"Content-Type\": \"application/json\"}'\r\n                            className=\"input-field h-20\"\r\n                            rows={2}\r\n                          />\r\n                        </div>\r\n                        <div>\r\n                          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                            Request Body (JSON)\r\n                          </label>\r\n                          <textarea\r\n                            value={nodeForm.apiBody || ''}\r\n                            onChange={(e) => setNodeForm(prev => ({ ...prev, apiBody: e.target.value }))}\r\n                            placeholder='{\"user_name\": \"{{user_name}}\", \"selected_product\": \"{{selected_product}}\"}'\r\n                            className=\"input-field h-24\"\r\n                            rows={3}\r\n                          />\r\n                          <p className=\"text-xs text-gray-500 mt-1\">\r\n                            Use {`{{variable_name}}`} to insert dynamic data from conversation.\r\n                          </p>\r\n                        </div>\r\n                      </div>\r\n                    )}\r\n\r\n                    {nodeForm.actionType === 'save_data' && (\r\n                      <div className=\"space-y-3\">\r\n                        <div>\r\n                          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                            Data Fields to Save\r\n                          </label>\r\n                          <textarea\r\n                            value={Array.isArray(nodeForm.saveDataFields) ? nodeForm.saveDataFields.join('\\n') : ''}\r\n                            onChange={(e) => setNodeForm(prev => ({\r\n                              ...prev,\r\n                              saveDataFields: e.target.value.split('\\n').filter(field => field.trim())\r\n                            }))}\r\n                            placeholder=\"user_name&#10;user_email&#10;selected_product&#10;phone_number\"\r\n                            className=\"input-field h-24\"\r\n                            rows={4}\r\n                          />\r\n                          <p className=\"text-xs text-gray-500 mt-1\">\r\n                            Enter one field name per line. These will be saved to the database.\r\n                          </p>\r\n                        </div>\r\n                      </div>\r\n                    )}\r\n\r\n                    {/* True/False Path Configuration for all condition types except random */}\r\n                    {nodeForm.conditionType !== 'random' && (\r\n                      <div className=\"space-y-3 border-t pt-4\">\r\n                        <h4 className=\"text-sm font-medium text-gray-700\">Path Configuration</h4>\r\n                        <div className=\"grid grid-cols-2 gap-4\">\r\n                          <div>\r\n                            <label className=\"block text-sm font-medium text-green-700 mb-2\">\r\n                              ✅ True Path (Condition Met)\r\n                            </label>\r\n                            <select\r\n                              value={nodeForm.truePath || ''}\r\n                              onChange={(e) => setNodeForm(prev => ({ ...prev, truePath: e.target.value || null }))}\r\n                              className=\"input-field border-green-300 focus:border-green-500\"\r\n                            >\r\n                              <option value=\"\">Select Next Node</option>\r\n                              {flowForm.nodes.map((node, nodeIndex) => (\r\n                                <option key={nodeIndex} value={nodeIndex + 1}>\r\n                                  {nodeIndex + 1}. {node.name}\r\n                                </option>\r\n                              ))}\r\n                            </select>\r\n                          </div>\r\n                          <div>\r\n                            <label className=\"block text-sm font-medium text-red-700 mb-2\">\r\n                              ❌ False Path (Condition Not Met)\r\n                            </label>\r\n                            <select\r\n                              value={nodeForm.falsePath || ''}\r\n                              onChange={(e) => setNodeForm(prev => ({ ...prev, falsePath: e.target.value || null }))}\r\n                              className=\"input-field border-red-300 focus:border-red-500\"\r\n                            >\r\n                              <option value=\"\">Select Next Node</option>\r\n                              {flowForm.nodes.map((node, nodeIndex) => (\r\n                                <option key={nodeIndex} value={nodeIndex + 1}>\r\n                                  {nodeIndex + 1}. {node.name}\r\n                                </option>\r\n                              ))}\r\n                            </select>\r\n                          </div>\r\n                        </div>\r\n                        <p className=\"text-xs text-gray-500\">\r\n                          Choose different paths based on whether the condition is true or false.\r\n                        </p>\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                )}\r\n\r\n                {/* Condition Node Configuration */}\r\n                {nodeForm.nodeType === 'condition' && (\r\n                  <div className=\"space-y-4\">\r\n                    <div>\r\n                      <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                        Condition Type\r\n                      </label>\r\n                      <select\r\n                        value={nodeForm.conditionType || 'user_response'}\r\n                        onChange={(e) => setNodeForm(prev => ({ ...prev, conditionType: e.target.value }))}\r\n                        className=\"input-field\"\r\n                      >\r\n                        <option value=\"user_response\">Based on User Response</option>\r\n                        <option value=\"variable_value\">Based on Variable Value</option>\r\n                        <option value=\"time_based\">Time-based Condition</option>\r\n                        <option value=\"random\">Random Selection</option>\r\n                      </select>\r\n                    </div>\r\n\r\n                    {nodeForm.conditionType === 'user_response' && (\r\n                      <div>\r\n                        <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                          Response Conditions\r\n                        </label>\r\n                        <div className=\"space-y-2\">\r\n                          {(nodeForm.responseConditions || []).map((condition, index) => (\r\n                            <div key={index} className=\"flex space-x-2\">\r\n                              <input\r\n                                type=\"text\"\r\n                                value={condition.keyword || ''}\r\n                                onChange={(e) => {\r\n                                  const updated = [...(nodeForm.responseConditions || [])];\r\n                                  updated[index] = { ...updated[index], keyword: e.target.value };\r\n                                  setNodeForm(prev => ({ ...prev, responseConditions: updated }));\r\n                                }}\r\n                                placeholder=\"Keyword to match\"\r\n                                className=\"input-field flex-1\"\r\n                              />\r\n                              <select\r\n                                value={condition.nextNode || ''}\r\n                                onChange={(e) => {\r\n                                  const updated = [...(nodeForm.responseConditions || [])];\r\n                                  updated[index] = { ...updated[index], nextNode: e.target.value };\r\n                                  setNodeForm(prev => ({ ...prev, responseConditions: updated }));\r\n                                }}\r\n                                className=\"input-field w-40\"\r\n                              >\r\n                                <option value=\"\">Select Next Node</option>\r\n                                {flowForm.nodes.map((node, nodeIndex) => (\r\n                                  <option key={nodeIndex} value={nodeIndex + 1}>\r\n                                    {nodeIndex + 1}. {node.name}\r\n                                  </option>\r\n                                ))}\r\n                              </select>\r\n                              <button\r\n                                type=\"button\"\r\n                                onClick={() => {\r\n                                  const updated = (nodeForm.responseConditions || []).filter((_, i) => i !== index);\r\n                                  setNodeForm(prev => ({ ...prev, responseConditions: updated }));\r\n                                }}\r\n                                className=\"px-3 py-2 text-red-600 hover:text-red-800\"\r\n                              >\r\n                                ×\r\n                              </button>\r\n                            </div>\r\n                          ))}\r\n                          <button\r\n                            type=\"button\"\r\n                            onClick={() => {\r\n                              const updated = [...(nodeForm.responseConditions || []), { keyword: '', nextNode: '' }];\r\n                              setNodeForm(prev => ({ ...prev, responseConditions: updated }));\r\n                            }}\r\n                            className=\"w-full px-3 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50\"\r\n                          >\r\n                            Add Condition\r\n                          </button>\r\n                        </div>\r\n                        <p className=\"text-xs text-gray-500 mt-1\">\r\n                          Define keywords and which node to go to when user response contains that keyword.\r\n                        </p>\r\n                      </div>\r\n                    )}\r\n\r\n                    {nodeForm.conditionType === 'variable_value' && (\r\n                      <div className=\"space-y-3\">\r\n                        <div>\r\n                          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                            Variable Name\r\n                          </label>\r\n                          <input\r\n                            type=\"text\"\r\n                            value={nodeForm.conditionVariable || ''}\r\n                            onChange={(e) => setNodeForm(prev => ({ ...prev, conditionVariable: e.target.value }))}\r\n                            placeholder=\"e.g., user_name, email\"\r\n                            className=\"input-field\"\r\n                          />\r\n                        </div>\r\n                        <div>\r\n                          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                            Condition\r\n                          </label>\r\n                          <select\r\n                            value={nodeForm.conditionOperator || 'equals'}\r\n                            onChange={(e) => setNodeForm(prev => ({ ...prev, conditionOperator: e.target.value }))}\r\n                            className=\"input-field\"\r\n                          >\r\n                            <option value=\"equals\">Equals</option>\r\n                            <option value=\"contains\">Contains</option>\r\n                            <option value=\"not_equals\">Not Equals</option>\r\n                            <option value=\"is_empty\">Is Empty</option>\r\n                            <option value=\"is_not_empty\">Is Not Empty</option>\r\n                          </select>\r\n                        </div>\r\n                        <div>\r\n                          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                            Expected Value\r\n                          </label>\r\n                          <input\r\n                            type=\"text\"\r\n                            value={nodeForm.conditionValue || ''}\r\n                            onChange={(e) => setNodeForm(prev => ({ ...prev, conditionValue: e.target.value }))}\r\n                            placeholder=\"Value to compare against\"\r\n                            className=\"input-field\"\r\n                          />\r\n                        </div>\r\n                      </div>\r\n                    )}\r\n\r\n                    {nodeForm.conditionType === 'random' && (\r\n                      <div>\r\n                        <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                          Random Paths\r\n                        </label>\r\n                        <div className=\"space-y-2\">\r\n                          {(nodeForm.randomPaths || []).map((path, index) => (\r\n                            <div key={index} className=\"flex space-x-2\">\r\n                              <input\r\n                                type=\"number\"\r\n                                min=\"1\"\r\n                                max=\"100\"\r\n                                value={path.weight || 50}\r\n                                onChange={(e) => {\r\n                                  const updated = [...(nodeForm.randomPaths || [])];\r\n                                  updated[index] = { ...updated[index], weight: parseInt(e.target.value) || 50 };\r\n                                  setNodeForm(prev => ({ ...prev, randomPaths: updated }));\r\n                                }}\r\n                                placeholder=\"Weight %\"\r\n                                className=\"input-field w-20\"\r\n                              />\r\n                              <select\r\n                                value={path.nextNode || ''}\r\n                                onChange={(e) => {\r\n                                  const updated = [...(nodeForm.randomPaths || [])];\r\n                                  updated[index] = { ...updated[index], nextNode: e.target.value };\r\n                                  setNodeForm(prev => ({ ...prev, randomPaths: updated }));\r\n                                }}\r\n                                className=\"input-field flex-1\"\r\n                              >\r\n                                <option value=\"\">Select Next Node</option>\r\n                                {flowForm.nodes.map((node, nodeIndex) => (\r\n                                  <option key={nodeIndex} value={nodeIndex + 1}>\r\n                                    {nodeIndex + 1}. {node.name}\r\n                                  </option>\r\n                                ))}\r\n                              </select>\r\n                              <button\r\n                                type=\"button\"\r\n                                onClick={() => {\r\n                                  const updated = (nodeForm.randomPaths || []).filter((_, i) => i !== index);\r\n                                  setNodeForm(prev => ({ ...prev, randomPaths: updated }));\r\n                                }}\r\n                                className=\"px-3 py-2 text-red-600 hover:text-red-800\"\r\n                              >\r\n                                ×\r\n                              </button>\r\n                            </div>\r\n                          ))}\r\n                          <button\r\n                            type=\"button\"\r\n                            onClick={() => {\r\n                              const updated = [...(nodeForm.randomPaths || []), { weight: 50, nextNode: '' }];\r\n                              setNodeForm(prev => ({ ...prev, randomPaths: updated }));\r\n                            }}\r\n                            className=\"w-full px-3 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50\"\r\n                          >\r\n                            Add Random Path\r\n                          </button>\r\n                        </div>\r\n                        <p className=\"text-xs text-gray-500 mt-1\">\r\n                          Define multiple paths with weights for random selection.\r\n                        </p>\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                )}\r\n\r\n                {/* Next Node Selection */}\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                    Next Node (Optional)\r\n                  </label>\r\n                  <select\r\n                    value={nodeForm.nextNodeId || ''}\r\n                    onChange={handleNodeFormNextNodeChange}\r\n                    className=\"input-field\"\r\n                  >\r\n                    <option value=\"\">Auto (Next in sequence)</option>\r\n                    {flowForm.nodes.map((node, index) => {\r\n                      // Use position-based indexing (index + 1) for next node selection\r\n                      const nodePosition = index + 1;\r\n                      return (\r\n                        <option key={index} value={nodePosition}>\r\n                          {nodePosition}. {typeof node.name === 'string' ? node.name : 'Unnamed Node'}\r\n                        </option>\r\n                      );\r\n                    })}\r\n                  </select>\r\n                  <p className=\"text-xs text-gray-500 mt-1\">\r\n                    Specify which node to go to next, or leave empty for sequential flow.\r\n                  </p>\r\n                </div>\r\n\r\n\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"p-6 border-t border-gray-200 flex-shrink-0\">\r\n              <div className=\"flex space-x-3\">\r\n                <button\r\n                  onClick={() => {\r\n                    setShowNodeModal(false);\r\n                    resetNodeForm();\r\n                    setEditingNodeIndex(-1);\r\n                  }}\r\n                  className=\"btn-secondary flex-1\"\r\n                >\r\n                  Cancel\r\n                </button>\r\n                <button\r\n                  onClick={handleAddNode}\r\n                  className=\"btn-primary flex-1\"\r\n                  disabled={!nodeForm.name.trim() || !nodeForm.message.trim()}\r\n                >\r\n                  {editingNodeIndex >= 0 ? 'Update Node' : 'Add Node'}\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* View Flow Modal */}\r\n      {showFlowModal && selectedFlow && (\r\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\r\n          <div className=\"bg-white rounded-xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto\">\r\n            <div className=\"p-6\">\r\n              <div className=\"flex items-center justify-between mb-6\">\r\n                <h3 className=\"text-lg font-semibold text-gray-900\">Flow Details</h3>\r\n                <button\r\n                  onClick={() => setShowFlowModal(false)}\r\n                  className=\"p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg\"\r\n                >\r\n                  <XMarkIcon className=\"w-5 h-5\" />\r\n                </button>\r\n              </div>\r\n\r\n              <div className=\"space-y-6\">\r\n                {/* Flow Info */}\r\n                <div>\r\n                  <h4 className=\"font-medium text-gray-900 mb-3\">{typeof selectedFlow.name === 'string' ? selectedFlow.name : String(selectedFlow.name || 'Unnamed Flow')}</h4>\r\n                  {selectedFlow.description && (\r\n                    <p className=\"text-sm text-gray-600 mb-4\">{selectedFlow.description}</p>\r\n                  )}\r\n                  \r\n                  <div className=\"grid grid-cols-2 gap-4 text-sm\">\r\n                    <div>\r\n                      <span className=\"text-gray-600\">Status:</span>\r\n                      <span className={`ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${\r\n                        selectedFlow.is_active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'\r\n                      }`}>\r\n                        {selectedFlow.is_active ? 'Active' : 'Inactive'}\r\n                      </span>\r\n                    </div>\r\n                    <div>\r\n                      <span className=\"text-gray-600\">Device:</span>\r\n                      <span className=\"ml-2 text-gray-900\">{typeof selectedFlow.device_name === 'string' ? selectedFlow.device_name : String(selectedFlow.device_name || 'Unknown Device')}</span>\r\n                    </div>\r\n                    <div>\r\n                      <span className=\"text-gray-600\">Nodes:</span>\r\n                      <span className=\"ml-2 text-gray-900\">{selectedFlow.node_count || 0}</span>\r\n                    </div>\r\n                    <div>\r\n                      <span className=\"text-gray-600\">Conversations:</span>\r\n                      <span className=\"ml-2 text-gray-900\">{selectedFlow.conversation_count || 0}</span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Trigger Keywords */}\r\n                <div>\r\n                  <h4 className=\"font-medium text-gray-900 mb-2\">Trigger Keywords</h4>\r\n                  <div className=\"flex flex-wrap gap-2\">\r\n                    {(typeof selectedFlow.trigger_keywords === 'string' ? selectedFlow.trigger_keywords : String(selectedFlow.trigger_keywords || '')).split(',').map((keyword, index) => (\r\n                      <span\r\n                        key={index}\r\n                        className=\"inline-flex items-center px-2 py-1 rounded text-xs bg-blue-100 text-blue-800\"\r\n                      >\r\n                        {keyword.trim()}\r\n                      </span>\r\n                    ))}\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Timestamps */}\r\n                <div className=\"text-sm text-gray-500\">\r\n                  <p>Created: {new Date(selectedFlow.created_at).toLocaleString()}</p>\r\n                  <p>Updated: {new Date(selectedFlow.updated_at).toLocaleString()}</p>\r\n                  {selectedFlow.last_triggered && (\r\n                    <p>Last triggered: {new Date(selectedFlow.last_triggered).toLocaleString()}</p>\r\n                  )}\r\n                </div>\r\n              </div>\r\n\r\n              <div className=\"flex justify-end mt-6\">\r\n                <button\r\n                  onClick={() => setShowFlowModal(false)}\r\n                  className=\"btn-secondary\"\r\n                >\r\n                  Close\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Chatbot; "], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SACEC,8BAA8B,EAC9BC,QAAQ,EACRC,mBAAmB,EACnBC,UAAU,EACVC,SAAS,EACTC,QAAQ,EACRC,SAAS,EACTC,OAAO,EACPC,gBAAgB,EAChBC,aAAa,EACbC,OAAO,EACPC,SAAS,EACTC,eAAe,EACfC,WAAW,EACXC,uBAAuB,EACvBC,SAAS,EACTC,cAAc,EACdC,QAAQ,EACRC,SAAS,EACTC,aAAa,EACbC,aAAa,EACbC,YAAY,EACZC,kBAAkB,EAClBC,cAAc,EACdC,eAAe,EACfC,UAAU,EACVC,eAAe,EACfC,eAAe,EACfC,YAAY,EACZC,uBAAuB,EACvBC,iBAAiB,EACjBC,eAAe,QACV,6BAA6B;AACpC,SAASC,gBAAgB,QAAQ,oCAAoC;;AAErE;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,cAAc,GAAG;EACrBC,IAAI,EAAE;IACJC,IAAI,EAAE,cAAc;IACpBC,WAAW,EAAE,oCAAoC;IACjDC,KAAK,EAAE;EACT,CAAC;EACDC,KAAK,EAAE;IACLH,IAAI,EAAE,iBAAiB;IACvBC,WAAW,EAAE,oCAAoC;IACjDC,KAAK,EAAE;EACT,CAAC;EACDE,QAAQ,EAAE;IACRJ,IAAI,EAAE,oBAAoB;IAC1BC,WAAW,EAAE,uCAAuC;IACpDC,KAAK,EAAE;EACT,CAAC;EACDG,OAAO,EAAE;IACPL,IAAI,EAAE,mBAAmB;IACzBC,WAAW,EAAE,gCAAgC;IAC7CC,KAAK,EAAE;EACT,CAAC;EACDI,IAAI,EAAE;IACJN,IAAI,EAAE,gBAAgB;IACtBC,WAAW,EAAE,wCAAwC;IACrDC,KAAK,EAAE;EACT,CAAC;EACDK,OAAO,EAAE;IACPP,IAAI,EAAE,qBAAqB;IAC3BC,WAAW,EAAE,kCAAkC;IAC/CC,KAAK,EAAE;EACT,CAAC;EACD;EACA;EACA;EACA;EACA;EACA;EACAM,IAAI,EAAE;IACJR,IAAI,EAAE,kBAAkB;IACxBC,WAAW,EAAE,sCAAsC;IACnDC,KAAK,EAAE;EACT,CAAC;EAEDO,aAAa,EAAE;IACbT,IAAI,EAAE,2BAA2B;IACjCC,WAAW,EAAE,sFAAsF;IACnGC,KAAK,EAAE;EACT,CAAC;EACDQ,QAAQ,EAAE;IACRV,IAAI,EAAE,oBAAoB;IAC1BC,WAAW,EAAE,gCAAgC;IAC7CC,KAAK,EAAE;EACT,CAAC;EACDS,KAAK,EAAE;IACLX,IAAI,EAAE,iBAAiB;IACvBC,WAAW,EAAE,oCAAoC;IACjDC,KAAK,EAAE;EACT,CAAC;EACDU,KAAK,EAAE;IACLZ,IAAI,EAAE,iBAAiB;IACvBC,WAAW,EAAE,oCAAoC;IACjDC,KAAK,EAAE;EACT;AACF,CAAC;AAED,MAAMW,OAAO,GAAGA,CAAA,KAAM;EACpB,MAAM;IAAEC,WAAW;IAAEC,SAAS;IAAEC,WAAW;IAAEC;EAAQ,CAAC,GAAGtB,gBAAgB,CAAC,CAAC;EAC3E;EACA,MAAM,CAACuB,WAAW,EAAEC,cAAc,CAAC,GAAG3D,QAAQ,CAAC,IAAI,CAAC;;EAEpD;EACA,MAAM4D,UAAU,GAAGA,CAACC,KAAK,EAAEC,OAAO,GAAG,SAAS,KAAK;IACjD,IAAID,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKE,SAAS,EAAE;MACzC,OAAO,EAAE;IACX;IACA,IAAI,OAAOF,KAAK,KAAK,QAAQ,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAI,OAAOA,KAAK,KAAK,SAAS,EAAE;MACxF,OAAOA,KAAK;IACd;IACA,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAC7BG,OAAO,CAACC,KAAK,CAAC,+BAA+BH,OAAO,GAAG,EAAED,KAAK,CAAC;MAC/DG,OAAO,CAACE,KAAK,CAAC,uCAAuC,CAAC;MACtD,IAAIL,KAAK,CAACrB,IAAI,EAAE,OAAO2B,MAAM,CAACN,KAAK,CAACrB,IAAI,CAAC;MACzC,IAAIqB,KAAK,CAACO,YAAY,EAAE,OAAOD,MAAM,CAACN,KAAK,CAACO,YAAY,CAAC;MACzD,IAAIP,KAAK,CAACQ,KAAK,EAAE,OAAOF,MAAM,CAACN,KAAK,CAACQ,KAAK,CAAC;MAC3C,IAAIR,KAAK,CAACtB,IAAI,EAAE,OAAO4B,MAAM,CAACN,KAAK,CAACtB,IAAI,CAAC;MACzC,OAAO+B,IAAI,CAACC,SAAS,CAACV,KAAK,CAAC;IAC9B;IACA,OAAOM,MAAM,CAACN,KAAK,CAAC;EACtB,CAAC;EAED,MAAM,CAACW,KAAK,EAAEC,QAAQ,CAAC,GAAGzE,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC0E,QAAQ,EAAEC,WAAW,CAAC,GAAG3E,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC4E,SAAS,EAAEC,YAAY,CAAC,GAAG7E,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC8E,OAAO,EAAEC,UAAU,CAAC,GAAG/E,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACgF,UAAU,EAAEC,aAAa,CAAC,GAAGjF,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACkF,YAAY,EAAEC,eAAe,CAAC,GAAGnF,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACoF,eAAe,EAAEC,kBAAkB,CAAC,GAAGrF,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACsF,aAAa,EAAEC,gBAAgB,CAAC,GAAGvF,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACwF,aAAa,EAAEC,gBAAgB,CAAC,GAAGzF,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC0F,YAAY,EAAEC,eAAe,CAAC,GAAG3F,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC4F,QAAQ,EAAEC,WAAW,CAAC,GAAG7F,QAAQ,CAAC;IACvCwC,IAAI,EAAE,EAAE;IACRC,WAAW,EAAE,EAAE;IACfqD,SAAS,EAAE,EAAE;IACbC,eAAe,EAAE,EAAE;IACnBC,gBAAgB,EAAE,UAAU;IAC5BC,oBAAoB,EAAE,KAAK;IAC3BC,QAAQ,EAAE,IAAI;IACdC,eAAe,EAAE,CAAC;IAClBC,KAAK,EAAE;EACT,CAAC,CAAC;EACF,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGtG,QAAQ,CAAC;IACvCwC,IAAI,EAAE,EAAE;IACR+D,OAAO,EAAE,EAAE;IACXC,QAAQ,EAAE,SAAS;IACnBC,WAAW,EAAE,MAAM;IAAE;IACrBC,OAAO,EAAE,EAAE;IACXC,UAAU,EAAE,IAAI;IAChBC,UAAU,EAAE,EAAE;IACdC,SAAS,EAAE,CAAC,CAAC;IACbC,cAAc,EAAE,IAAI;IACpBC,cAAc,EAAE,OAAO;IAAE;IACzBC,eAAe,EAAE,EAAE;IAAE;IACrBC,UAAU,EAAE,EAAE;IAAE;IAChB;IACAC,UAAU,EAAE,SAAS;IACrBC,UAAU,EAAE,EAAE;IACdC,YAAY,EAAE,CAAC;IACfC,eAAe,EAAE,EAAE;IACnBC,YAAY,EAAE,EAAE;IAChBC,SAAS,EAAE,EAAE;IAAE;IACfC,aAAa,EAAE,EAAE;IAAE;IACnBC,cAAc,EAAE,EAAE;IAAE;IACpBC,WAAW,EAAE,EAAE;IAAE;IACjBC,SAAS,EAAE,MAAM;IAAE;IACnBC,UAAU,EAAE,EAAE;IAAE;IAChBC,OAAO,EAAE,EAAE;IAAE;IACb;IACAC,aAAa,EAAE,eAAe;IAC9BC,kBAAkB,EAAE,EAAE;IACtBC,iBAAiB,EAAE,EAAE;IACrBC,iBAAiB,EAAE,QAAQ;IAC3BC,cAAc,EAAE,EAAE;IAClBC,WAAW,EAAE,EAAE;IACfC,QAAQ,EAAE,IAAI;IAAE;IAChBC,SAAS,EAAE,IAAI,CAAC;EAClB,CAAC,CAAC;EAGF,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGvI,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACwI,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGzI,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAE5D;EACAC,SAAS,CAAC,MAAM;IACdyI,SAAS,CAAC,CAAC;IACXC,YAAY,CAAC,CAAC;IACdC,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,EAAE,CAAC;;EAIN;EACA3I,SAAS,CAAC,MAAM;IACd,IAAIoG,QAAQ,CAACG,QAAQ,KAAK,UAAU,EAAE;MACpC,IAAI,CAACqC,KAAK,CAACC,OAAO,CAACzC,QAAQ,CAACK,OAAO,CAAC,EAAE;QACpCJ,WAAW,CAACyC,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAErC,OAAO,EAAE;QAAG,CAAC,CAAC,CAAC;MACjD;IACF;EACF,CAAC,EAAE,CAACL,QAAQ,CAACG,QAAQ,CAAC,CAAC;EAEvB,MAAMkC,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAI;MACF3D,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMiE,QAAQ,GAAG,MAAMC,MAAM,CAACC,WAAW,CAACC,QAAQ,CAACC,KAAK,CACtD;AACR;AACA;AACA;AACA;AACA,qCACM,CAAC;MACD,IAAIJ,QAAQ,CAACK,OAAO,EAAE;QACpB5E,QAAQ,CAACuE,QAAQ,CAACM,IAAI,IAAI,EAAE,CAAC;MAC/B;IACF,CAAC,CAAC,OAAOrF,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACtD,CAAC,SAAS;MACRc,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM4D,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,MAAMK,QAAQ,GAAG,MAAMC,MAAM,CAACC,WAAW,CAACK,QAAQ,CAACC,WAAW,CAAC,CAAC;MAChExF,OAAO,CAACyF,GAAG,CAAC,oBAAoB,EAAET,QAAQ,CAAC;MAE3C,IAAIA,QAAQ,CAACK,OAAO,IAAIR,KAAK,CAACC,OAAO,CAACE,QAAQ,CAACtE,QAAQ,CAAC,EAAE;QACxD;QACA,MAAMgF,iBAAiB,GAAGV,QAAQ,CAACtE,QAAQ,CAACiF,MAAM,CAACC,OAAO,IAAI;UAC5D,MAAMC,WAAW,GAAGD,OAAO,CAACE,cAAc,KAAK,WAAW,IACvCF,OAAO,CAACG,MAAM,KAAK,WAAW,IAC9BH,OAAO,CAACI,UAAU,KAAK,IAAI;UAE9ChG,OAAO,CAACyF,GAAG,CAAC,WAAW,OAAOG,OAAO,CAAC9D,SAAS,KAAK,QAAQ,GAAG8D,OAAO,CAAC9D,SAAS,GAAI,OAAO8D,OAAO,CAACpH,IAAI,KAAK,QAAQ,GAAGoH,OAAO,CAACpH,IAAI,GAAG2B,MAAM,CAACyF,OAAO,CAAC9D,SAAS,IAAI8D,OAAO,CAACpH,IAAI,IAAI,SAAS,CAAE,GAAG,EAAE;YAChMuH,MAAM,EAAEH,OAAO,CAACG,MAAM;YACtBD,cAAc,EAAEF,OAAO,CAACE,cAAc;YACtCE,UAAU,EAAEJ,OAAO,CAACI,UAAU;YAC9BH;UACF,CAAC,CAAC;UAEF,OAAOA,WAAW;QACpB,CAAC,CAAC;QAEF7F,OAAO,CAACyF,GAAG,CAAC,eAAe,EAAET,QAAQ,CAACtE,QAAQ,CAACuF,MAAM,CAAC;QACtDjG,OAAO,CAACyF,GAAG,CAAC,qBAAqB,EAAEC,iBAAiB,CAACO,MAAM,CAAC;QAC5DtF,WAAW,CAAC+E,iBAAiB,CAAC;MAChC,CAAC,MAAM;QACL1F,OAAO,CAACkG,IAAI,CAAC,4BAA4B,EAAElB,QAAQ,CAAC;QACpDrE,WAAW,CAAC,EAAE,CAAC;MACjB;IACF,CAAC,CAAC,OAAOV,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CU,WAAW,CAAC,EAAE,CAAC;IACjB;EACF,CAAC;EAED,MAAMiE,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAMI,QAAQ,GAAG,MAAMC,MAAM,CAACC,WAAW,CAACC,QAAQ,CAACC,KAAK,CACtD;AACR;AACA;AACA,kDACM,CAAC;MACD,IAAIJ,QAAQ,CAACK,OAAO,EAAE;QACpBxE,YAAY,CAACmE,QAAQ,CAACM,IAAI,IAAI,EAAE,CAAC;MACnC;IACF,CAAC,CAAC,OAAOrF,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IAClD;EACF,CAAC;EAED,MAAMkG,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI,CAACvE,QAAQ,CAACpD,IAAI,CAAC4H,IAAI,CAAC,CAAC,IAAI,CAACxE,QAAQ,CAACG,eAAe,CAACqE,IAAI,CAAC,CAAC,IAAI,CAACxE,QAAQ,CAACE,SAAS,EAAE;MACpFvC,SAAS,CAAC,kBAAkB,EAAE,2EAA2E,CAAC;MAC1G;IACF;IAEA,IAAIqC,QAAQ,CAACQ,KAAK,CAAC6D,MAAM,KAAK,CAAC,EAAE;MAC/B1G,SAAS,CAAC,kBAAkB,EAAE,0CAA0C,CAAC;MACzE;IACF;IAEA,IAAI;MACFS,OAAO,CAACyF,GAAG,CAAC,qCAAqC,EAAE;QACjDjH,IAAI,EAAEoD,QAAQ,CAACpD,IAAI,CAAC4H,IAAI,CAAC,CAAC;QAC1B3H,WAAW,EAAEmD,QAAQ,CAACnD,WAAW,CAAC2H,IAAI,CAAC,CAAC,IAAI,IAAI;QAChDtE,SAAS,EAAEF,QAAQ,CAACE,SAAS;QAC7BC,eAAe,EAAEH,QAAQ,CAACG,eAAe,CAACqE,IAAI,CAAC,CAAC;QAChDlE,QAAQ,EAAEN,QAAQ,CAACM,QAAQ,GAAG,CAAC,GAAG,CAAC;QACnCC,eAAe,EAAEP,QAAQ,CAACO,eAAe,IAAI,CAAC;QAC9CkE,SAAS,EAAEzE,QAAQ,CAACQ,KAAK,CAAC6D;MAC5B,CAAC,CAAC;;MAEF;MACA,MAAMK,UAAU,GAAG,MAAMrB,MAAM,CAACC,WAAW,CAACC,QAAQ,CAACC,KAAK,CACxD;AACR;AACA,0EAA0E,EAClE,CACExD,QAAQ,CAACpD,IAAI,CAAC4H,IAAI,CAAC,CAAC,EACpBxE,QAAQ,CAACnD,WAAW,CAAC2H,IAAI,CAAC,CAAC,IAAI,IAAI,EACnCxE,QAAQ,CAACE,SAAS,EAClBF,QAAQ,CAACG,eAAe,CAACqE,IAAI,CAAC,CAAC,EAC/BxE,QAAQ,CAACM,QAAQ,GAAG,CAAC,GAAG,CAAC,EACzBN,QAAQ,CAACO,eAAe,IAAI,CAAC,CAEjC,CAAC;MAEDnC,OAAO,CAACyF,GAAG,CAAC,0BAA0B,EAAEa,UAAU,CAAC;MAEnD,IAAIA,UAAU,CAACjB,OAAO,EAAE;QACtB,MAAMkB,MAAM,GAAGD,UAAU,CAACE,QAAQ;;QAElC;QACA,MAAMC,SAAS,GAAG,IAAIC,GAAG,CAAC,CAAC,CAAC,CAAC;;QAE7B;QACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG/E,QAAQ,CAACQ,KAAK,CAAC6D,MAAM,EAAEU,CAAC,EAAE,EAAE;UAC9C,MAAMC,IAAI,GAAGhF,QAAQ,CAACQ,KAAK,CAACuE,CAAC,CAAC;UAC9B,MAAME,UAAU,GAAG,MAAM5B,MAAM,CAACC,WAAW,CAACC,QAAQ,CAACC,KAAK,CACxD;AACZ;AACA;AACA,0FAA0F,EAC9E,CACEmB,MAAM,EACNK,IAAI,CAACpI,IAAI,EACToI,IAAI,CAACrE,OAAO,EACZqE,IAAI,CAACpE,QAAQ,EACblC,IAAI,CAACC,SAAS,CAACqG,IAAI,CAAClE,OAAO,IAAI,EAAE,CAAC,EAClC,IAAI;UAAE;UACNiE,CAAC,GAAG,CAAC,EACLC,IAAI,CAAChE,UAAU,IAAI,IAAI,EACvBgE,IAAI,CAACE,cAAc,IAAI,IAAI,EAC3BF,IAAI,CAAC7D,cAAc,IAAI,IAAI,CAE/B,CAAC;UAED,IAAI8D,UAAU,CAACxB,OAAO,EAAE;YACtBoB,SAAS,CAACM,GAAG,CAACJ,CAAC,GAAG,CAAC,EAAEE,UAAU,CAACL,QAAQ,CAAC,CAAC,CAAC;UAC7C;QACF;;QAEA;QACA,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG/E,QAAQ,CAACQ,KAAK,CAAC6D,MAAM,EAAEU,CAAC,EAAE,EAAE;UAC9C,MAAMC,IAAI,GAAGhF,QAAQ,CAACQ,KAAK,CAACuE,CAAC,CAAC;UAC9B,IAAIC,IAAI,CAACjE,UAAU,IAAI,CAACqE,KAAK,CAACJ,IAAI,CAACjE,UAAU,CAAC,IAAIiE,IAAI,CAACjE,UAAU,IAAIf,QAAQ,CAACQ,KAAK,CAAC6D,MAAM,EAAE;YAC1F,MAAMgB,gBAAgB,GAAGR,SAAS,CAACS,GAAG,CAACC,QAAQ,CAACP,IAAI,CAACjE,UAAU,CAAC,CAAC;YACjE,IAAIsE,gBAAgB,EAAE;cACpB,MAAMG,aAAa,GAAGX,SAAS,CAACS,GAAG,CAACP,CAAC,GAAG,CAAC,CAAC;cAC1C,MAAM1B,MAAM,CAACC,WAAW,CAACC,QAAQ,CAACC,KAAK,CACrC,wDAAwD,EACxD,CAAC6B,gBAAgB,EAAEG,aAAa,CAClC,CAAC;YACH;UACF;QACF;QAEA/F,kBAAkB,CAAC,KAAK,CAAC;QACzBgG,aAAa,CAAC,CAAC;QACf,MAAM3C,SAAS,CAAC,CAAC;QACjBpF,WAAW,CAAC,cAAc,EAAE,oCAAoC,CAAC;MACnE,CAAC,MAAM;QACLC,SAAS,CAAC,iBAAiB,EAAE,iCAAiC,GAAG+G,UAAU,CAACrG,KAAK,CAAC;MACpF;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpDV,SAAS,CAAC,iBAAiB,EAAE,gDAAgD,CAAC;IAChF;EACF,CAAC;EAED,MAAM+H,gBAAgB,GAAG,MAAOf,MAAM,IAAK;IACzC,MAAMgB,SAAS,GAAG,MAAM9H,OAAO,CAAC,sEAAsE,EAAE,qBAAqB,CAAC;IAC9H,IAAI,CAAC8H,SAAS,EAAE;MACd;IACF;IAEA,IAAI;MACFvH,OAAO,CAACyF,GAAG,CAAC,sCAAsCc,MAAM,EAAE,CAAC;;MAE3D;MACA,MAAMiB,sBAAsB,GAAG,MAAMvC,MAAM,CAACC,WAAW,CAACC,QAAQ,CAACC,KAAK,CACpE,sHAAsH,EACtH,CAACmB,MAAM,CACT,CAAC;MACDvG,OAAO,CAACyF,GAAG,CAAC,aAAa+B,sBAAsB,CAACC,OAAO,IAAI,CAAC,kCAAkClB,MAAM,EAAE,CAAC;;MAEvG;MACA,MAAMtB,MAAM,CAACC,WAAW,CAACC,QAAQ,CAACC,KAAK,CACrC,iDAAiD,EACjD,CAACmB,MAAM,CACT,CAAC;;MAED;MACA,MAAMmB,iBAAiB,GAAG,MAAMzC,MAAM,CAACC,WAAW,CAACC,QAAQ,CAACC,KAAK,CAC/D,6CAA6C,EAC7C,CAACmB,MAAM,CACT,CAAC;MACDvG,OAAO,CAACyF,GAAG,CAAC,eAAeiC,iBAAiB,CAACD,OAAO,IAAI,CAAC,mBAAmBlB,MAAM,EAAE,CAAC;;MAErF;MACA,MAAMoB,MAAM,GAAG,MAAM1C,MAAM,CAACC,WAAW,CAACC,QAAQ,CAACC,KAAK,CACpD,wCAAwC,EACxC,CAACmB,MAAM,CACT,CAAC;MAED,IAAIoB,MAAM,CAACtC,OAAO,EAAE;QAClBrF,OAAO,CAACyF,GAAG,CAAC,uCAAuCc,MAAM,8BAA8B,CAAC;QACxF,MAAM7B,SAAS,CAAC,CAAC;QACjBpF,WAAW,CAAC,cAAc,EAAE,sEAAsE,CAAC;MACrG,CAAC,MAAM;QACLC,SAAS,CAAC,eAAe,EAAE,iCAAiC,GAAGoI,MAAM,CAAC1H,KAAK,CAAC;MAC9E;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpDV,SAAS,CAAC,eAAe,EAAE,gDAAgD,CAAC;IAC9E;EACF,CAAC;EAED,MAAMqI,gBAAgB,GAAG,MAAAA,CAAOrB,MAAM,EAAEsB,aAAa,KAAK;IACxD,IAAI;MACF;MACA,IAAIA,aAAa,EAAE;QACjB7H,OAAO,CAACyF,GAAG,CAAC,qBAAqBc,MAAM,kCAAkC,CAAC;QAC1E,MAAMiB,sBAAsB,GAAG,MAAMvC,MAAM,CAACC,WAAW,CAACC,QAAQ,CAACC,KAAK,CACpE,sHAAsH,EACtH,CAACmB,MAAM,CACT,CAAC;QACDvG,OAAO,CAACyF,GAAG,CAAC,YAAY+B,sBAAsB,CAACC,OAAO,IAAI,CAAC,kCAAkClB,MAAM,EAAE,CAAC;MACxG;MAEA,MAAMoB,MAAM,GAAG,MAAM1C,MAAM,CAACC,WAAW,CAACC,QAAQ,CAACC,KAAK,CACpD,qFAAqF,EACrF,CAACyC,aAAa,GAAG,CAAC,GAAG,CAAC,EAAEtB,MAAM,CAChC,CAAC;MAED,IAAIoB,MAAM,CAACtC,OAAO,EAAE;QAClB,MAAMX,SAAS,CAAC,CAAC;QACjB,MAAMoD,UAAU,GAAGD,aAAa,GAAG,UAAU,GAAG,SAAS;QACzDvI,WAAW,CAAC,gBAAgB,EAAE,QAAQwI,UAAU,gBAAgBD,aAAa,GAAG,iCAAiC,GAAG,EAAE,EAAE,CAAC;MAC3H,CAAC,MAAM;QACLtI,SAAS,CAAC,eAAe,EAAE,gCAAgC,GAAGoI,MAAM,CAAC1H,KAAK,CAAC;MAC7E;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnDV,SAAS,CAAC,eAAe,EAAE,+CAA+C,CAAC;IAC7E;EACF,CAAC;EAED,MAAMwI,cAAc,GAAG,MAAOC,IAAI,IAAK;IACrC,IAAI;MACF;MACA,MAAMC,WAAW,GAAG,MAAMhD,MAAM,CAACC,WAAW,CAACC,QAAQ,CAACC,KAAK,CACzD,iEAAiE,EACjE,CAAC4C,IAAI,CAACE,EAAE,CACV,CAAC;MAED,IAAID,WAAW,CAAC5C,OAAO,EAAE;QACvB;QACA,MAAM8C,eAAe,GAAG,IAAIzB,GAAG,CAAC,CAAC;QACjCuB,WAAW,CAAC3C,IAAI,CAAC8C,OAAO,CAACxB,IAAI,IAAI;UAC/BuB,eAAe,CAACpB,GAAG,CAACH,IAAI,CAACsB,EAAE,EAAEtB,IAAI,CAACyB,QAAQ,CAAC;QAC7C,CAAC,CAAC;QAEF,MAAMjG,KAAK,GAAG6F,WAAW,CAAC3C,IAAI,CAACgD,GAAG,CAAC,CAAC1B,IAAI,EAAE2B,KAAK,KAAK;UAClD;UACA,IAAI5F,UAAU,GAAG,IAAI;UACrB,IAAIiE,IAAI,CAAC4B,YAAY,EAAE;YACrB;YACA,MAAMC,kBAAkB,GAAGN,eAAe,CAACjB,GAAG,CAACN,IAAI,CAAC4B,YAAY,CAAC;YACjE,IAAIC,kBAAkB,EAAE;cACtB9F,UAAU,GAAG8F,kBAAkB;YACjC;UACF;UAEA,OAAO;YACLP,EAAE,EAAEtB,IAAI,CAACsB,EAAE;YACX1J,IAAI,EAAEoI,IAAI,CAACpI,IAAI;YACf+D,OAAO,EAAEqE,IAAI,CAACrE,OAAO;YACrBC,QAAQ,EAAEoE,IAAI,CAAC8B,SAAS;YACxBhG,OAAO,EAAEkE,IAAI,CAAClE,OAAO,GAAGpC,IAAI,CAACqI,KAAK,CAAC/B,IAAI,CAAClE,OAAO,CAAC,GAAG,EAAE;YACrDC,UAAU,EAAEA,UAAU;YACtBC,UAAU,EAAEgE,IAAI,CAACgC,WAAW;YAC5B9B,cAAc,EAAEF,IAAI,CAACiC,eAAe;YACpC9F,cAAc,EAAE6D,IAAI,CAACkC,eAAe;YACpC9F,eAAe,EAAE4D,IAAI,CAAClE,OAAO,IAAI,OAAOpC,IAAI,CAACqI,KAAK,CAAC/B,IAAI,CAAClE,OAAO,CAAC,KAAK,QAAQ,IAAI,CAACmC,KAAK,CAACC,OAAO,CAACxE,IAAI,CAACqI,KAAK,CAAC/B,IAAI,CAAClE,OAAO,CAAC,CAAC,GACrHpC,IAAI,CAACqI,KAAK,CAAC/B,IAAI,CAAClE,OAAO,CAAC,CAACqG,gBAAgB,IAAI,EAAE,GAC/C,EAAE;YACN9F,UAAU,EAAE2D,IAAI,CAAC8B,SAAS,KAAK,WAAW,IAAI9B,IAAI,CAAClE,OAAO,GAAGpC,IAAI,CAACqI,KAAK,CAAC/B,IAAI,CAAClE,OAAO,CAAC,GAAG;UAC1F,CAAC;QACH,CAAC,CAAC;;QAEF;QACAb,WAAW,CAAC;UACVqG,EAAE,EAAEF,IAAI,CAACE,EAAE;UAAE;UACb1J,IAAI,EAAEwJ,IAAI,CAACxJ,IAAI;UACfC,WAAW,EAAEuJ,IAAI,CAACvJ,WAAW,IAAI,EAAE;UACnCqD,SAAS,EAAEkG,IAAI,CAACgB,UAAU;UAC1BjH,eAAe,EAAEiG,IAAI,CAACiB,gBAAgB;UACtC/G,QAAQ,EAAE8F,IAAI,CAACkB,SAAS;UACxB/G,eAAe,EAAE6F,IAAI,CAACmB,gBAAgB,IAAI,CAAC;UAC3C/G,KAAK,EAAEA;QACT,CAAC,CAAC;QAEFb,gBAAgB,CAAC,IAAI,CAAC;MACxB,CAAC,MAAM;QACLhC,SAAS,CAAC,aAAa,EAAE,6BAA6B,GAAG0I,WAAW,CAAChI,KAAK,CAAC;MAC7E;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvDV,SAAS,CAAC,aAAa,EAAE,mDAAmD,CAAC;IAC/E;EACF,CAAC;EAED,MAAM6J,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI,CAACxH,QAAQ,CAACpD,IAAI,CAAC4H,IAAI,CAAC,CAAC,IAAI,CAACxE,QAAQ,CAACG,eAAe,CAACqE,IAAI,CAAC,CAAC,IAAI,CAACxE,QAAQ,CAACE,SAAS,EAAE;MACpFvC,SAAS,CAAC,kBAAkB,EAAE,2EAA2E,CAAC;MAC1G;IACF;IAEA,IAAIqC,QAAQ,CAACQ,KAAK,CAAC6D,MAAM,KAAK,CAAC,EAAE;MAC/B1G,SAAS,CAAC,kBAAkB,EAAE,0CAA0C,CAAC;MACzE;IACF;IAEA,IAAI;MACF;MACA,MAAM+G,UAAU,GAAG,MAAMrB,MAAM,CAACC,WAAW,CAACC,QAAQ,CAACC,KAAK,CACxD;AACR;AACA;AACA,qBAAqB,EACb,CACExD,QAAQ,CAACpD,IAAI,CAAC4H,IAAI,CAAC,CAAC,EACpBxE,QAAQ,CAACnD,WAAW,CAAC2H,IAAI,CAAC,CAAC,IAAI,IAAI,EACnCxE,QAAQ,CAACE,SAAS,EAClBF,QAAQ,CAACG,eAAe,CAACqE,IAAI,CAAC,CAAC,EAC/BxE,QAAQ,CAACM,QAAQ,GAAG,CAAC,GAAG,CAAC,EACzBN,QAAQ,CAACO,eAAe,IAAI,CAAC,EAC7BP,QAAQ,CAACsG,EAAE,CAEf,CAAC;MAED,IAAI5B,UAAU,CAACjB,OAAO,EAAE;QACtB;QACA,MAAMJ,MAAM,CAACC,WAAW,CAACC,QAAQ,CAACC,KAAK,CACrC,sHAAsH,EACtH,CAACxD,QAAQ,CAACsG,EAAE,CACd,CAAC;;QAED;QACA,MAAMjD,MAAM,CAACC,WAAW,CAACC,QAAQ,CAACC,KAAK,CACrC,6CAA6C,EAC7C,CAACxD,QAAQ,CAACsG,EAAE,CACd,CAAC;;QAED;QACA,MAAMzB,SAAS,GAAG,IAAIC,GAAG,CAAC,CAAC,CAAC,CAAC;;QAE7B;QACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG/E,QAAQ,CAACQ,KAAK,CAAC6D,MAAM,EAAEU,CAAC,EAAE,EAAE;UAC9C,MAAMC,IAAI,GAAGhF,QAAQ,CAACQ,KAAK,CAACuE,CAAC,CAAC;UAC9B,MAAME,UAAU,GAAG,MAAM5B,MAAM,CAACC,WAAW,CAACC,QAAQ,CAACC,KAAK,CACxD;AACZ;AACA;AACA,0FAA0F,EAC9E,CACExD,QAAQ,CAACsG,EAAE,EACXtB,IAAI,CAACpI,IAAI,EACToI,IAAI,CAACrE,OAAO,EACZqE,IAAI,CAACpE,QAAQ,EACblC,IAAI,CAACC,SAAS,CAACqG,IAAI,CAAClE,OAAO,IAAI,EAAE,CAAC,EAClC,IAAI;UAAE;UACNiE,CAAC,GAAG,CAAC,EACLC,IAAI,CAAChE,UAAU,IAAI,IAAI,EACvBgE,IAAI,CAACE,cAAc,IAAI,IAAI,EAC3BF,IAAI,CAAC7D,cAAc,IAAI,IAAI,CAE/B,CAAC;UAED,IAAI8D,UAAU,CAACxB,OAAO,EAAE;YACtBoB,SAAS,CAACM,GAAG,CAACJ,CAAC,GAAG,CAAC,EAAEE,UAAU,CAACL,QAAQ,CAAC,CAAC,CAAC;UAC7C;QACF;;QAEA;QACA,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG/E,QAAQ,CAACQ,KAAK,CAAC6D,MAAM,EAAEU,CAAC,EAAE,EAAE;UAC9C,MAAMC,IAAI,GAAGhF,QAAQ,CAACQ,KAAK,CAACuE,CAAC,CAAC;UAC9B,IAAIC,IAAI,CAACjE,UAAU,IAAI,CAACqE,KAAK,CAACJ,IAAI,CAACjE,UAAU,CAAC,IAAIiE,IAAI,CAACjE,UAAU,IAAIf,QAAQ,CAACQ,KAAK,CAAC6D,MAAM,EAAE;YAC1F,MAAMgB,gBAAgB,GAAGR,SAAS,CAACS,GAAG,CAACC,QAAQ,CAACP,IAAI,CAACjE,UAAU,CAAC,CAAC;YACjE,IAAIsE,gBAAgB,EAAE;cACpB,MAAMG,aAAa,GAAGX,SAAS,CAACS,GAAG,CAACP,CAAC,GAAG,CAAC,CAAC;cAC1C,MAAM1B,MAAM,CAACC,WAAW,CAACC,QAAQ,CAACC,KAAK,CACrC,wDAAwD,EACxD,CAAC6B,gBAAgB,EAAEG,aAAa,CAClC,CAAC;YACH;UACF;QACF;QAEA9H,WAAW,CAAC,cAAc,EAAE,oCAAoC,CAAC;QACjEiC,gBAAgB,CAAC,KAAK,CAAC;QACvB8F,aAAa,CAAC,CAAC;QACf,MAAM3C,SAAS,CAAC,CAAC;MACnB,CAAC,MAAM;QACLnF,SAAS,CAAC,eAAe,EAAE,iCAAiC,GAAG+G,UAAU,CAACrG,KAAK,CAAC;MAClF;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpDV,SAAS,CAAC,eAAe,EAAE,gDAAgD,CAAC;IAC9E;EACF,CAAC;EAED,MAAM8J,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI,CAAChH,QAAQ,CAAC7D,IAAI,CAAC4H,IAAI,CAAC,CAAC,IAAI,CAAC/D,QAAQ,CAACE,OAAO,CAAC6D,IAAI,CAAC,CAAC,EAAE;MACrD7G,SAAS,CAAC,kBAAkB,EAAE,sCAAsC,CAAC;MACrE;IACF;IAEA,IAAIuH,cAAc,GAAG,IAAI;IACzB,IAAI/D,cAAc,GAAG,IAAI;;IAEzB;IACA,IAAIV,QAAQ,CAACS,cAAc,EAAE;MAC3B,IAAI;QACF;QACA,MAAMwG,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;QAC/B,MAAMC,QAAQ,GAAG,MAAM,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;UACtDL,MAAM,CAACM,MAAM,GAAG,MAAMF,OAAO,CAACJ,MAAM,CAAC3B,MAAM,CAAC;UAC5C2B,MAAM,CAACO,OAAO,GAAGF,MAAM;UACvBL,MAAM,CAACQ,aAAa,CAACzH,QAAQ,CAACS,cAAc,CAAC;QAC/C,CAAC,CAAC;QAEFgE,cAAc,GAAG0C,QAAQ;QACzBzG,cAAc,GAAGV,QAAQ,CAACU,cAAc;MAC1C,CAAC,CAAC,OAAO9C,KAAK,EAAE;QACdV,SAAS,CAAC,kBAAkB,EAAE,+BAA+B,GAAGU,KAAK,CAACsC,OAAO,CAAC;QAC9E;MACF;IACF;;IAEA;IACA,IAAIwH,WAAW,GAAG,CAAC,CAAC;IACpB,IAAI1H,QAAQ,CAACG,QAAQ,KAAK,UAAU,EAAE;MACpC;MACA,MAAMwH,eAAe,GAAG3H,QAAQ,CAACK,OAAO,CAACiD,MAAM,CAACsE,GAAG,IAAIA,GAAG,CAAC7D,IAAI,CAAC,CAAC,CAAC;MAClE,MAAM8D,kBAAkB,GAAGF,eAAe,CAAC1B,GAAG,CAAC,CAAC6B,MAAM,EAAE5B,KAAK,MAAM;QACjEnI,YAAY,EAAE+J,MAAM,CAAC/D,IAAI,CAAC,CAAC;QAC3B8B,EAAE,EAAE,UAAUK,KAAK,GAAG,CAAC,EAAE;QACzBlI,KAAK,EAAE8J,MAAM,CAAC/D,IAAI,CAAC;MACrB,CAAC,CAAC,CAAC;MAEH2D,WAAW,GAAG;QACZrH,OAAO,EAAEwH,kBAAkB;QAC3BnB,gBAAgB,EAAE1G,QAAQ,CAACW,eAAe,IAAI,IAAI;QAClDoH,gBAAgB,EAAE/H,QAAQ,CAACgI,eAAe,IAAI;MAChD,CAAC;IACH,CAAC,MAAM,IAAIhI,QAAQ,CAACG,QAAQ,KAAK,QAAQ,EAAE;MACzC;MACAuH,WAAW,GAAG;QACZO,WAAW,EAAEjI,QAAQ,CAACa,UAAU;QAChCqH,WAAW,EAAElI,QAAQ,CAACc,UAAU,IAAI,IAAI;QACxCqH,aAAa,EAAEnI,QAAQ,CAACe,YAAY,IAAI,CAAC;QACzCqH,gBAAgB,EAAEpI,QAAQ,CAACgB,eAAe,IAAI,IAAI;QAClDqH,aAAa,EAAErI,QAAQ,CAACiB,YAAY,IAAI,IAAI;QAC5CqH,UAAU,EAAEtI,QAAQ,CAACkB,SAAS,IAAI,IAAI;QACtCqH,cAAc,EAAEvI,QAAQ,CAACmB,aAAa,IAAI,IAAI;QAC9CqH,gBAAgB,EAAExI,QAAQ,CAACoB,cAAc,IAAI,EAAE;QAC/CqH,YAAY,EAAEzI,QAAQ,CAACqB,WAAW,IAAI,IAAI;QAC1CqH,UAAU,EAAE1I,QAAQ,CAACsB,SAAS,IAAI,MAAM;QACxCqH,WAAW,EAAE3I,QAAQ,CAACuB,UAAU,IAAI,IAAI;QACxCqH,QAAQ,EAAE5I,QAAQ,CAACwB,OAAO,IAAI;MAChC,CAAC;IACH,CAAC,MAAM,IAAIxB,QAAQ,CAACG,QAAQ,KAAK,WAAW,EAAE;MAC5C;MACAuH,WAAW,GAAG;QACZmB,cAAc,EAAE7I,QAAQ,CAACyB,aAAa;QACtCqH,mBAAmB,EAAE9I,QAAQ,CAAC0B,kBAAkB,IAAI,EAAE;QACtDqH,kBAAkB,EAAE/I,QAAQ,CAAC2B,iBAAiB,IAAI,IAAI;QACtDqH,kBAAkB,EAAEhJ,QAAQ,CAAC4B,iBAAiB,IAAI,QAAQ;QAC1DqH,eAAe,EAAEjJ,QAAQ,CAAC6B,cAAc,IAAI,IAAI;QAChDqH,YAAY,EAAElJ,QAAQ,CAAC8B,WAAW,IAAI,EAAE;QACxCqH,SAAS,EAAEnJ,QAAQ,CAAC+B,QAAQ,IAAI,IAAI;QACpCqH,UAAU,EAAEpJ,QAAQ,CAACgC,SAAS,IAAI;MACpC,CAAC;IACH,CAAC,MAAM;MACL0F,WAAW,GAAG1H,QAAQ,CAACK,OAAO,CAACiD,MAAM,CAACsE,GAAG,IAAIA,GAAG,CAAC7D,IAAI,CAAC,CAAC,CAAC;IAC1D;IAEA,MAAMsF,OAAO,GAAG;MACdxD,EAAE,EAAEyD,IAAI,CAACC,GAAG,CAAC,CAAC;MAAE;MAChBpN,IAAI,EAAE6D,QAAQ,CAAC7D,IAAI,CAAC4H,IAAI,CAAC,CAAC;MAC1B7D,OAAO,EAAEF,QAAQ,CAACE,OAAO,CAAC6D,IAAI,CAAC,CAAC;MAChC5D,QAAQ,EAAEH,QAAQ,CAACG,QAAQ;MAC3BE,OAAO,EAAEqH,WAAW;MACpBpH,UAAU,EAAEN,QAAQ,CAACM,UAAU;MAC/BC,UAAU,EAAEP,QAAQ,CAACO,UAAU,IAAI,IAAI;MACvCkE,cAAc,EAAEA,cAAc;MAC9B/D,cAAc,EAAEA,cAAc;MAC9BC,eAAe,EAAEX,QAAQ,CAACW,eAAe,IAAI,IAAI;MACjDC,UAAU,EAAEZ,QAAQ,CAACY,UAAU,IAAI;IACrC,CAAC;IAED,IAAIuB,gBAAgB,IAAI,CAAC,EAAE;MACzB;MACA,MAAMqH,YAAY,GAAG,CAAC,GAAGjK,QAAQ,CAACQ,KAAK,CAAC;MACxCyJ,YAAY,CAACrH,gBAAgB,CAAC,GAAGkH,OAAO;MACxC7J,WAAW,CAACkD,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE3C,KAAK,EAAEyJ;MAAa,CAAC,CAAC,CAAC;MACvDpH,mBAAmB,CAAC,CAAC,CAAC,CAAC;IACzB,CAAC,MAAM;MACL;MACA5C,WAAW,CAACkD,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE3C,KAAK,EAAE,CAAC,GAAG2C,IAAI,CAAC3C,KAAK,EAAEsJ,OAAO;MAAE,CAAC,CAAC,CAAC;IACrE;IAEAnH,gBAAgB,CAAC,KAAK,CAAC;IACvBuH,aAAa,CAAC,CAAC;EACjB,CAAC;EAED,MAAMC,cAAc,GAAIxD,KAAK,IAAK;IAChC,MAAM3B,IAAI,GAAGhF,QAAQ,CAACQ,KAAK,CAACmG,KAAK,CAAC;;IAElC;IACA,IAAIwB,WAAW,GAAG,EAAE;IACpB,IAAI/G,eAAe,GAAG,EAAE;IACxB,IAAIqH,eAAe,GAAG,SAAS;IAC/B,IAAI2B,YAAY,GAAG,CAAC,CAAC;IACrB,IAAIC,eAAe,GAAG,CAAC,CAAC;IAExB,IAAIrF,IAAI,CAACpE,QAAQ,KAAK,UAAU,EAAE;MAChC,IAAIqC,KAAK,CAACC,OAAO,CAAC8B,IAAI,CAAClE,OAAO,CAAC,EAAE;QAC/B;QACAqH,WAAW,GAAGnD,IAAI,CAAClE,OAAO,CAAC4F,GAAG,CAAC6B,MAAM,IACnC,OAAOA,MAAM,KAAK,QAAQ,GAAGA,MAAM,GAAI,OAAOA,MAAM,KAAK,QAAQ,IAAIA,MAAM,GAAIA,MAAM,CAAC/J,YAAY,IAAI+J,MAAM,CAAC9J,KAAK,IAAI8J,MAAM,CAAC3L,IAAI,IAAI2L,MAAM,CAAC5L,IAAI,IAAI,EAAE,GAAI,EAC5J,CAAC;MACH,CAAC,MAAM,IAAI,OAAOqI,IAAI,CAAClE,OAAO,KAAK,QAAQ,IAAIkE,IAAI,CAAClE,OAAO,EAAE;QAC3D;QACA,MAAMwJ,YAAY,GAAGtF,IAAI,CAAClE,OAAO,CAACA,OAAO,IAAI,EAAE;QAC/CqH,WAAW,GAAGmC,YAAY,CAAC5D,GAAG,CAAC6B,MAAM,IACnC,OAAOA,MAAM,KAAK,QAAQ,GAAGA,MAAM,GAAI,OAAOA,MAAM,KAAK,QAAQ,IAAIA,MAAM,GAAIA,MAAM,CAAC/J,YAAY,IAAI+J,MAAM,CAAC9J,KAAK,IAAI8J,MAAM,CAAC3L,IAAI,IAAI2L,MAAM,CAAC5L,IAAI,IAAI,EAAE,GAAI,EAC5J,CAAC;QACDyE,eAAe,GAAG4D,IAAI,CAAClE,OAAO,CAACqG,gBAAgB,IAAI,EAAE;QACrDsB,eAAe,GAAGzD,IAAI,CAAClE,OAAO,CAAC0H,gBAAgB,IAAI,SAAS;MAC9D;IACF,CAAC,MAAM,IAAIxD,IAAI,CAACpE,QAAQ,KAAK,QAAQ,EAAE;MACrC;MACA,IAAI,OAAOoE,IAAI,CAAClE,OAAO,KAAK,QAAQ,IAAIkE,IAAI,CAAClE,OAAO,EAAE;QACpDsJ,YAAY,GAAG;UACb9I,UAAU,EAAE0D,IAAI,CAAClE,OAAO,CAAC4H,WAAW,IAAI,SAAS;UACjDnH,UAAU,EAAEyD,IAAI,CAAClE,OAAO,CAAC6H,WAAW,IAAI,EAAE;UAC1CnH,YAAY,EAAEwD,IAAI,CAAClE,OAAO,CAAC8H,aAAa,IAAI,CAAC;UAC7CnH,eAAe,EAAEuD,IAAI,CAAClE,OAAO,CAAC+H,gBAAgB,IAAI,EAAE;UACpDnH,YAAY,EAAEsD,IAAI,CAAClE,OAAO,CAACgI,aAAa,IAAI,EAAE;UAC9CnH,SAAS,EAAEqD,IAAI,CAAClE,OAAO,CAACiI,UAAU,IAAI,EAAE;UACxCnH,aAAa,EAAEoD,IAAI,CAAClE,OAAO,CAACkI,cAAc,IAAI,EAAE;UAChDnH,cAAc,EAAEmD,IAAI,CAAClE,OAAO,CAACmI,gBAAgB,IAAI,EAAE;UACnDnH,WAAW,EAAEkD,IAAI,CAAClE,OAAO,CAACoI,YAAY,IAAI,EAAE;UAC5CnH,SAAS,EAAEiD,IAAI,CAAClE,OAAO,CAACqI,UAAU,IAAI,MAAM;UAC5CnH,UAAU,EAAEgD,IAAI,CAAClE,OAAO,CAACsI,WAAW,IAAI,EAAE;UAC1CnH,OAAO,EAAE+C,IAAI,CAAClE,OAAO,CAACuI,QAAQ,IAAI;QACpC,CAAC;MACH;IACF,CAAC,MAAM,IAAIrE,IAAI,CAACpE,QAAQ,KAAK,WAAW,EAAE;MACxC;MACA,IAAI,OAAOoE,IAAI,CAAClE,OAAO,KAAK,QAAQ,IAAIkE,IAAI,CAAClE,OAAO,EAAE;QACpDuJ,eAAe,GAAG;UAChBnI,aAAa,EAAE8C,IAAI,CAAClE,OAAO,CAACwI,cAAc,IAAI,eAAe;UAC7DnH,kBAAkB,EAAE6C,IAAI,CAAClE,OAAO,CAACyI,mBAAmB,IAAI,EAAE;UAC1DnH,iBAAiB,EAAE4C,IAAI,CAAClE,OAAO,CAAC0I,kBAAkB,IAAI,EAAE;UACxDnH,iBAAiB,EAAE2C,IAAI,CAAClE,OAAO,CAAC2I,kBAAkB,IAAI,QAAQ;UAC9DnH,cAAc,EAAE0C,IAAI,CAAClE,OAAO,CAAC4I,eAAe,IAAI,EAAE;UAClDnH,WAAW,EAAEyC,IAAI,CAAClE,OAAO,CAAC6I,YAAY,IAAI,EAAE;UAC5CnH,QAAQ,EAAEwC,IAAI,CAAClE,OAAO,CAAC8I,SAAS,IAAI,IAAI;UACxCnH,SAAS,EAAEuC,IAAI,CAAClE,OAAO,CAAC+I,UAAU,IAAI;QACxC,CAAC;MACH;IACF,CAAC,MAAM;MACL;MACA,IAAI5G,KAAK,CAACC,OAAO,CAAC8B,IAAI,CAAClE,OAAO,CAAC,EAAE;QAC/BqH,WAAW,GAAGnD,IAAI,CAAClE,OAAO;MAC5B;IACF;IAEAJ,WAAW,CAAC;MACV9D,IAAI,EAAEoI,IAAI,CAACpI,IAAI;MACf+D,OAAO,EAAEqE,IAAI,CAACrE,OAAO;MACrBC,QAAQ,EAAEoE,IAAI,CAACpE,QAAQ;MACvBE,OAAO,EAAEqH,WAAW;MACpBpH,UAAU,EAAEiE,IAAI,CAACjE,UAAU;MAC3BC,UAAU,EAAEgE,IAAI,CAAChE,UAAU,IAAI,EAAE;MACjCI,eAAe,EAAEA,eAAe;MAChCqH,eAAe,EAAEA,eAAe;MAChC5H,WAAW,EAAE,MAAM;MACnBI,SAAS,EAAE,CAAC,CAAC;MACbC,cAAc,EAAE,IAAI;MACpBC,cAAc,EAAE,OAAO;MACvBE,UAAU,EAAE,EAAE;MACd;MACAC,UAAU,EAAE8I,YAAY,CAAC9I,UAAU,IAAI,SAAS;MAChDC,UAAU,EAAE6I,YAAY,CAAC7I,UAAU,IAAI,EAAE;MACzCC,YAAY,EAAE4I,YAAY,CAAC5I,YAAY,IAAI,CAAC;MAC5CC,eAAe,EAAE2I,YAAY,CAAC3I,eAAe,IAAI,EAAE;MACnDC,YAAY,EAAE0I,YAAY,CAAC1I,YAAY,IAAI,EAAE;MAC7CC,SAAS,EAAEyI,YAAY,CAACzI,SAAS,IAAI,EAAE;MACvCC,aAAa,EAAEwI,YAAY,CAACxI,aAAa,IAAI,EAAE;MAC/CC,cAAc,EAAEuI,YAAY,CAACvI,cAAc,IAAI,EAAE;MACjDC,WAAW,EAAEsI,YAAY,CAACtI,WAAW,IAAI,EAAE;MAC3CC,SAAS,EAAEqI,YAAY,CAACrI,SAAS,IAAI,MAAM;MAC3CC,UAAU,EAAEoI,YAAY,CAACpI,UAAU,IAAI,EAAE;MACzCC,OAAO,EAAEmI,YAAY,CAACnI,OAAO,IAAI,EAAE;MACnC;MACAC,aAAa,EAAEmI,eAAe,CAACnI,aAAa,IAAI,eAAe;MAC/DC,kBAAkB,EAAEkI,eAAe,CAAClI,kBAAkB,IAAI,EAAE;MAC5DC,iBAAiB,EAAEiI,eAAe,CAACjI,iBAAiB,IAAI,EAAE;MAC1DC,iBAAiB,EAAEgI,eAAe,CAAChI,iBAAiB,IAAI,QAAQ;MAChEC,cAAc,EAAE+H,eAAe,CAAC/H,cAAc,IAAI,EAAE;MACpDC,WAAW,EAAE8H,eAAe,CAAC9H,WAAW,IAAI,EAAE;MAC9CC,QAAQ,EAAE6H,eAAe,CAAC7H,QAAQ,IAAI,IAAI;MAC1CC,SAAS,EAAE4H,eAAe,CAAC5H,SAAS,IAAI;IAC1C,CAAC,CAAC;IACFI,mBAAmB,CAAC8D,KAAK,CAAC;IAC1BhE,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC;EAED,MAAM4H,gBAAgB,GAAG,MAAO5D,KAAK,IAAK;IACxC,MAAMhB,SAAS,GAAG,MAAM9H,OAAO,CAAC,4CAA4C,EAAE,aAAa,CAAC;IAC5F,IAAI8H,SAAS,EAAE;MACb,MAAMsE,YAAY,GAAGjK,QAAQ,CAACQ,KAAK,CAACuD,MAAM,CAAC,CAACyG,CAAC,EAAEzF,CAAC,KAAKA,CAAC,KAAK4B,KAAK,CAAC;MACjE1G,WAAW,CAACkD,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE3C,KAAK,EAAEyJ;MAAa,CAAC,CAAC,CAAC;IACzD;EACF,CAAC;EAED,MAAMQ,oBAAoB,GAAIzJ,UAAU,IAAK;IAC3C,MAAM0J,aAAa,GAAGzH,KAAK,CAACC,OAAO,CAAClE,SAAS,CAAC,GAAGA,SAAS,GAAG,EAAE;IAC/D,MAAM2L,QAAQ,GAAGD,aAAa,CAACE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACvE,EAAE,KAAKf,QAAQ,CAACvE,UAAU,CAAC,CAAC;IACvE,IAAI2J,QAAQ,EAAE;MACZ;MACA,IAAI1J,SAAS,GAAG,EAAE;MAClB,IAAI;QACFA,SAAS,GAAGvC,IAAI,CAACqI,KAAK,CAAC4D,QAAQ,CAAC1J,SAAS,IAAI,IAAI,CAAC;QAClD,IAAI,CAACgC,KAAK,CAACC,OAAO,CAACjC,SAAS,CAAC,EAAE;UAC7BA,SAAS,GAAG,EAAE;QAChB;MACF,CAAC,CAAC,OAAO5C,KAAK,EAAE;QACdD,OAAO,CAACC,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;QACzD4C,SAAS,GAAG,EAAE;MAChB;MAEA,MAAM6J,YAAY,GAAG,CAAC,CAAC;MACvB7J,SAAS,CAACuF,OAAO,CAACuE,QAAQ,IAAI;QAC5B;QACA,MAAMC,OAAO,GAAG,OAAOD,QAAQ,KAAK,QAAQ,GAAGA,QAAQ,GAAGxM,MAAM,CAACwM,QAAQ,IAAI,EAAE,CAAC;QAChF,IAAIC,OAAO,EAAE;UACXF,YAAY,CAACE,OAAO,CAAC,GAAG,KAAKA,OAAO,IAAI;QAC1C;MACF,CAAC,CAAC;MAEFtK,WAAW,CAACyC,IAAI,KAAK;QACnB,GAAGA,IAAI;QACPnC,UAAU,EAAEA,UAAU;QACtBL,OAAO,EAAEgK,QAAQ,CAACM,OAAO,IAAI,EAAE;QAC/BhK,SAAS,EAAE6J;MACb,CAAC,CAAC,CAAC;IACL,CAAC,MAAM;MACLpK,WAAW,CAACyC,IAAI,KAAK;QACnB,GAAGA,IAAI;QACPnC,UAAU,EAAE,EAAE;QACdL,OAAO,EAAE,EAAE;QACXM,SAAS,EAAE,CAAC;MACd,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EAED,MAAMwE,aAAa,GAAGA,CAAA,KAAM;IAC1BxF,WAAW,CAAC;MACVqG,EAAE,EAAE,IAAI;MAAE;MACV1J,IAAI,EAAE,EAAE;MACRC,WAAW,EAAE,EAAE;MACfqD,SAAS,EAAE,EAAE;MACbC,eAAe,EAAE,EAAE;MACnBG,QAAQ,EAAE,IAAI;MACdC,eAAe,EAAE,CAAC;MAClBC,KAAK,EAAE;IACT,CAAC,CAAC;EACJ,CAAC;EAED,MAAM0J,aAAa,GAAGA,CAAA,KAAM;IAC1BxJ,WAAW,CAAC;MACV9D,IAAI,EAAE,EAAE;MACR+D,OAAO,EAAE,EAAE;MACXC,QAAQ,EAAE,SAAS;MACnBC,WAAW,EAAE,MAAM;MACnBC,OAAO,EAAE,EAAE;MACXC,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE,EAAE;MACdC,SAAS,EAAE,CAAC,CAAC;MACbC,cAAc,EAAE,IAAI;MACpBC,cAAc,EAAE,OAAO;MACvBC,eAAe,EAAE,EAAE;MACnBqH,eAAe,EAAE,SAAS;MAAE;MAC5BpH,UAAU,EAAE,EAAE;MACd;MACAC,UAAU,EAAE,SAAS;MACrBC,UAAU,EAAE,EAAE;MACdC,YAAY,EAAE,CAAC;MACfC,eAAe,EAAE,EAAE;MACnBC,YAAY,EAAE,EAAE;MAChBC,SAAS,EAAE,EAAE;MACbC,aAAa,EAAE,EAAE;MACjBC,cAAc,EAAE,EAAE;MAClBC,WAAW,EAAE,EAAE;MACfC,SAAS,EAAE,MAAM;MACjBC,UAAU,EAAE,EAAE;MACdC,OAAO,EAAE,EAAE;MACX;MACAC,aAAa,EAAE,eAAe;MAC9BC,kBAAkB,EAAE,EAAE;MACtBC,iBAAiB,EAAE,EAAE;MACrBC,iBAAiB,EAAE,QAAQ;MAC3BC,cAAc,EAAE,EAAE;MAClBC,WAAW,EAAE,EAAE;MACfC,QAAQ,EAAE,IAAI;MACdC,SAAS,EAAE;IACb,CAAC,CAAC;EACJ,CAAC;EAED,MAAMyI,SAAS,GAAGA,CAAA,KAAM;IACtB,MAAMC,cAAc,GAAGlI,KAAK,CAACC,OAAO,CAACzC,QAAQ,CAACK,OAAO,CAAC,GAAGL,QAAQ,CAACK,OAAO,GAAG,EAAE;IAC9EJ,WAAW,CAACyC,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAErC,OAAO,EAAE,CAAC,GAAGqK,cAAc,EAAE,EAAE;IAAE,CAAC,CAAC,CAAC;EACtE,CAAC;EAED,MAAMC,YAAY,GAAGA,CAACzE,KAAK,EAAE1I,KAAK,KAAK;IACrC,MAAMoN,cAAc,GAAG,CAAC,IAAIpI,KAAK,CAACC,OAAO,CAACzC,QAAQ,CAACK,OAAO,CAAC,GAAGL,QAAQ,CAACK,OAAO,GAAG,EAAE,CAAC,CAAC;IACrFuK,cAAc,CAAC1E,KAAK,CAAC,GAAGpI,MAAM,CAACN,KAAK,IAAI,EAAE,CAAC,CAAC,CAAC;IAC7CyC,WAAW,CAACyC,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAErC,OAAO,EAAEuK;IAAe,CAAC,CAAC,CAAC;EAC7D,CAAC;EAED,MAAMC,YAAY,GAAI3E,KAAK,IAAK;IAC9B,MAAMwE,cAAc,GAAGlI,KAAK,CAACC,OAAO,CAACzC,QAAQ,CAACK,OAAO,CAAC,GAAGL,QAAQ,CAACK,OAAO,GAAG,EAAE;IAC9E,MAAMuK,cAAc,GAAGF,cAAc,CAACpH,MAAM,CAAC,CAACyG,CAAC,EAAEzF,CAAC,KAAKA,CAAC,KAAK4B,KAAK,CAAC;IACnEjG,WAAW,CAACyC,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAErC,OAAO,EAAEuK;IAAe,CAAC,CAAC,CAAC;EAC7D,CAAC;;EAED;EACA,MAAME,sBAAsB,GAAGjR,WAAW,CAAEkR,CAAC,IAAK;IAChDnM,aAAa,CAACmM,CAAC,CAACC,MAAM,CAACxN,KAAK,CAAC;EAC/B,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMyN,wBAAwB,GAAGpR,WAAW,CAAEkR,CAAC,IAAK;IAClDjM,eAAe,CAACiM,CAAC,CAACC,MAAM,CAACxN,KAAK,CAAC;EACjC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAM0N,wBAAwB,GAAGrR,WAAW,CAAEkR,CAAC,IAAK;IAClDvL,WAAW,CAACkD,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEvG,IAAI,EAAE4O,CAAC,CAACC,MAAM,CAACxN;IAAM,CAAC,CAAC,CAAC;EAC1D,CAAC,EAAE,EAAE,CAAC;EAEN,MAAM2N,+BAA+B,GAAGtR,WAAW,CAAEkR,CAAC,IAAK;IACzDvL,WAAW,CAACkD,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEtG,WAAW,EAAE2O,CAAC,CAACC,MAAM,CAACxN;IAAM,CAAC,CAAC,CAAC;EACjE,CAAC,EAAE,EAAE,CAAC;EAEN,MAAM4N,2BAA2B,GAAGvR,WAAW,CAAEkR,CAAC,IAAK;IACrDvL,WAAW,CAACkD,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEjD,SAAS,EAAEsL,CAAC,CAACC,MAAM,CAACxN;IAAM,CAAC,CAAC,CAAC;EAC/D,CAAC,EAAE,EAAE,CAAC;EAEN,MAAM6N,4BAA4B,GAAGxR,WAAW,CAAEkR,CAAC,IAAK;IACtDvL,WAAW,CAACkD,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEhD,eAAe,EAAEqL,CAAC,CAACC,MAAM,CAACxN;IAAM,CAAC,CAAC,CAAC;EACrE,CAAC,EAAE,EAAE,CAAC;EAEN,MAAM8N,4BAA4B,GAAGzR,WAAW,CAAEkR,CAAC,IAAK;IACtDvL,WAAW,CAACkD,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE5C,eAAe,EAAEgF,QAAQ,CAACiG,CAAC,CAACC,MAAM,CAACxN,KAAK,CAAC,IAAI;IAAE,CAAC,CAAC,CAAC;EACpF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAM+N,0BAA0B,GAAG1R,WAAW,CAAEkR,CAAC,IAAK;IACpDvL,WAAW,CAACkD,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE7C,QAAQ,EAAEkL,CAAC,CAACC,MAAM,CAACQ;IAAQ,CAAC,CAAC,CAAC;EAChE,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,wBAAwB,GAAG5R,WAAW,CAAEkR,CAAC,IAAK;IAClD9K,WAAW,CAACyC,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEvG,IAAI,EAAE4O,CAAC,CAACC,MAAM,CAACxN;IAAM,CAAC,CAAC,CAAC;EAC1D,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMkO,2BAA2B,GAAG7R,WAAW,CAAEkR,CAAC,IAAK;IACrD9K,WAAW,CAACyC,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAExC,OAAO,EAAE6K,CAAC,CAACC,MAAM,CAACxN;IAAM,CAAC,CAAC,CAAC;EAC7D,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMmO,kCAAkC,GAAG9R,WAAW,CAAEkR,CAAC,IAAK;IAC5D9K,WAAW,CAACyC,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEhC,cAAc,EAAEqK,CAAC,CAACC,MAAM,CAACxN;IAAM,CAAC,CAAC,CAAC;EACpE,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMoO,kCAAkC,GAAG/R,WAAW,CAAEkR,CAAC,IAAK;IAC5D9K,WAAW,CAACyC,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEjC,cAAc,EAAEsK,CAAC,CAACC,MAAM,CAACa,KAAK,CAAC,CAAC;IAAE,CAAC,CAAC,CAAC;EACvE,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,mCAAmC,GAAGjS,WAAW,CAAEkR,CAAC,IAAK;IAC7D9K,WAAW,CAACyC,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE/B,eAAe,EAAEoK,CAAC,CAACC,MAAM,CAACxN;IAAM,CAAC,CAAC,CAAC;EACrE,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMuO,4BAA4B,GAAGlS,WAAW,CAAEkR,CAAC,IAAK;IACtD9K,WAAW,CAACyC,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAEpC,UAAU,EAAEyK,CAAC,CAACC,MAAM,CAACxN,KAAK,GAAGsH,QAAQ,CAACiG,CAAC,CAACC,MAAM,CAACxN,KAAK,CAAC,GAAG;IAAK,CAAC,CAAC,CAAC;EAClG,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMwO,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI;MACF;MACA,MAAMC,aAAa,GAAG,MAAMrJ,MAAM,CAACC,WAAW,CAACC,QAAQ,CAACC,KAAK,CAC3D,sDACF,CAAC;MAED,IAAI,CAACkJ,aAAa,CAACjJ,OAAO,EAAE;QAC1B9F,SAAS,CAAC,eAAe,EAAE,iCAAiC,GAAG+O,aAAa,CAACrO,KAAK,CAAC;QACnF;MACF;MAEA,MAAMO,KAAK,GAAG8N,aAAa,CAAChJ,IAAI,IAAI,EAAE;;MAEtC;MACA,MAAMiJ,kBAAkB,GAAG,MAAMtJ,MAAM,CAACC,WAAW,CAACC,QAAQ,CAACC,KAAK,CAChE,oDACF,CAAC;MAED,MAAMoJ,UAAU,GAAGD,kBAAkB,CAAClJ,OAAO,GAAIkJ,kBAAkB,CAACjJ,IAAI,IAAI,EAAE,GAAI,EAAE;MAEpF,MAAMmJ,UAAU,GAAG;QACjBC,OAAO,EAAE,KAAK;QACdC,UAAU,EAAE,IAAIhD,IAAI,CAAC,CAAC,CAACiD,WAAW,CAAC,CAAC;QACpCC,IAAI,EAAE,yBAAyB;QAC/BrO,KAAK,EAAE,EAAE;QACTgO,UAAU,EAAEA,UAAU,CAAClG,GAAG,CAACwG,GAAG,KAAK;UACjC,GAAGA,GAAG;UACN;UACAC,OAAO,EAAE;QACX,CAAC,CAAC;MACJ,CAAC;;MAED;MACA,KAAK,MAAM/G,IAAI,IAAIxH,KAAK,EAAE;QACxB,MAAMwO,aAAa,GAAG,MAAM/J,MAAM,CAACC,WAAW,CAACC,QAAQ,CAACC,KAAK,CAC3D,qEAAqE,EACrE,CAAC4C,IAAI,CAACE,EAAE,CACV,CAAC;QAED,MAAM9F,KAAK,GAAG4M,aAAa,CAAC3J,OAAO,GAAI2J,aAAa,CAAC1J,IAAI,IAAI,EAAE,GAAI,EAAE;QAErEmJ,UAAU,CAACjO,KAAK,CAACyO,IAAI,CAAC;UACpB,GAAGjH,IAAI;UACP5F,KAAK,EAAEA;QACT,CAAC,CAAC;MACJ;;MAEA;MACA,MAAM8M,OAAO,GAAG5O,IAAI,CAACC,SAAS,CAACkO,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC;MACnD,MAAMU,QAAQ,GAAG,IAAIC,IAAI,CAAC,CAACF,OAAO,CAAC,EAAE;QAAEL,IAAI,EAAE;MAAmB,CAAC,CAAC;MAElE,MAAMQ,IAAI,GAAGzQ,QAAQ,CAAC0Q,aAAa,CAAC,GAAG,CAAC;MACxCD,IAAI,CAACE,IAAI,GAAGC,GAAG,CAACC,eAAe,CAACN,QAAQ,CAAC;MACzCE,IAAI,CAACK,QAAQ,GAAG,qBAAqB,IAAI/D,IAAI,CAAC,CAAC,CAACiD,WAAW,CAAC,CAAC,CAACe,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO;MAClFN,IAAI,CAACO,KAAK,CAAC,CAAC;MAEZtQ,WAAW,CACT,iBAAiB,EACjB,yBAAyBkB,KAAK,CAACyF,MAAM,sBAAsBuI,UAAU,CAACvI,MAAM,eAC9E,CAAC;IACH,CAAC,CAAC,OAAOhG,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjDV,SAAS,CAAC,eAAe,EAAE,6CAA6C,CAAC;IAC3E;EACF,CAAC;;EAED;EACA,MAAMsQ,kCAAkC,GAAG,MAAAA,CAAA,KAAY;IACrD,IAAI;MACF,MAAMC,aAAa,GAAG,MAAMrQ,OAAO,CACjC,4IAA4I,EAC5I,gCACF,CAAC;MAED,IAAI,CAACqQ,aAAa,EAAE;QAClB;MACF;MAEA,MAAMnI,MAAM,GAAG,MAAM1C,MAAM,CAACC,WAAW,CAAC6K,MAAM,CAAC,wCAAwC,CAAC;MAExF,IAAIpI,MAAM,CAACtC,OAAO,EAAE;QAClB/F,WAAW,CACT,kBAAkB,EAClB,2BAA2BqI,MAAM,CAACqI,OAAO,4BAA4BrI,MAAM,CAACsI,aAAa,yBAAyBtI,MAAM,CAACuI,YAAY,sBACvI,CAAC;MACH,CAAC,MAAM;QACL3Q,SAAS,CAAC,gBAAgB,EAAE,4CAA4C,GAAGoI,MAAM,CAAC1H,KAAK,CAAC;MAC1F;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;MACjEV,SAAS,CAAC,gBAAgB,EAAE,6DAA6D,CAAC;IAC5F;EACF,CAAC;;EAED;EACA,MAAM4Q,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI;MACF;MACA,MAAML,aAAa,GAAG,MAAMrQ,OAAO,CACjC,gHAAgH,EAChH,qBACF,CAAC;MAED,IAAI,CAACqQ,aAAa,EAAE;QAClB;MACF;MAEA,MAAMnI,MAAM,GAAG,MAAM1C,MAAM,CAACC,WAAW,CAACkL,cAAc,CAAC;QACrD/P,KAAK,EAAE,qBAAqB;QAC5BgQ,OAAO,EAAE,CACP;UAAE7R,IAAI,EAAE,YAAY;UAAE8R,UAAU,EAAE,CAAC,MAAM;QAAE,CAAC,EAC5C;UAAE9R,IAAI,EAAE,WAAW;UAAE8R,UAAU,EAAE,CAAC,GAAG;QAAE,CAAC,CACzC;QACDC,UAAU,EAAE,CAAC,UAAU;MACzB,CAAC,CAAC;MAEF,IAAI5I,MAAM,CAAC6I,QAAQ,IAAI,CAAC7I,MAAM,CAAC8I,SAAS,IAAI9I,MAAM,CAAC8I,SAAS,CAACxK,MAAM,KAAK,CAAC,EAAE;QACzE;MACF;MAEA,MAAMyK,QAAQ,GAAG/I,MAAM,CAAC8I,SAAS,CAAC,CAAC,CAAC;;MAEpC;MACA,MAAME,UAAU,GAAG,MAAM1L,MAAM,CAACC,WAAW,CAAC0L,EAAE,CAACC,QAAQ,CAACH,QAAQ,CAAC;MACjE,IAAI,CAACC,UAAU,CAACtL,OAAO,EAAE;QACvB9F,SAAS,CAAC,eAAe,EAAE,uBAAuB,GAAGoR,UAAU,CAAC1Q,KAAK,CAAC;QACtE;MACF;MAEA,IAAI6Q,UAAU;MACd,IAAI;QACFA,UAAU,GAAGxQ,IAAI,CAACqI,KAAK,CAACgI,UAAU,CAACrL,IAAI,CAAC;MAC1C,CAAC,CAAC,OAAOyL,UAAU,EAAE;QACnBxR,SAAS,CAAC,eAAe,EAAE,gEAAgE,CAAC;QAC5F;MACF;;MAEA;MACA,IAAI,CAACuR,UAAU,CAACtQ,KAAK,IAAI,CAACsQ,UAAU,CAACtC,UAAU,EAAE;QAC/CjP,SAAS,CAAC,eAAe,EAAE,6CAA6C,CAAC;QACzE;MACF;;MAEA;MACA,MAAMyR,SAAS,GAAGF,UAAU,CAACtQ,KAAK,GAAGsQ,UAAU,CAACtQ,KAAK,CAACyF,MAAM,GAAG,CAAC;MAChE,MAAMgL,cAAc,GAAGH,UAAU,CAACtC,UAAU,GAAGsC,UAAU,CAACtC,UAAU,CAACvI,MAAM,GAAG,CAAC;MAE/E,IAAIiL,cAAc,GAAG,qBAAqB;MAC1C,IAAIF,SAAS,GAAG,CAAC,EAAE;QACjBE,cAAc,IAAI,KAAKF,SAAS,oBAAoB;MACtD;MACA,IAAIC,cAAc,GAAG,CAAC,EAAE;QACtBC,cAAc,IAAI,KAAKD,cAAc,kBAAkB;MACzD;MACAC,cAAc,IAAI,wBAAwB;MAE1C,MAAMC,YAAY,GAAG,MAAM1R,OAAO,CAACyR,cAAc,EAAE,gBAAgB,CAAC;MAEpE,IAAI,CAACC,YAAY,EAAE;QACjB;MACF;MAEA,IAAIC,gBAAgB,GAAG,CAAC;MACxB,IAAIC,cAAc,GAAG,CAAC;MACtB,IAAIC,qBAAqB,GAAG,CAAC;MAC7B,IAAIC,mBAAmB,GAAG,CAAC;;MAE3B;MACA,IAAIT,UAAU,CAACtQ,KAAK,IAAIqE,KAAK,CAACC,OAAO,CAACgM,UAAU,CAACtQ,KAAK,CAAC,EAAE;QACvD,KAAK,MAAMgR,QAAQ,IAAIV,UAAU,CAACtQ,KAAK,EAAE;UACvC,IAAI;YACF;YACA,MAAM8F,UAAU,GAAG,MAAMrB,MAAM,CAACC,WAAW,CAACC,QAAQ,CAACC,KAAK,CACxD;AACd;AACA;AACA;AACA,4FAA4F,EAC9E,CACEoM,QAAQ,CAACxI,UAAU,IAAI,EAAE,EACzBwI,QAAQ,CAAChT,IAAI,IAAI,eAAe,EAChCgT,QAAQ,CAAC/S,WAAW,IAAI,IAAI,EAC5B+S,QAAQ,CAACvI,gBAAgB,IAAI,EAAE,EAC/BuI,QAAQ,CAACtI,SAAS,KAAKnJ,SAAS,GAAGyR,QAAQ,CAACtI,SAAS,GAAG,CAAC,EACzDsI,QAAQ,CAACC,eAAe,IAAI,IAAI,EAChCD,QAAQ,CAACE,gBAAgB,IAAI,IAAI,EACjCF,QAAQ,CAACrI,gBAAgB,IAAI,CAAC,EAC9B,CAAC;YAAE;YACH,IAAI,CAAC;YAAA,CAET,CAAC;YAED,IAAI7C,UAAU,CAACjB,OAAO,IAAImM,QAAQ,CAACpP,KAAK,IAAIyC,KAAK,CAACC,OAAO,CAAC0M,QAAQ,CAACpP,KAAK,CAAC,EAAE;cACzE,MAAMuP,SAAS,GAAGrL,UAAU,CAACE,QAAQ;;cAErC;cACA,KAAK,MAAMoL,QAAQ,IAAIJ,QAAQ,CAACpP,KAAK,EAAE;gBACrC,MAAM6C,MAAM,CAACC,WAAW,CAACC,QAAQ,CAACC,KAAK,CACrC;AAClB;AACA;AACA;AACA,gGAAgG,EAC9E,CACEuM,SAAS,EACTC,QAAQ,CAACpT,IAAI,IAAI,eAAe,EAChCoT,QAAQ,CAACrP,OAAO,IAAI,EAAE,EACtBqP,QAAQ,CAAClJ,SAAS,IAAI,SAAS,EAC/BkJ,QAAQ,CAAClP,OAAO,IAAI,IAAI,EACxBkP,QAAQ,CAACpJ,YAAY,IAAI,IAAI,EAC7BoJ,QAAQ,CAACvJ,QAAQ,IAAI,CAAC,EACtBuJ,QAAQ,CAAChJ,WAAW,IAAI,IAAI,EAC5BgJ,QAAQ,CAAC/I,eAAe,IAAI,IAAI,EAChC+I,QAAQ,CAAC9I,eAAe,IAAI,IAAI,CAEpC,CAAC;cACH;YACF;YAEAsI,gBAAgB,EAAE;UACpB,CAAC,CAAC,OAAOnR,KAAK,EAAE;YACdD,OAAO,CAACC,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;YAC7CoR,cAAc,EAAE;UAClB;QACF;MACF;;MAEA;MACA,IAAIP,UAAU,CAACtC,UAAU,IAAI3J,KAAK,CAACC,OAAO,CAACgM,UAAU,CAACtC,UAAU,CAAC,EAAE;QACjE,KAAK,MAAMqD,aAAa,IAAIf,UAAU,CAACtC,UAAU,EAAE;UACjD,IAAI;YACF;YACA,IAAIqD,aAAa,CAAC9C,OAAO,KAAK,YAAY,EAAE;cAC1C/O,OAAO,CAACkG,IAAI,CAAC,gEAAgE,CAAC;cAC9E;YACF;YAEA,MAAMjB,MAAM,CAACC,WAAW,CAACC,QAAQ,CAACC,KAAK,CACrC;AACd;AACA;AACA;AACA;AACA;AACA,uHAAuH,EACzG,CACE,CAACyM,aAAa,CAACrT,IAAI,IAAI,qBAAqB,IAAI,aAAa,EAC7DqT,aAAa,CAACpT,WAAW,IAAI,IAAI,EACjCoT,aAAa,CAACC,QAAQ,IAAI,QAAQ,EAClCD,aAAa,CAAC9C,OAAO,IAAI,EAAE,EAC3B8C,aAAa,CAACE,KAAK,IAAI,eAAe,EACtCF,aAAa,CAACG,WAAW,IAAI,GAAG,EAChCH,aAAa,CAACI,UAAU,IAAI,IAAI,EAChCJ,aAAa,CAACK,aAAa,IAAI,IAAI,EACnCL,aAAa,CAACM,QAAQ,IAAI,IAAI,EAC9B,CAAC;YAAE;YACHN,aAAa,CAACO,WAAW,IAAI,IAAI,EACjCP,aAAa,CAACQ,QAAQ,IAAI,IAAI,EAC9BR,aAAa,CAACS,WAAW,IAAI,cAAc,EAC3CT,aAAa,CAACU,QAAQ,IAAI,SAAS,EACnCV,aAAa,CAACW,cAAc,IAAI,IAAI,EACpCX,aAAa,CAACH,gBAAgB,IAAI,IAAI,EACtCG,aAAa,CAACY,uBAAuB,IAAI,EAAE,EAC3CZ,aAAa,CAACa,eAAe,KAAK3S,SAAS,GAAG8R,aAAa,CAACa,eAAe,GAAG,CAAC,EAC/Eb,aAAa,CAACc,oBAAoB,IAAI,GAAG,CAE7C,CAAC;YAEDrB,qBAAqB,EAAE;UACzB,CAAC,CAAC,OAAOrR,KAAK,EAAE;YACdD,OAAO,CAACC,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;YACnDsR,mBAAmB,EAAE;UACvB;QACF;MACF;;MAEA;MACA,MAAM7M,SAAS,CAAC,CAAC;MAEjB,MAAMkO,YAAY,GAAGxB,gBAAgB,GAAGE,qBAAqB;MAC7D,MAAMuB,WAAW,GAAGxB,cAAc,GAAGE,mBAAmB;MAExD,IAAIhP,OAAO,GAAG,qBAAqB;MACnC,IAAI6O,gBAAgB,GAAG,CAAC,EAAE;QACxB7O,OAAO,IAAI,2BAA2B6O,gBAAgB,IAAI;MAC5D;MACA,IAAIE,qBAAqB,GAAG,CAAC,EAAE;QAC7B/O,OAAO,IAAI,yBAAyB+O,qBAAqB,IAAI;MAC/D;MACA,IAAIuB,WAAW,GAAG,CAAC,EAAE;QACnBtQ,OAAO,IAAI,WAAWsQ,WAAW,IAAI;MACvC;MACA,IAAI/B,UAAU,CAACtC,UAAU,IAAIsC,UAAU,CAACtC,UAAU,CAACsE,IAAI,CAAChE,GAAG,IAAIA,GAAG,CAACC,OAAO,KAAK,YAAY,CAAC,EAAE;QAC5FxM,OAAO,IAAI,8FAA8F;MAC3G;MAEAjD,WAAW,CAAC,iBAAiB,EAAEiD,OAAO,CAAC;IAEzC,CAAC,CAAC,OAAOtC,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjDV,SAAS,CAAC,eAAe,EAAE,uEAAuE,CAAC;IACrG;EACF,CAAC;;EAED;EACA,MAAMwT,SAAS,GAAGlO,KAAK,CAACC,OAAO,CAACtE,KAAK,CAAC,GAAGA,KAAK,GAAG,EAAE;EACnD,MAAMwS,aAAa,GAAGD,SAAS,CAACpN,MAAM,CAACqC,IAAI,IAAI;IAC7C,MAAMiL,QAAQ,GAAG,OAAOjL,IAAI,CAACxJ,IAAI,KAAK,QAAQ,GAAGwJ,IAAI,CAACxJ,IAAI,GAAG2B,MAAM,CAAC6H,IAAI,CAACxJ,IAAI,IAAI,EAAE,CAAC;IACpF,MAAMuD,eAAe,GAAG,OAAOiG,IAAI,CAACiB,gBAAgB,KAAK,QAAQ,GAAGjB,IAAI,CAACiB,gBAAgB,GAAG9I,MAAM,CAAC6H,IAAI,CAACiB,gBAAgB,IAAI,EAAE,CAAC;IAC/H,MAAMiK,aAAa,GAAGD,QAAQ,CAACE,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACpS,UAAU,CAACmS,WAAW,CAAC,CAAC,CAAC,IAC1DpR,eAAe,CAACoR,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACpS,UAAU,CAACmS,WAAW,CAAC,CAAC,CAAC;IACrF,MAAME,aAAa,GAAGnS,YAAY,KAAK,KAAK,IACtBA,YAAY,KAAK,QAAQ,IAAI8G,IAAI,CAACkB,SAAU,IAC5ChI,YAAY,KAAK,UAAU,IAAI,CAAC8G,IAAI,CAACkB,SAAU;IACrE,OAAOgK,aAAa,IAAIG,aAAa;EACvC,CAAC,CAAC;EAEF,IAAIvS,OAAO,EAAE;IACX,oBACEzC,OAAA;MAAKiV,SAAS,EAAC,uCAAuC;MAAAC,QAAA,eACpDlV,OAAA;QAAKiV,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBAC1ClV,OAAA;UAAKiV,SAAS,EAAC;QAAiF;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvGtV,OAAA;UAAMiV,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAwB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5D;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACEtV,OAAA;IAAKiV,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExBlV,OAAA;MAAKiV,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAChDlV,OAAA;QAAAkV,QAAA,gBACElV,OAAA;UAAIiV,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7DtV,OAAA;UAAGiV,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAuC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrE,CAAC,eACNtV,OAAA;QAAKiV,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBAC1ClV,OAAA;UACEuV,OAAO,EAAEvF,oBAAqB;UAC9BiF,SAAS,EAAC,2CAA2C;UACrDjT,KAAK,EAAC,sBAAsB;UAAAkT,QAAA,gBAE5BlV,OAAA,CAACJ,iBAAiB;YAACqV,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACzCtV,OAAA;YAAAkV,QAAA,EAAM;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CAAC,eACTtV,OAAA;UACEuV,OAAO,EAAEzD,oBAAqB;UAC9BmD,SAAS,EAAC,2CAA2C;UACrDjT,KAAK,EAAC,sBAAsB;UAAAkT,QAAA,gBAE5BlV,OAAA,CAACH,eAAe;YAACoV,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACvCtV,OAAA;YAAAkV,QAAA,EAAM;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CAAC,eACTtV,OAAA;UACEuV,OAAO,EAAE/D,kCAAmC;UAC5CyD,SAAS,EAAC,2CAA2C;UACrDjT,KAAK,EAAC,6DAA6D;UAAAkT,QAAA,gBAEnElV,OAAA,CAAC9B,SAAS;YAAC+W,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjCtV,OAAA;YAAAkV,QAAA,EAAM;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CAAC,eACTtV,OAAA;UACEuV,OAAO,EAAEjP,YAAa;UACtB2O,SAAS,EAAC,2CAA2C;UACrDjT,KAAK,EAAC,kBAAkB;UAAAkT,QAAA,gBAExBlV,OAAA;YAAAkV,QAAA,EAAM;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACftV,OAAA;YAAAkV,QAAA,EAAM;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CAAC,eACTtV,OAAA;UACEuV,OAAO,EAAEA,CAAA,KAAMvS,kBAAkB,CAAC,IAAI,CAAE;UACxCiS,SAAS,EAAC,yCAAyC;UACnDO,QAAQ,EAAEnT,QAAQ,CAACuF,MAAM,KAAK,CAAE;UAAAsN,QAAA,gBAEhClV,OAAA,CAACjC,QAAQ;YAACkX,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAChCtV,OAAA;YAAAkV,QAAA,EAAM;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNtV,OAAA;MAAKiV,SAAS,EAAC,iCAAiC;MAAAC,QAAA,gBAE9ClV,OAAA;QAAKiV,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BlV,OAAA,CAAChC,mBAAmB;UAACiX,SAAS,EAAC;QAA0E;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC5GtV,OAAA;UACEwQ,IAAI,EAAC,MAAM;UACXiF,WAAW,EAAC,iBAAiB;UAC7BjU,KAAK,EAAEmB,UAAW;UAClB+S,QAAQ,EAAE5G,sBAAuB;UACjCmG,SAAS,EAAC;QAAmB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGNtV,OAAA;QACEwB,KAAK,EAAEqB,YAAa;QACpB6S,QAAQ,EAAEzG,wBAAyB;QACnCgG,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBAE/BlV,OAAA;UAAQwB,KAAK,EAAC,KAAK;UAAA0T,QAAA,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACtCtV,OAAA;UAAQwB,KAAK,EAAC,QAAQ;UAAA0T,QAAA,EAAC;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACtCtV,OAAA;UAAQwB,KAAK,EAAC,UAAU;UAAA0T,QAAA,EAAC;QAAQ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGNtV,OAAA;MAAKiV,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBACpDlV,OAAA;QAAKiV,SAAS,EAAC,MAAM;QAAAC,QAAA,eACnBlV,OAAA;UAAKiV,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChClV,OAAA;YAAKiV,SAAS,EAAC,eAAe;YAAAC,QAAA,eAC5BlV,OAAA,CAAClC,8BAA8B;cAACmX,SAAS,EAAC;YAAuB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjE,CAAC,eACNtV,OAAA;YAAKiV,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBlV,OAAA;cAAGiV,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAChEtV,OAAA;cAAGiV,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAE/S,KAAK,CAACyF;YAAM;cAAAuN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENtV,OAAA;QAAKiV,SAAS,EAAC,MAAM;QAAAC,QAAA,eACnBlV,OAAA;UAAKiV,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChClV,OAAA;YAAKiV,SAAS,EAAC,eAAe;YAAAC,QAAA,eAC5BlV,OAAA,CAAC7B,QAAQ;cAAC8W,SAAS,EAAC;YAAwB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C,CAAC,eACNtV,OAAA;YAAKiV,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBlV,OAAA;cAAGiV,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACjEtV,OAAA;cAAGiV,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAC5CR,SAAS,CAACpN,MAAM,CAACqO,CAAC,IAAIA,CAAC,CAAC9K,SAAS,CAAC,CAACjD;YAAM;cAAAuN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENtV,OAAA;QAAKiV,SAAS,EAAC,MAAM;QAAAC,QAAA,eACnBlV,OAAA;UAAKiV,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChClV,OAAA;YAAKiV,SAAS,EAAC,eAAe;YAAAC,QAAA,eAC5BlV,OAAA,CAACjB,QAAQ;cAACkW,SAAS,EAAC;YAAyB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CAAC,eACNtV,OAAA;YAAKiV,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBlV,OAAA;cAAGiV,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAChEtV,OAAA;cAAGiV,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAC5CR,SAAS,CAACkB,MAAM,CAAC,CAACC,KAAK,EAAEF,CAAC,KAAKE,KAAK,IAAIF,CAAC,CAACG,UAAU,IAAI,CAAC,CAAC,EAAE,CAAC;YAAC;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENtV,OAAA;QAAKiV,SAAS,EAAC,MAAM;QAAAC,QAAA,eACnBlV,OAAA;UAAKiV,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChClV,OAAA;YAAKiV,SAAS,EAAC,eAAe;YAAAC,QAAA,eAC5BlV,OAAA,CAACtB,eAAe;cAACuW,SAAS,EAAC;YAAyB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD,CAAC,eACNtV,OAAA;YAAKiV,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBlV,OAAA;cAAGiV,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAClEtV,OAAA;cAAGiV,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAC5CR,SAAS,CAACkB,MAAM,CAAC,CAACC,KAAK,EAAEF,CAAC,KAAKE,KAAK,IAAIF,CAAC,CAACI,kBAAkB,IAAI,CAAC,CAAC,EAAE,CAAC;YAAC;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLjT,QAAQ,CAACuF,MAAM,KAAK,CAAC,iBACpB5H,OAAA;MAAKiV,SAAS,EAAC,sDAAsD;MAAAC,QAAA,eACnElV,OAAA;QAAKiV,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBAC1ClV,OAAA,CAACpB,uBAAuB;UAACqW,SAAS,EAAC;QAAyB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC/DtV,OAAA;UAAAkV,QAAA,gBACElV,OAAA;YAAIiV,SAAS,EAAC,qCAAqC;YAAAC,QAAA,EAAC;UAA4B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrFtV,OAAA;YAAGiV,SAAS,EAAC,8BAA8B;YAAAC,QAAA,EAAC;UAE5C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJtV,OAAA;YAAGiV,SAAS,EAAC,8BAA8B;YAAAC,QAAA,GAAC,0BAClB,EAAC7S,QAAQ,CAACuF,MAAM,EAAC,sCAC3C;UAAA;YAAAuN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAGAX,aAAa,CAAC/M,MAAM,KAAK,CAAC,gBACzB5H,OAAA;MAAKiV,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChClV,OAAA,CAAClC,8BAA8B;QAACmX,SAAS,EAAC;MAAsC;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACnFtV,OAAA;QAAIiV,SAAS,EAAC,wCAAwC;QAAAC,QAAA,EACnDvS,UAAU,IAAIE,YAAY,KAAK,KAAK,GAAG,gBAAgB,GAAG;MAAsB;QAAAsS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/E,CAAC,eACLtV,OAAA;QAAGiV,SAAS,EAAC,oBAAoB;QAAAC,QAAA,EAC9BvS,UAAU,IAAIE,YAAY,KAAK,KAAK,GACjC,8CAA8C,GAC9C;MAAiE;QAAAsS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEpE,CAAC,EACH,CAAC3S,UAAU,IAAIE,YAAY,KAAK,KAAK,IAAIR,QAAQ,CAACuF,MAAM,GAAG,CAAC,iBAC3D5H,OAAA;QACEuV,OAAO,EAAEA,CAAA,KAAMvS,kBAAkB,CAAC,IAAI,CAAE;QACxCiS,SAAS,EAAC,iDAAiD;QAAAC,QAAA,gBAE3DlV,OAAA,CAACjC,QAAQ;UAACkX,SAAS,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAChCtV,OAAA;UAAAkV,QAAA,EAAM;QAAW;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB,CACT;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,gBAENtV,OAAA;MAAKiV,SAAS,EAAC,sEAAsE;MAAAC,QAAA,gBAGnFlV,OAAA;QAAKiV,SAAS,EAAC,+CAA+C;QAAAC,QAAA,eAC5DlV,OAAA;UAAKiV,SAAS,EAAC,wEAAwE;UAAAC,QAAA,gBACrFlV,OAAA;YAAKiV,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACtCtV,OAAA;YAAKiV,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACxCtV,OAAA;YAAKiV,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACxCtV,OAAA;YAAKiV,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACvCtV,OAAA;YAAKiV,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC/CtV,OAAA;YAAKiV,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNtV,OAAA;QAAKiV,SAAS,EAAC,0BAA0B;QAAAC,QAAA,EACtCP,aAAa,CAAC1K,GAAG,CAAEN,IAAI,iBACtB3J,OAAA;UAAmBiV,SAAS,EAAC,2DAA2D;UAAAC,QAAA,eACtFlV,OAAA;YAAKiV,SAAS,EAAC,sCAAsC;YAAAC,QAAA,gBAEnDlV,OAAA;cAAKiV,SAAS,EAAC,YAAY;cAAAC,QAAA,eACzBlV,OAAA;gBAAKiV,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,gBACzClV,OAAA;kBAAKiV,SAAS,EAAE,uEACdtL,IAAI,CAACkB,SAAS,GAAG,eAAe,GAAG,aAAa,EAC/C;kBAAAqK,QAAA,EACAvL,IAAI,CAACkB,SAAS,gBACb7K,OAAA,CAAClC,8BAA8B;oBAACmX,SAAS,EAAC;kBAAyB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAEtEtV,OAAA,CAAClC,8BAA8B;oBAACmX,SAAS,EAAC;kBAAuB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBACpE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eACNtV,OAAA;kBAAKiV,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAC7BlV,OAAA;oBAAIiV,SAAS,EAAC,sCAAsC;oBAAAC,QAAA,EACjD,OAAOvL,IAAI,CAACxJ,IAAI,KAAK,QAAQ,GAAGwJ,IAAI,CAACxJ,IAAI,GAAG2B,MAAM,CAAC6H,IAAI,CAACxJ,IAAI,IAAI,cAAc;kBAAC;oBAAAgV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9E,CAAC,EACJ3L,IAAI,CAACvJ,WAAW,iBACfJ,OAAA;oBAAGiV,SAAS,EAAC,yCAAyC;oBAAAC,QAAA,EACnDvL,IAAI,CAACvJ;kBAAW;oBAAA+U,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChB,CACJ,eAEDtV,OAAA;oBAAKiV,SAAS,EAAC,2BAA2B;oBAAAC,QAAA,GACvC,CAAC,OAAOvL,IAAI,CAACiB,gBAAgB,KAAK,QAAQ,GAAGjB,IAAI,CAACiB,gBAAgB,GAAG9I,MAAM,CAAC6H,IAAI,CAACiB,gBAAgB,IAAI,EAAE,CAAC,EAAE0G,KAAK,CAAC,GAAG,CAAC,CAAC0E,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC/L,GAAG,CAAC,CAACgM,OAAO,EAAE/L,KAAK,kBACnJlK,OAAA;sBAEEiV,SAAS,EAAC,8EAA8E;sBAAAC,QAAA,EAEvFe,OAAO,CAAClO,IAAI,CAAC;oBAAC,GAHVmC,KAAK;sBAAAiL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAIN,CACP,CAAC,EACD,CAAC,OAAO3L,IAAI,CAACiB,gBAAgB,KAAK,QAAQ,GAAGjB,IAAI,CAACiB,gBAAgB,GAAG9I,MAAM,CAAC6H,IAAI,CAACiB,gBAAgB,IAAI,EAAE,CAAC,EAAE0G,KAAK,CAAC,GAAG,CAAC,CAAC1J,MAAM,GAAG,CAAC,iBAC9H5H,OAAA;sBAAMiV,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,GAAC,GACrC,EAAC,CAAC,OAAOvL,IAAI,CAACiB,gBAAgB,KAAK,QAAQ,GAAGjB,IAAI,CAACiB,gBAAgB,GAAG9I,MAAM,CAAC6H,IAAI,CAACiB,gBAAgB,IAAI,EAAE,CAAC,EAAE0G,KAAK,CAAC,GAAG,CAAC,CAAC1J,MAAM,GAAG,CAAC,EAAC,OACpI;oBAAA;sBAAAuN,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CACP;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNtV,OAAA;cAAKiV,SAAS,EAAC,YAAY;cAAAC,QAAA,eACzBlV,OAAA;gBAAMiV,SAAS,EAAE,uEACftL,IAAI,CAACkB,SAAS,GAAG,6BAA6B,GAAG,2BAA2B,EAC3E;gBAAAqK,QAAA,GACAvL,IAAI,CAACkB,SAAS,gBAAG7K,OAAA,CAAC7B,QAAQ;kBAAC8W,SAAS,EAAC;gBAAc;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAAGtV,OAAA,CAAC5B,SAAS;kBAAC6W,SAAS,EAAC;gBAAc;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,EAC/F3L,IAAI,CAACkB,SAAS,GAAG,QAAQ,GAAG,UAAU;cAAA;gBAAAsK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eAGNtV,OAAA;cAAKiV,SAAS,EAAC,YAAY;cAAAC,QAAA,eACzBlV,OAAA;gBAAKiV,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAC/CvL,IAAI,CAACuM,WAAW,GACd,OAAOvM,IAAI,CAACuM,WAAW,KAAK,QAAQ,GAAGvM,IAAI,CAACuM,WAAW,GAAGpU,MAAM,CAAC6H,IAAI,CAACuM,WAAW,CAAC,GACnF;cAAW;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEV;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNtV,OAAA;cAAKiV,SAAS,EAAC,YAAY;cAAAC,QAAA,eACzBlV,OAAA;gBAAMiV,SAAS,EAAC,mGAAmG;gBAAAC,QAAA,GAChHvL,IAAI,CAACmM,UAAU,IAAI,CAAC,EAAC,QACxB;cAAA;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eAGNtV,OAAA;cAAKiV,SAAS,EAAC,YAAY;cAAAC,QAAA,eACzBlV,OAAA;gBAAKiV,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAC/CvL,IAAI,CAACoM,kBAAkB,IAAI;cAAC;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNtV,OAAA;cAAKiV,SAAS,EAAC,YAAY;cAAAC,QAAA,eACzBlV,OAAA;gBAAKiV,SAAS,EAAC,yCAAyC;gBAAAC,QAAA,gBACtDlV,OAAA;kBACEuV,OAAO,EAAEA,CAAA,KAAM;oBACbjS,eAAe,CAACqG,IAAI,CAAC;oBACrBvG,gBAAgB,CAAC,IAAI,CAAC;kBACxB,CAAE;kBACF6R,SAAS,EAAC,qFAAqF;kBAC/FjT,KAAK,EAAC,WAAW;kBAAAkT,QAAA,eAEjBlV,OAAA,CAAC3B,OAAO;oBAAC4W,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC,eACTtV,OAAA;kBACEuV,OAAO,EAAEA,CAAA,KAAM7L,cAAc,CAACC,IAAI,CAAE;kBACpCsL,SAAS,EAAC,yFAAyF;kBACnGjT,KAAK,EAAC,WAAW;kBAAAkT,QAAA,eAEjBlV,OAAA,CAAC/B,UAAU;oBAACgX,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5B,CAAC,eACTtV,OAAA;kBACEuV,OAAO,EAAEA,CAAA,KAAMhM,gBAAgB,CAACI,IAAI,CAACE,EAAE,EAAEF,IAAI,CAACkB,SAAS,CAAE;kBACzDoK,SAAS,EAAE,sEACTtL,IAAI,CAACkB,SAAS,GACV,0CAA0C,GAC1C,wCAAwC,EAC3C;kBACH7I,KAAK,EAAE2H,IAAI,CAACkB,SAAS,GAAG,YAAY,GAAG,eAAgB;kBAAAqK,QAAA,EAEtDvL,IAAI,CAACkB,SAAS,gBAAG7K,OAAA,CAAC5B,SAAS;oBAAC6W,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAAGtV,OAAA,CAAC7B,QAAQ;oBAAC8W,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChF,CAAC,eACTtV,OAAA;kBACEuV,OAAO,EAAEA,CAAA,KAAMtM,gBAAgB,CAACU,IAAI,CAACE,EAAE,CAAE;kBACzCoL,SAAS,EAAC,mFAAmF;kBAC7FjT,KAAK,EAAC,aAAa;kBAAAkT,QAAA,eAEnBlV,OAAA,CAAC9B,SAAS;oBAAC+W,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC,GArHE3L,IAAI,CAACE,EAAE;UAAAsL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAsHZ,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAGAvS,eAAe,iBACd/C,OAAA;MAAKiV,SAAS,EAAC,gFAAgF;MAAAC,QAAA,eAC7FlV,OAAA;QAAKiV,SAAS,EAAC,6EAA6E;QAAAC,QAAA,eAC1FlV,OAAA;UAAKiV,SAAS,EAAC,KAAK;UAAAC,QAAA,gBAClBlV,OAAA;YAAKiV,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBACrDlV,OAAA;cAAIiV,SAAS,EAAC,qCAAqC;cAAAC,QAAA,EAAC;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5EtV,OAAA;cACEuV,OAAO,EAAEA,CAAA,KAAM;gBACbvS,kBAAkB,CAAC,KAAK,CAAC;gBACzBgG,aAAa,CAAC,CAAC;cACjB,CAAE;cACFiM,SAAS,EAAC,oEAAoE;cAAAC,QAAA,eAE9ElV,OAAA,CAACnB,SAAS;gBAACoW,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENtV,OAAA;YAAKiV,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBAEpDlV,OAAA;cAAKiV,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBlV,OAAA;gBAAIiV,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAE5DtV,OAAA;gBAAAkV,QAAA,gBACElV,OAAA;kBAAOiV,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAEhE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRtV,OAAA;kBACEwQ,IAAI,EAAC,MAAM;kBACXhP,KAAK,EAAE+B,QAAQ,CAACpD,IAAK;kBACrBuV,QAAQ,EAAExG,wBAAyB;kBACnCuG,WAAW,EAAC,4BAA4B;kBACxCR,SAAS,EAAC;gBAAa;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENtV,OAAA;gBAAAkV,QAAA,gBACElV,OAAA;kBAAOiV,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAEhE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRtV,OAAA;kBACEwB,KAAK,EAAE+B,QAAQ,CAACnD,WAAY;kBAC5BsV,QAAQ,EAAEvG,+BAAgC;kBAC1CsG,WAAW,EAAC,yCAAyC;kBACrDU,IAAI,EAAE,CAAE;kBACRlB,SAAS,EAAC;gBAAyB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENtV,OAAA;gBAAAkV,QAAA,gBACElV,OAAA;kBAAOiV,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAEhE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRtV,OAAA;kBACEwB,KAAK,EAAE+B,QAAQ,CAACE,SAAU;kBAC1BiS,QAAQ,EAAEtG,2BAA4B;kBACtC6F,SAAS,EAAC,aAAa;kBAAAC,QAAA,gBAEvBlV,OAAA;oBAAQwB,KAAK,EAAC,EAAE;oBAAA0T,QAAA,EAAC;kBAAgB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EACzC,CAAC9O,KAAK,CAACC,OAAO,CAACpE,QAAQ,CAAC,GAAGA,QAAQ,GAAG,EAAE,EAAE4H,GAAG,CAAC1C,OAAO,IAAI;oBACxD;oBACA,IAAI,CAACA,OAAO,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;sBAC3C,OAAO,IAAI;oBACb;oBAEA,MAAM9D,SAAS,GAAG8D,OAAO,CAACoD,UAAU,IAAI,WAAWyL,IAAI,CAACC,MAAM,CAAC,CAAC,EAAE;oBAClE,MAAMC,UAAU,GAAG,OAAO/O,OAAO,CAAC2O,WAAW,KAAK,QAAQ,GAAG3O,OAAO,CAAC2O,WAAW,GAAGpU,MAAM,CAACyF,OAAO,CAAC2O,WAAW,IAAI,gBAAgB,CAAC;oBAClI,MAAMK,WAAW,GAAG,OAAOhP,OAAO,CAACiP,YAAY,KAAK,QAAQ,IAAI,OAAOjP,OAAO,CAACiP,YAAY,KAAK,QAAQ,GAAGjP,OAAO,CAACiP,YAAY,GAAG1U,MAAM,CAACyF,OAAO,CAACiP,YAAY,IAAI,SAAS,CAAC;oBAE3K,oBACExW,OAAA;sBAAwBwB,KAAK,EAAEiC,SAAU;sBAAAyR,QAAA,GACtCoB,UAAU,EAAC,KAAG,EAACC,WAAW,EAAC,GAC9B;oBAAA,GAFa9S,SAAS;sBAAA0R,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAEd,CAAC;kBAEb,CAAC,CAAC,CAAChO,MAAM,CAACmP,OAAO,CAAC;gBAAA;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eAENtV,OAAA;gBAAAkV,QAAA,gBACElV,OAAA;kBAAOiV,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAEhE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRtV,OAAA;kBACEwQ,IAAI,EAAC,MAAM;kBACXhP,KAAK,EAAE+B,QAAQ,CAACG,eAAgB;kBAChCgS,QAAQ,EAAErG,4BAA6B;kBACvCoG,WAAW,EAAC,sCAAsC;kBAClDR,SAAS,EAAC;gBAAa;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC,eACFtV,OAAA;kBAAGiV,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAC;gBAE1C;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eAENtV,OAAA;gBAAAkV,QAAA,gBACElV,OAAA;kBAAOiV,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,gBAC7DlV,OAAA,CAACvB,SAAS;oBAACwW,SAAS,EAAC;kBAAqB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,6BAE/C;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRtV,OAAA;kBACEwQ,IAAI,EAAC,QAAQ;kBACbkG,GAAG,EAAC,GAAG;kBACPlV,KAAK,EAAE+B,QAAQ,CAACO,eAAgB;kBAChC4R,QAAQ,EAAEpG,4BAA6B;kBACvCmG,WAAW,EAAC,GAAG;kBACfR,SAAS,EAAC;gBAAa;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC,eACFtV,OAAA;kBAAGiV,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAC;gBAE1C;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eAENtV,OAAA;gBAAKiV,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChClV,OAAA;kBACEwQ,IAAI,EAAC,UAAU;kBACf3G,EAAE,EAAC,cAAc;kBACjB2F,OAAO,EAAEjM,QAAQ,CAACM,QAAS;kBAC3B6R,QAAQ,EAAEnG,0BAA2B;kBACrC0F,SAAS,EAAC;gBAAe;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CAAC,eACFtV,OAAA;kBAAO2W,OAAO,EAAC,cAAc;kBAAC1B,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAC;gBAErE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNtV,OAAA;cAAKiV,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBlV,OAAA;gBAAKiV,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,gBAChDlV,OAAA;kBAAIiV,SAAS,EAAC,2BAA2B;kBAAAC,QAAA,GAAC,cAAY,EAAC3R,QAAQ,CAACQ,KAAK,CAAC6D,MAAM,EAAC,GAAC;gBAAA;kBAAAuN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACnFtV,OAAA;kBACEuV,OAAO,EAAEA,CAAA,KAAMrP,gBAAgB,CAAC,IAAI,CAAE;kBACtC+O,SAAS,EAAC,mDAAmD;kBAAAC,QAAA,gBAE7DlV,OAAA,CAACjC,QAAQ;oBAACkX,SAAS,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAChCtV,OAAA;oBAAAkV,QAAA,EAAM;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eAENtV,OAAA;gBAAKiV,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,GAChD3R,QAAQ,CAACQ,KAAK,CAACkG,GAAG,CAAC,CAAC1B,IAAI,EAAE2B,KAAK,KAAK;kBACnC;kBACA,IAAI,CAAC3B,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;oBACrC,OAAO,IAAI;kBACb;kBAEA,MAAMqO,QAAQ,GAAG,OAAOrO,IAAI,CAACpI,IAAI,KAAK,QAAQ,GAAGoI,IAAI,CAACpI,IAAI,GAAG,cAAc;kBAC3E,MAAMgE,QAAQ,GAAG,OAAOoE,IAAI,CAACpE,QAAQ,KAAK,QAAQ,GAAGoE,IAAI,CAACpE,QAAQ,GAAG,SAAS;kBAC9E,MAAM0S,WAAW,GAAG,OAAOtO,IAAI,CAACrE,OAAO,KAAK,QAAQ,GAAGqE,IAAI,CAACrE,OAAO,GAAG,YAAY;kBAElF,oBACElE,OAAA;oBAAiBiV,SAAS,EAAC,uCAAuC;oBAAAC,QAAA,eAChElV,OAAA;sBAAKiV,SAAS,EAAC,kCAAkC;sBAAAC,QAAA,gBAC/ClV,OAAA;wBAAKiV,SAAS,EAAC,QAAQ;wBAAAC,QAAA,gBACrBlV,OAAA;0BAAKiV,SAAS,EAAC,6BAA6B;0BAAAC,QAAA,gBAC1ClV,OAAA;4BAAMiV,SAAS,EAAC,mCAAmC;4BAAAC,QAAA,GAAEhL,KAAK,GAAG,CAAC,EAAC,IAAE,EAAC0M,QAAQ;0BAAA;4BAAAzB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC,eAClFtV,OAAA;4BAAMiV,SAAS,EAAC,8EAA8E;4BAAAC,QAAA,EAC3F/Q;0BAAQ;4BAAAgR,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACL,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACJ,CAAC,eACNtV,OAAA;0BAAGiV,SAAS,EAAC,yCAAyC;0BAAAC,QAAA,EAAE2B;wBAAW;0BAAA1B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,EACzE,CAAC,MAAM;0BACN;0BACA,IAAIwB,gBAAgB,GAAG,EAAE;0BACzB,IAAItQ,KAAK,CAACC,OAAO,CAAC8B,IAAI,CAAClE,OAAO,CAAC,EAAE;4BAC/ByS,gBAAgB,GAAGvO,IAAI,CAAClE,OAAO;0BACjC,CAAC,MAAM,IAAI,OAAOkE,IAAI,CAAClE,OAAO,KAAK,QAAQ,IAAIkE,IAAI,CAAClE,OAAO,IAAIkE,IAAI,CAAClE,OAAO,CAACA,OAAO,EAAE;4BACnFyS,gBAAgB,GAAGvO,IAAI,CAAClE,OAAO,CAACA,OAAO;0BACzC;0BAEA,IAAIyS,gBAAgB,CAAClP,MAAM,GAAG,CAAC,EAAE;4BAC/B,oBACE5H,OAAA;8BAAKiV,SAAS,EAAC,2BAA2B;8BAAAC,QAAA,EACvC4B,gBAAgB,CAAC7M,GAAG,CAAC,CAAC6B,MAAM,EAAEiL,QAAQ,KAAK;gCAC1C;gCACA,IAAIC,WAAW,GAAG,QAAQ;gCAC1B,IAAI,OAAOlL,MAAM,KAAK,QAAQ,EAAE;kCAC9BkL,WAAW,GAAGlL,MAAM;gCACtB,CAAC,MAAM,IAAI,OAAOA,MAAM,KAAK,QAAQ,IAAIA,MAAM,EAAE;kCAC/C;kCACAkL,WAAW,GAAGlV,MAAM,CAACgK,MAAM,CAAC/J,YAAY,IAAI+J,MAAM,CAAC9J,KAAK,IAAI8J,MAAM,CAAC3L,IAAI,IAAI2L,MAAM,CAAC5L,IAAI,IAAI4L,MAAM,CAACtK,KAAK,IAAI,QAAQ,CAAC;gCACrH,CAAC,MAAM;kCACLwV,WAAW,GAAGlV,MAAM,CAACgK,MAAM,IAAI,QAAQ,CAAC;gCAC1C;gCAEA,oBACE9L,OAAA;kCAAqBiV,SAAS,EAAC,yCAAyC;kCAAAC,QAAA,EACrE8B;gCAAW,GADHD,QAAQ;kCAAA5B,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAEb,CAAC;8BAEX,CAAC;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACC,CAAC;0BAEV;0BACA,OAAO,IAAI;wBACb,CAAC,EAAE,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACD,CAAC,eACNtV,OAAA;wBAAKiV,SAAS,EAAC,kCAAkC;wBAAAC,QAAA,gBAC/ClV,OAAA;0BACEuV,OAAO,EAAEA,CAAA,KAAM7H,cAAc,CAACxD,KAAK,CAAE;0BACrC+K,SAAS,EAAC,+CAA+C;0BAAAC,QAAA,eAEzDlV,OAAA,CAAC/B,UAAU;4BAACgX,SAAS,EAAC;0BAAS;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC5B,CAAC,eACTtV,OAAA;0BACEuV,OAAO,EAAEA,CAAA,KAAMzH,gBAAgB,CAAC5D,KAAK,CAAE;0BACvC+K,SAAS,EAAC,8CAA8C;0BAAAC,QAAA,eAExDlV,OAAA,CAAC9B,SAAS;4BAAC+W,SAAS,EAAC;0BAAS;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC3B,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACN,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC,GA5DIpL,KAAK;oBAAAiL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OA6DZ,CAAC;gBAER,CAAC,CAAC,CAAChO,MAAM,CAACmP,OAAO,CAAC,EAEjBlT,QAAQ,CAACQ,KAAK,CAAC6D,MAAM,KAAK,CAAC,iBAC1B5H,OAAA;kBAAKiV,SAAS,EAAC,gCAAgC;kBAAAC,QAAA,gBAC7ClV,OAAA,CAAClC,8BAA8B;oBAACmX,SAAS,EAAC;kBAAoC;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACjFtV,OAAA;oBAAGiV,SAAS,EAAC,SAAS;oBAAAC,QAAA,EAAC;kBAAkB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAC7CtV,OAAA;oBAAGiV,SAAS,EAAC,SAAS;oBAAAC,QAAA,EAAC;kBAAkC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1D,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENtV,OAAA;YAAKiV,SAAS,EAAC,qBAAqB;YAAAC,QAAA,gBAClClV,OAAA;cACEuV,OAAO,EAAEA,CAAA,KAAM;gBACbvS,kBAAkB,CAAC,KAAK,CAAC;gBACzBgG,aAAa,CAAC,CAAC;cACjB,CAAE;cACFiM,SAAS,EAAC,sBAAsB;cAAAC,QAAA,EACjC;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTtV,OAAA;cACEuV,OAAO,EAAEzN,gBAAiB;cAC1BmN,SAAS,EAAC,oBAAoB;cAC9BO,QAAQ,EACN,CAACjS,QAAQ,CAACpD,IAAI,CAAC4H,IAAI,CAAC,CAAC,IACrB,CAACxE,QAAQ,CAACG,eAAe,CAACqE,IAAI,CAAC,CAAC,IAChC,CAACxE,QAAQ,CAACE,SAAS,IACnBF,QAAQ,CAACQ,KAAK,CAAC6D,MAAM,KAAK,CAC3B;cAAAsN,QAAA,EACF;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAGArS,aAAa,iBACZjD,OAAA;MAAKiV,SAAS,EAAC,gFAAgF;MAAAC,QAAA,eAC7FlV,OAAA;QAAKiV,SAAS,EAAC,6EAA6E;QAAAC,QAAA,eAC1FlV,OAAA;UAAKiV,SAAS,EAAC,KAAK;UAAAC,QAAA,gBAClBlV,OAAA;YAAKiV,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBACrDlV,OAAA;cAAIiV,SAAS,EAAC,qCAAqC;cAAAC,QAAA,EAAC;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC1EtV,OAAA;cACEuV,OAAO,EAAEA,CAAA,KAAM;gBACbrS,gBAAgB,CAAC,KAAK,CAAC;gBACvB8F,aAAa,CAAC,CAAC;cACjB,CAAE;cACFiM,SAAS,EAAC,oEAAoE;cAAAC,QAAA,eAE9ElV,OAAA,CAACnB,SAAS;gBAACoW,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENtV,OAAA;YAAKiV,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBAEpDlV,OAAA;cAAKiV,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBlV,OAAA;gBAAIiV,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAE5DtV,OAAA;gBAAAkV,QAAA,gBACElV,OAAA;kBAAOiV,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAEhE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRtV,OAAA;kBACEwQ,IAAI,EAAC,MAAM;kBACXhP,KAAK,EAAE+B,QAAQ,CAACpD,IAAK;kBACrBuV,QAAQ,EAAExG,wBAAyB;kBACnCuG,WAAW,EAAC,4BAA4B;kBACxCR,SAAS,EAAC;gBAAa;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENtV,OAAA;gBAAAkV,QAAA,gBACElV,OAAA;kBAAOiV,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAEhE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRtV,OAAA;kBACEwB,KAAK,EAAE+B,QAAQ,CAACnD,WAAY;kBAC5BsV,QAAQ,EAAEvG,+BAAgC;kBAC1CsG,WAAW,EAAC,yCAAyC;kBACrDU,IAAI,EAAE,CAAE;kBACRlB,SAAS,EAAC;gBAAyB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENtV,OAAA;gBAAAkV,QAAA,gBACElV,OAAA;kBAAOiV,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAEhE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRtV,OAAA;kBACEwB,KAAK,EAAE+B,QAAQ,CAACE,SAAU;kBAC1BiS,QAAQ,EAAEtG,2BAA4B;kBACtC6F,SAAS,EAAC,aAAa;kBAAAC,QAAA,gBAEvBlV,OAAA;oBAAQwB,KAAK,EAAC,EAAE;oBAAA0T,QAAA,EAAC;kBAAgB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EACzC,CAAC9O,KAAK,CAACC,OAAO,CAACpE,QAAQ,CAAC,GAAGA,QAAQ,GAAG,EAAE,EAAE4H,GAAG,CAAC1C,OAAO,IAAI;oBACxD;oBACA,IAAI,CAACA,OAAO,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;sBAC3C,OAAO,IAAI;oBACb;oBAEA,MAAM9D,SAAS,GAAG8D,OAAO,CAACoD,UAAU,IAAI,WAAWyL,IAAI,CAACC,MAAM,CAAC,CAAC,EAAE;oBAClE,MAAMC,UAAU,GAAG,OAAO/O,OAAO,CAAC2O,WAAW,KAAK,QAAQ,GAAG3O,OAAO,CAAC2O,WAAW,GAAGpU,MAAM,CAACyF,OAAO,CAAC2O,WAAW,IAAI,gBAAgB,CAAC;oBAClI,MAAMK,WAAW,GAAG,OAAOhP,OAAO,CAACiP,YAAY,KAAK,QAAQ,IAAI,OAAOjP,OAAO,CAACiP,YAAY,KAAK,QAAQ,GAAGjP,OAAO,CAACiP,YAAY,GAAG1U,MAAM,CAACyF,OAAO,CAACiP,YAAY,IAAI,SAAS,CAAC;oBAE3K,oBACExW,OAAA;sBAAwBwB,KAAK,EAAEiC,SAAU;sBAAAyR,QAAA,GACtCoB,UAAU,EAAC,KAAG,EAACC,WAAW,EAAC,GAC9B;oBAAA,GAFa9S,SAAS;sBAAA0R,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAEd,CAAC;kBAEb,CAAC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eAENtV,OAAA;gBAAAkV,QAAA,gBACElV,OAAA;kBAAOiV,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAEhE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRtV,OAAA;kBACEwQ,IAAI,EAAC,MAAM;kBACXhP,KAAK,EAAE+B,QAAQ,CAACG,eAAgB;kBAChCgS,QAAQ,EAAErG,4BAA6B;kBACvCoG,WAAW,EAAC,6CAA6C;kBACzDR,SAAS,EAAC;gBAAa;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC,eACFtV,OAAA;kBAAGiV,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAC;gBAE1C;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eAENtV,OAAA;gBAAKiV,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,eAC1ClV,OAAA;kBAAOiV,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAClClV,OAAA;oBACEwQ,IAAI,EAAC,UAAU;oBACfhB,OAAO,EAAEjM,QAAQ,CAACM,QAAS;oBAC3B6R,QAAQ,EAAEnG,0BAA2B;oBACrC0F,SAAS,EAAC;kBAA2D;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtE,CAAC,eACFtV,OAAA;oBAAMiV,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eAENtV,OAAA;gBAAAkV,QAAA,gBACElV,OAAA;kBAAOiV,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAEhE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRtV,OAAA;kBACEwQ,IAAI,EAAC,QAAQ;kBACbhP,KAAK,EAAE+B,QAAQ,CAACO,eAAgB;kBAChC4R,QAAQ,EAAEpG,4BAA6B;kBACvCmG,WAAW,EAAC,GAAG;kBACfiB,GAAG,EAAC,GAAG;kBACPzB,SAAS,EAAC;gBAAa;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC,eACFtV,OAAA;kBAAGiV,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAC;gBAE1C;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNtV,OAAA;cAAKiV,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBlV,OAAA;gBAAKiV,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,gBAChDlV,OAAA;kBAAIiV,SAAS,EAAC,2BAA2B;kBAAAC,QAAA,GAAC,cAAY,EAAC3R,QAAQ,CAACQ,KAAK,CAAC6D,MAAM,EAAC,GAAC;gBAAA;kBAAAuN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACnFtV,OAAA;kBACEuV,OAAO,EAAEA,CAAA,KAAMrP,gBAAgB,CAAC,IAAI,CAAE;kBACtC+O,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,gBAEjClV,OAAA,CAACjC,QAAQ;oBAACkX,SAAS,EAAC;kBAAc;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,YAEvC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eAENtV,OAAA;gBAAKiV,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,EAChD3R,QAAQ,CAACQ,KAAK,CAACkG,GAAG,CAAC,CAAC1B,IAAI,EAAE2B,KAAK,kBAC9BlK,OAAA;kBAAiBiV,SAAS,EAAC,kDAAkD;kBAAAC,QAAA,eAC3ElV,OAAA;oBAAKiV,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,gBAC/ClV,OAAA;sBAAKiV,SAAS,EAAC,QAAQ;sBAAAC,QAAA,gBACrBlV,OAAA;wBAAKiV,SAAS,EAAC,kCAAkC;wBAAAC,QAAA,gBAC/ClV,OAAA;0BAAMiV,SAAS,EAAC,mCAAmC;0BAAAC,QAAA,GAAC,GAAC,EAAChL,KAAK,GAAG,CAAC;wBAAA;0BAAAiL,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC,eACvEtV,OAAA;0BAAMiV,SAAS,EAAC,mCAAmC;0BAAAC,QAAA,EAAE3M,IAAI,CAACpI;wBAAI;0BAAAgV,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC,eACtEtV,OAAA;0BAAMiV,SAAS,EAAE,uEACf1M,IAAI,CAACpE,QAAQ,KAAK,SAAS,GAAG,2BAA2B,GACzDoE,IAAI,CAACpE,QAAQ,KAAK,UAAU,GAAG,6BAA6B,GAC5D,+BAA+B,EAC9B;0BAAA+Q,QAAA,EACA3M,IAAI,CAACpE;wBAAQ;0BAAAgR,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACV,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CAAC,eACNtV,OAAA;wBAAGiV,SAAS,EAAC,4BAA4B;wBAAAC,QAAA,EAAE3M,IAAI,CAACrE;sBAAO;wBAAAiR,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,EAC3D,CAAC,MAAM;wBACN,IAAI/M,IAAI,CAACpE,QAAQ,KAAK,UAAU,IAAIoE,IAAI,CAAClE,OAAO,EAAE;0BAChD,IAAI4S,aAAa,GAAG,EAAE;0BACtB,IAAIzQ,KAAK,CAACC,OAAO,CAAC8B,IAAI,CAAClE,OAAO,CAAC,EAAE;4BAC/B4S,aAAa,GAAG1O,IAAI,CAAClE,OAAO;0BAC9B,CAAC,MAAM,IAAI,OAAOkE,IAAI,CAAClE,OAAO,KAAK,QAAQ,IAAIkE,IAAI,CAAClE,OAAO,CAACA,OAAO,EAAE;4BACnE4S,aAAa,GAAG1O,IAAI,CAAClE,OAAO,CAACA,OAAO,CAAC4F,GAAG,CAAC2B,GAAG,IAC1C,OAAOA,GAAG,KAAK,QAAQ,GAAGA,GAAG,GAAIA,GAAG,CAAC7J,YAAY,IAAI6J,GAAG,CAAC5J,KAAK,IAAI4J,GAAG,CAACzL,IAAI,IAAI,EAChF,CAAC;0BACH;0BAEA,IAAI8W,aAAa,CAACrP,MAAM,GAAG,CAAC,EAAE;4BAC5B,oBACE5H,OAAA;8BAAKiV,SAAS,EAAC,sBAAsB;8BAAAC,QAAA,EAClC+B,aAAa,CAAChN,GAAG,CAAC,CAAC6B,MAAM,EAAEiL,QAAQ,kBAClC/W,OAAA;gCAAqBiV,SAAS,EAAC,8EAA8E;gCAAAC,QAAA,EAC1G,OAAOpJ,MAAM,KAAK,QAAQ,GAAGA,MAAM,GAAGhK,MAAM,CAACgK,MAAM,IAAI,EAAE;8BAAC,GADlDiL,QAAQ;gCAAA5B,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAEb,CACP;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACC,CAAC;0BAEV;wBACF;wBACA,OAAO,IAAI;sBACb,CAAC,EAAE,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD,CAAC,eACNtV,OAAA;sBAAKiV,SAAS,EAAC,kCAAkC;sBAAAC,QAAA,gBAC/ClV,OAAA;wBACEuV,OAAO,EAAEA,CAAA,KAAM7H,cAAc,CAACxD,KAAK,CAAE;wBACrC+K,SAAS,EAAC,+CAA+C;wBAAAC,QAAA,eAEzDlV,OAAA,CAAC/B,UAAU;0BAACgX,SAAS,EAAC;wBAAS;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC5B,CAAC,eACTtV,OAAA;wBACEuV,OAAO,EAAEA,CAAA,KAAMzH,gBAAgB,CAAC5D,KAAK,CAAE;wBACvC+K,SAAS,EAAC,8CAA8C;wBAAAC,QAAA,eAExDlV,OAAA,CAAC9B,SAAS;0BAAC+W,SAAS,EAAC;wBAAS;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC3B,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC,GAvDEpL,KAAK;kBAAAiL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAwDV,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENtV,OAAA;YAAKiV,SAAS,EAAC,+DAA+D;YAAAC,QAAA,gBAC5ElV,OAAA;cACEuV,OAAO,EAAEA,CAAA,KAAM;gBACbrS,gBAAgB,CAAC,KAAK,CAAC;gBACvB8F,aAAa,CAAC,CAAC;cACjB,CAAE;cACFiM,SAAS,EAAC,eAAe;cAAAC,QAAA,EAC1B;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTtV,OAAA;cACEuV,OAAO,EAAExK,gBAAiB;cAC1BkK,SAAS,EAAC,aAAa;cACvBO,QAAQ,EACN,CAACjS,QAAQ,CAACpD,IAAI,CAAC4H,IAAI,CAAC,CAAC,IACrB,CAACxE,QAAQ,CAACG,eAAe,CAACqE,IAAI,CAAC,CAAC,IAChC,CAACxE,QAAQ,CAACE,SAAS,IACnBF,QAAQ,CAACQ,KAAK,CAAC6D,MAAM,KAAK,CAC3B;cAAAsN,QAAA,EACF;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAGArP,aAAa,iBACZjG,OAAA;MAAKiV,SAAS,EAAC,gFAAgF;MAAAC,QAAA,eAC7FlV,OAAA;QAAKiV,SAAS,EAAC,0EAA0E;QAAAC,QAAA,gBACvFlV,OAAA;UAAKiV,SAAS,EAAC,mBAAmB;UAAAC,QAAA,eAChClV,OAAA;YAAKiV,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBACrDlV,OAAA;cAAIiV,SAAS,EAAC,qCAAqC;cAAAC,QAAA,EAChD/O,gBAAgB,IAAI,CAAC,GAAG,WAAW,GAAG;YAAU;cAAAgP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CAAC,eACLtV,OAAA;cACEuV,OAAO,EAAEA,CAAA,KAAM;gBACbrP,gBAAgB,CAAC,KAAK,CAAC;gBACvBuH,aAAa,CAAC,CAAC;gBACfrH,mBAAmB,CAAC,CAAC,CAAC,CAAC;cACzB,CAAE;cACF6O,SAAS,EAAC,oEAAoE;cAAAC,QAAA,eAE9ElV,OAAA,CAACnB,SAAS;gBAACoW,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENtV,OAAA;UAAKiV,SAAS,EAAC,6BAA6B;UAAAC,QAAA,eAC1ClV,OAAA;YAAKiV,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBlV,OAAA;cAAAkV,QAAA,gBACElV,OAAA;gBAAOiV,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRtV,OAAA;gBACEwQ,IAAI,EAAC,MAAM;gBACXhP,KAAK,EAAEwC,QAAQ,CAAC7D,IAAK;gBACrBuV,QAAQ,EAAEjG,wBAAyB;gBACnCgG,WAAW,EAAC,uBAAuB;gBACnCR,SAAS,EAAC;cAAa;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENtV,OAAA;cAAAkV,QAAA,gBACElV,OAAA;gBAAOiV,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRtV,OAAA;gBACEwB,KAAK,EAAEwC,QAAQ,CAACG,QAAS;gBACzBuR,QAAQ,EAAG3G,CAAC,IAAK;kBACf;kBACA9K,WAAW,CAACyC,IAAI,KAAK;oBACnB,GAAGA,IAAI;oBACPvC,QAAQ,EAAE4K,CAAC,CAACC,MAAM,CAACxN,KAAK;oBACxB6C,OAAO,EAAE,EAAE;oBAAE;oBACbM,eAAe,EAAE,EAAE;oBAAE;oBACrBC,UAAU,EAAE,EAAE,CAAC;kBACjB,CAAC,CAAC,CAAC;gBACL,CAAE;gBACFqQ,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAEvBlV,OAAA;kBAAQwB,KAAK,EAAC,SAAS;kBAAA0T,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACxCtV,OAAA;kBAAQwB,KAAK,EAAC,UAAU;kBAAA0T,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC1CtV,OAAA;kBAAQwB,KAAK,EAAC,QAAQ;kBAAA0T,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtCtV,OAAA;kBAAQwB,KAAK,EAAC,WAAW;kBAAA0T,QAAA,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAGNtV,OAAA;cAAAkV,QAAA,gBACElV,OAAA;gBAAOiV,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRtV,OAAA;gBAAKiV,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,gBACrClV,OAAA;kBACEwQ,IAAI,EAAC,QAAQ;kBACb+E,OAAO,EAAEA,CAAA,KAAMtR,WAAW,CAACyC,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAEtC,WAAW,EAAE,MAAM;oBAAEG,UAAU,EAAE;kBAAG,CAAC,CAAC,CAAE;kBACvF0Q,SAAS,EAAE,uDACTjR,QAAQ,CAACI,WAAW,KAAK,MAAM,GAC3B,0CAA0C,GAC1C,uCAAuC,EAC1C;kBAAA8Q,QAAA,gBAEHlV,OAAA,CAACL,uBAAuB;oBAACsV,SAAS,EAAC;kBAAc;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACpDtV,OAAA;oBAAKiV,SAAS,EAAC,aAAa;oBAAAC,QAAA,EAAC;kBAAY;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC/CtV,OAAA;oBAAKiV,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAC;kBAA4B;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnE,CAAC,eACTtV,OAAA;kBACEwQ,IAAI,EAAC,QAAQ;kBACb+E,OAAO,EAAEA,CAAA,KAAMtR,WAAW,CAACyC,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAEtC,WAAW,EAAE;kBAAW,CAAC,CAAC,CAAE;kBAC3E6Q,SAAS,EAAE,uDACTjR,QAAQ,CAACI,WAAW,KAAK,UAAU,GAC/B,0CAA0C,GAC1C,uCAAuC,EAC1C;kBAAA8Q,QAAA,gBAEHlV,OAAA,CAAC1B,gBAAgB;oBAAC2W,SAAS,EAAC;kBAAc;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC7CtV,OAAA;oBAAKiV,SAAS,EAAC,aAAa;oBAAAC,QAAA,EAAC;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC3CtV,OAAA;oBAAKiV,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAC;kBAAuB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EAGLtR,QAAQ,CAACI,WAAW,KAAK,UAAU,iBAClCpE,OAAA;cAAAkV,QAAA,gBACElV,OAAA;gBAAOiV,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRtV,OAAA;gBACEwB,KAAK,EAAEwC,QAAQ,CAACO,UAAW;gBAC3BmR,QAAQ,EAAG3G,CAAC,IAAKf,oBAAoB,CAACe,CAAC,CAACC,MAAM,CAACxN,KAAK,CAAE;gBACtDyT,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAEvBlV,OAAA;kBAAQwB,KAAK,EAAC,EAAE;kBAAA0T,QAAA,EAAC;gBAAoB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EAC7C,CAAC9O,KAAK,CAACC,OAAO,CAAClE,SAAS,CAAC,GAAGA,SAAS,GAAG,EAAE,EAAE0H,GAAG,CAACiE,QAAQ,IAAI;kBAC3D;kBACA,IAAI,CAACA,QAAQ,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;oBAC7C,OAAO,IAAI;kBACb;kBAEA,MAAMgJ,YAAY,GAAGjX,cAAc,CAACiO,QAAQ,CAACsC,IAAI,IAAI,MAAM,CAAC;kBAC5D,MAAM2G,YAAY,GAAG,OAAOjJ,QAAQ,CAAC/N,IAAI,KAAK,QAAQ,GAAG+N,QAAQ,CAAC/N,IAAI,GAAG2B,MAAM,CAACoM,QAAQ,CAAC/N,IAAI,IAAI,kBAAkB,CAAC;kBACpH,MAAMiX,QAAQ,GAAG,QAAOF,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAE/W,IAAI,MAAK,QAAQ,GAAG+W,YAAY,CAAC/W,IAAI,GAAG2B,MAAM,CAACoM,QAAQ,CAACsC,IAAI,IAAI,MAAM,CAAC;kBAC7G,MAAMjM,UAAU,GAAG2J,QAAQ,CAACrE,EAAE,IAAI,YAAYuM,IAAI,CAACC,MAAM,CAAC,CAAC,EAAE;kBAE7D,oBACErW,OAAA;oBAAyBwB,KAAK,EAAE+C,UAAW;oBAAA2Q,QAAA,GACxCiC,YAAY,EAAC,IAAE,EAACC,QAAQ,EAAC,GAC5B;kBAAA,GAFa7S,UAAU;oBAAA4Q,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEf,CAAC;gBAEb,CAAC,CAAC,CAAChO,MAAM,CAACmP,OAAO,CAAC;cAAA;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CACN,eAGDtV,OAAA;cAAAkV,QAAA,gBACElV,OAAA;gBAAOiV,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,GAC5DlR,QAAQ,CAACI,WAAW,KAAK,UAAU,GAAG,iBAAiB,GAAG,iBAAiB,EAAC,IAC/E;cAAA;gBAAA+Q,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRtV,OAAA;gBACEwB,KAAK,EAAEwC,QAAQ,CAACE,OAAQ;gBACxBwR,QAAQ,EAAEhG,2BAA4B;gBACtC+F,WAAW,EAAEzR,QAAQ,CAACI,WAAW,KAAK,UAAU,GAAG,qCAAqC,GAAG,oCAAqC;gBAChI+R,IAAI,EAAE,CAAE;gBACRlB,SAAS,EAAC,yBAAyB;gBACnCoC,QAAQ,EAAErT,QAAQ,CAACI,WAAW,KAAK;cAAW;gBAAA+Q,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,EAGLtR,QAAQ,CAACI,WAAW,KAAK,UAAU,IAAIJ,QAAQ,CAACO,UAAU,IAAI+S,MAAM,CAACC,IAAI,CAACvT,QAAQ,CAACQ,SAAS,CAAC,CAACoD,MAAM,GAAG,CAAC,iBACvG5H,OAAA;cAAAkV,QAAA,gBACElV,OAAA;gBAAOiV,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRtV,OAAA;gBAAKiV,SAAS,EAAC,WAAW;gBAAAC,QAAA,EACvBoC,MAAM,CAACC,IAAI,CAACvT,QAAQ,CAACQ,SAAS,CAAC,CAACyF,GAAG,CAACqE,QAAQ,IAAI;kBAC/C;kBACA,MAAMkJ,YAAY,GAAG,OAAOlJ,QAAQ,KAAK,QAAQ,GAAGA,QAAQ,GAAGxM,MAAM,CAACwM,QAAQ,IAAI,EAAE,CAAC;kBACrF,oBACEtO,OAAA;oBAAAkV,QAAA,gBACElV,OAAA;sBAAOiV,SAAS,EAAC,8CAA8C;sBAAAC,QAAA,EAC5DsC;oBAAY;sBAAArC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACR,CAAC,eACRtV,OAAA;sBACEwQ,IAAI,EAAC,MAAM;sBACXhP,KAAK,EAAE,OAAOwC,QAAQ,CAACQ,SAAS,CAACgT,YAAY,CAAC,KAAK,QAAQ,GAAGxT,QAAQ,CAACQ,SAAS,CAACgT,YAAY,CAAC,GAAG1V,MAAM,CAACkC,QAAQ,CAACQ,SAAS,CAACgT,YAAY,CAAC,IAAI,EAAE,CAAE;sBAChJ9B,QAAQ,EAAG3G,CAAC,IAAK9K,WAAW,CAACyC,IAAI,KAAK;wBACpC,GAAGA,IAAI;wBACPlC,SAAS,EAAE;0BACT,GAAGkC,IAAI,CAAClC,SAAS;0BACjB,CAACgT,YAAY,GAAGzI,CAAC,CAACC,MAAM,CAACxN;wBAC3B;sBACF,CAAC,CAAC,CAAE;sBACJiU,WAAW,EAAE,qBAAqB+B,YAAY,IAAK;sBACnDvC,SAAS,EAAC;oBAAa;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxB,CAAC;kBAAA,GAhBMkC,YAAY;oBAAArC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAiBjB,CAAC;gBAEV,CAAC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN,EAGAtR,QAAQ,CAACI,WAAW,KAAK,MAAM,iBAC9BpE,OAAA;cAAAkV,QAAA,gBACElV,OAAA;gBAAOiV,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRtV,OAAA;gBAAKiV,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxBlV,OAAA;kBAAKiV,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1ClV,OAAA;oBACEwB,KAAK,EAAEwC,QAAQ,CAACU,cAAe;oBAC/BgR,QAAQ,EAAE/F,kCAAmC;oBAC7CsF,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,gBAE5BlV,OAAA;sBAAQwB,KAAK,EAAC,OAAO;sBAAA0T,QAAA,EAAC;oBAAK;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACpCtV,OAAA;sBAAQwB,KAAK,EAAC,OAAO;sBAAA0T,QAAA,EAAC;oBAAK;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACpCtV,OAAA;sBAAQwB,KAAK,EAAC,OAAO;sBAAA0T,QAAA,EAAC;oBAAK;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACpCtV,OAAA;sBAAQwB,KAAK,EAAC,UAAU;sBAAA0T,QAAA,EAAC;oBAAQ;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpC,CAAC,eACTtV,OAAA;oBACEwQ,IAAI,EAAC,MAAM;oBACXkF,QAAQ,EAAE9F,kCAAmC;oBAC7C6H,MAAM,EACJzT,QAAQ,CAACU,cAAc,KAAK,OAAO,GAAG,SAAS,GAC/CV,QAAQ,CAACU,cAAc,KAAK,OAAO,GAAG,SAAS,GAC/CV,QAAQ,CAACU,cAAc,KAAK,OAAO,GAAG,SAAS,GAC/C,KACD;oBACDuQ,SAAS,EAAC;kBAAoB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,EACLtR,QAAQ,CAACS,cAAc,iBACtBzE,OAAA;kBAAKiV,SAAS,EAAC,WAAW;kBAAAC,QAAA,gBACxBlV,OAAA;oBAAKiV,SAAS,EAAC,mDAAmD;oBAAAC,QAAA,gBAChElV,OAAA,CAACf,aAAa;sBAACgW,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACrCtV,OAAA;sBAAAkV,QAAA,EAAO,OAAOlR,QAAQ,CAACS,cAAc,CAACtE,IAAI,KAAK,QAAQ,GAAG6D,QAAQ,CAACS,cAAc,CAACtE,IAAI,GAAG2B,MAAM,CAACkC,QAAQ,CAACS,cAAc,CAACtE,IAAI,IAAI,cAAc;oBAAC;sBAAAgV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eACvJtV,OAAA;sBACEuV,OAAO,EAAEA,CAAA,KAAMtR,WAAW,CAACyC,IAAI,KAAK;wBAAE,GAAGA,IAAI;wBAAEjC,cAAc,EAAE;sBAAK,CAAC,CAAC,CAAE;sBACxEwQ,SAAS,EAAC,iCAAiC;sBAAAC,QAAA,eAE3ClV,OAAA,CAACnB,SAAS;wBAACoW,SAAS,EAAC;sBAAS;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3B,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,EAGLtR,QAAQ,CAACG,QAAQ,KAAK,UAAU,iBAC/BnE,OAAA;oBAAKiV,SAAS,EAAC,sDAAsD;oBAAAC,QAAA,eACnElV,OAAA;sBAAKiV,SAAS,EAAC,4BAA4B;sBAAAC,QAAA,gBACzClV,OAAA;wBAAKiV,SAAS,EAAC,8CAA8C;wBAACyC,IAAI,EAAC,cAAc;wBAACC,OAAO,EAAC,WAAW;wBAAAzC,QAAA,eACnGlV,OAAA;0BAAM4X,QAAQ,EAAC,SAAS;0BAACC,CAAC,EAAC,mNAAmN;0BAACC,QAAQ,EAAC;wBAAS;0BAAA3C,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACjQ,CAAC,eACNtV,OAAA;wBAAAkV,QAAA,gBACElV,OAAA;0BAAGiV,SAAS,EAAC,qCAAqC;0BAAAC,QAAA,EAAC;wBAEnD;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAG,CAAC,eACJtV,OAAA;0BAAGiV,SAAS,EAAC,8BAA8B;0BAAAC,QAAA,EAAC;wBAE5C;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAG,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACD,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN,EAEAtR,QAAQ,CAACG,QAAQ,KAAK,UAAU,iBAC/BnE,OAAA;cAAKiV,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBlV,OAAA;gBAAAkV,QAAA,gBACElV,OAAA;kBAAOiV,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAEhE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRtV,OAAA;kBAAKiV,SAAS,EAAC,WAAW;kBAAAC,QAAA,GACvB,CAAC1O,KAAK,CAACC,OAAO,CAACzC,QAAQ,CAACK,OAAO,CAAC,GAAGL,QAAQ,CAACK,OAAO,GAAG,EAAE,EAAE4F,GAAG,CAAC,CAAC6B,MAAM,EAAE5B,KAAK,KAAK;oBAChF;oBACA,IAAI6N,WAAW,GAAG,EAAE;oBACpB,IAAI,OAAOjM,MAAM,KAAK,QAAQ,EAAE;sBAC9BiM,WAAW,GAAGjM,MAAM;oBACtB,CAAC,MAAM,IAAI,OAAOA,MAAM,KAAK,QAAQ,IAAIA,MAAM,KAAK,IAAI,EAAE;sBACxDiM,WAAW,GAAGjW,MAAM,CAACgK,MAAM,CAAC/J,YAAY,IAAI+J,MAAM,CAAC9J,KAAK,IAAI8J,MAAM,CAAC3L,IAAI,IAAI2L,MAAM,CAAC5L,IAAI,IAAI4L,MAAM,CAACtK,KAAK,IAAI,EAAE,CAAC;oBAC/G,CAAC,MAAM;sBACLuW,WAAW,GAAGjW,MAAM,CAACgK,MAAM,IAAI,EAAE,CAAC;oBACpC;oBAEA,oBACE9L,OAAA;sBAAiBiV,SAAS,EAAC,6BAA6B;sBAAAC,QAAA,gBACtDlV,OAAA;wBACEwQ,IAAI,EAAC,MAAM;wBACXhP,KAAK,EAAEuW,WAAY;wBACnBrC,QAAQ,EAAG3G,CAAC,IAAKJ,YAAY,CAACzE,KAAK,EAAE6E,CAAC,CAACC,MAAM,CAACxN,KAAK,CAAE;wBACrDiU,WAAW,EAAE,UAAUvL,KAAK,GAAG,CAAC,EAAG;wBACnC+K,SAAS,EAAC;sBAAoB;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC/B,CAAC,eACFtV,OAAA;wBACEuV,OAAO,EAAEA,CAAA,KAAM1G,YAAY,CAAC3E,KAAK,CAAE;wBACnC+K,SAAS,EAAC,6CAA6C;wBAAAC,QAAA,eAEvDlV,OAAA,CAAC9B,SAAS;0BAAC+W,SAAS,EAAC;wBAAS;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC3B,CAAC;oBAAA,GAbDpL,KAAK;sBAAAiL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAcV,CAAC;kBAEV,CAAC,CAAC,eACFtV,OAAA;oBACEuV,OAAO,EAAE9G,SAAU;oBACnBwG,SAAS,EAAC,8BAA8B;oBAAAC,QAAA,EACzC;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNtV,OAAA;gBAAAkV,QAAA,gBACElV,OAAA;kBAAOiV,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAEhE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRtV,OAAA;kBACEwB,KAAK,EAAEwC,QAAQ,CAACgI,eAAe,IAAI,SAAU;kBAC7C0J,QAAQ,EAAG3G,CAAC,IAAK9K,WAAW,CAACyC,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAEsF,eAAe,EAAE+C,CAAC,CAACC,MAAM,CAACxN;kBAAM,CAAC,CAAC,CAAE;kBACrFyT,SAAS,EAAC,aAAa;kBAAAC,QAAA,gBAEvBlV,OAAA;oBAAQwB,KAAK,EAAC,SAAS;oBAAA0T,QAAA,EAAC;kBAAgC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACjEtV,OAAA;oBAAQwB,KAAK,EAAC,MAAM;oBAAA0T,QAAA,EAAC;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjC,CAAC,eACTtV,OAAA;kBAAGiV,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAC;gBAE1C;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,EAGHtR,QAAQ,CAACgI,eAAe,KAAK,SAAS,IAAIhI,QAAQ,CAACS,cAAc,iBAChEzE,OAAA;kBAAKiV,SAAS,EAAC,2DAA2D;kBAAAC,QAAA,eACxElV,OAAA;oBAAKiV,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,gBACzClV,OAAA;sBAAKiV,SAAS,EAAC,8CAA8C;sBAACyC,IAAI,EAAC,cAAc;sBAACC,OAAO,EAAC,WAAW;sBAAAzC,QAAA,eACnGlV,OAAA;wBAAM4X,QAAQ,EAAC,SAAS;wBAACC,CAAC,EAAC,mNAAmN;wBAACC,QAAQ,EAAC;sBAAS;wBAAA3C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjQ,CAAC,eACNtV,OAAA;sBAAAkV,QAAA,gBACElV,OAAA;wBAAGiV,SAAS,EAAC,qCAAqC;wBAAAC,QAAA,EAAC;sBAEnD;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC,eACJtV,OAAA;wBAAGiV,SAAS,EAAC,8BAA8B;wBAAAC,QAAA,EAAC;sBAE5C;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAGNtV,OAAA;gBAAAkV,QAAA,gBACElV,OAAA;kBAAOiV,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAEhE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRtV,OAAA;kBACEwQ,IAAI,EAAC,MAAM;kBACXhP,KAAK,EAAEwC,QAAQ,CAACW,eAAgB;kBAChC+Q,QAAQ,EAAE5F,mCAAoC;kBAC9C2F,WAAW,EAAC,0BAA0B;kBACtCR,SAAS,EAAC;gBAAa;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC,eACFtV,OAAA;kBAAGiV,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAC;gBAE1C;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN,EAGAtR,QAAQ,CAACG,QAAQ,KAAK,QAAQ,iBAC7BnE,OAAA;cAAKiV,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBlV,OAAA;gBAAAkV,QAAA,gBACElV,OAAA;kBAAOiV,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAEhE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRtV,OAAA;kBACEwB,KAAK,EAAEwC,QAAQ,CAACa,UAAU,IAAI,SAAU;kBACxC6Q,QAAQ,EAAG3G,CAAC,IAAK9K,WAAW,CAACyC,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAE7B,UAAU,EAAEkK,CAAC,CAACC,MAAM,CAACxN;kBAAM,CAAC,CAAC,CAAE;kBAChFyT,SAAS,EAAC,aAAa;kBAAAC,QAAA,gBAEvBlV,OAAA;oBAAQwB,KAAK,EAAC,SAAS;oBAAA0T,QAAA,EAAC;kBAAY;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC7CtV,OAAA;oBAAQwB,KAAK,EAAC,OAAO;oBAAA0T,QAAA,EAAC;kBAAU;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACzCtV,OAAA;oBAAQwB,KAAK,EAAC,WAAW;oBAAA0T,QAAA,EAAC;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC5CtV,OAAA;oBAAQwB,KAAK,EAAC,UAAU;oBAAA0T,QAAA,EAAC;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC1CtV,OAAA;oBAAQwB,KAAK,EAAC,OAAO;oBAAA0T,QAAA,EAAC;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,EAELtR,QAAQ,CAACa,UAAU,KAAK,SAAS,iBAChC7E,OAAA;gBAAAkV,QAAA,gBACElV,OAAA;kBAAOiV,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAEhE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRtV,OAAA;kBACEwQ,IAAI,EAAC,KAAK;kBACVhP,KAAK,EAAEwC,QAAQ,CAACc,UAAU,IAAI,EAAG;kBACjC4Q,QAAQ,EAAG3G,CAAC,IAAK9K,WAAW,CAACyC,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAE5B,UAAU,EAAEiK,CAAC,CAACC,MAAM,CAACxN;kBAAM,CAAC,CAAC,CAAE;kBAChFiU,WAAW,EAAC,uCAAuC;kBACnDR,SAAS,EAAC;gBAAa;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC,eACFtV,OAAA;kBAAGiV,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAC;gBAE1C;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CACN,EAEAtR,QAAQ,CAACa,UAAU,KAAK,OAAO,iBAC9B7E,OAAA;gBAAAkV,QAAA,gBACElV,OAAA;kBAAOiV,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAEhE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRtV,OAAA;kBACEwQ,IAAI,EAAC,QAAQ;kBACbkG,GAAG,EAAC,GAAG;kBACPsB,GAAG,EAAC,KAAK;kBACTxW,KAAK,EAAEwC,QAAQ,CAACe,YAAY,IAAI,CAAE;kBAClC2Q,QAAQ,EAAG3G,CAAC,IAAK9K,WAAW,CAACyC,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAE3B,YAAY,EAAE+D,QAAQ,CAACiG,CAAC,CAACC,MAAM,CAACxN,KAAK,CAAC,IAAI;kBAAE,CAAC,CAAC,CAAE;kBACjGyT,SAAS,EAAC;gBAAa;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC,eACFtV,OAAA;kBAAGiV,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAC;gBAE1C;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CACN,EAEAtR,QAAQ,CAACa,UAAU,KAAK,OAAO,iBAC9B7E,OAAA;gBAAKiV,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxBlV,OAAA;kBAAAkV,QAAA,gBACElV,OAAA;oBAAOiV,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAEhE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACRtV,OAAA;oBACEwQ,IAAI,EAAC,OAAO;oBACZhP,KAAK,EAAEwC,QAAQ,CAACgB,eAAe,IAAI,EAAG;oBACtC0Q,QAAQ,EAAG3G,CAAC,IAAK9K,WAAW,CAACyC,IAAI,KAAK;sBAAE,GAAGA,IAAI;sBAAE1B,eAAe,EAAE+J,CAAC,CAACC,MAAM,CAACxN;oBAAM,CAAC,CAAC,CAAE;oBACrFiU,WAAW,EAAC,wCAAwC;oBACpDR,SAAS,EAAC;kBAAa;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxB,CAAC,eACFtV,OAAA;oBAAGiV,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,EAAC;kBAE1C;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,eACNtV,OAAA;kBAAAkV,QAAA,gBACElV,OAAA;oBAAOiV,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAEhE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACRtV,OAAA;oBACEwQ,IAAI,EAAC,MAAM;oBACXhP,KAAK,EAAEwC,QAAQ,CAACiB,YAAY,IAAI,EAAG;oBACnCyQ,QAAQ,EAAG3G,CAAC,IAAK9K,WAAW,CAACyC,IAAI,KAAK;sBAAE,GAAGA,IAAI;sBAAEzB,YAAY,EAAE8J,CAAC,CAACC,MAAM,CAACxN;oBAAM,CAAC,CAAC,CAAE;oBAClFiU,WAAW,EAAC,+BAA+B;oBAC3CR,SAAS,EAAC;kBAAa;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNtV,OAAA;kBAAAkV,QAAA,gBACElV,OAAA;oBAAOiV,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAEhE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACRtV,OAAA;oBACEwB,KAAK,EAAEwC,QAAQ,CAACkB,SAAS,IAAI,EAAG;oBAChCwQ,QAAQ,EAAG3G,CAAC,IAAK9K,WAAW,CAACyC,IAAI,KAAK;sBAAE,GAAGA,IAAI;sBAAExB,SAAS,EAAE6J,CAAC,CAACC,MAAM,CAACxN;oBAAM,CAAC,CAAC,CAAE;oBAC/EiU,WAAW,EAAC,6EAA6E;oBACzFR,SAAS,EAAC,kBAAkB;oBAC5BkB,IAAI,EAAE;kBAAE;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC,eACFtV,OAAA;oBAAGiV,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,GAAC,MACpC,EAAC,mBAAmB,EAAC,4CAC3B;kBAAA;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,eACNtV,OAAA;kBAAAkV,QAAA,gBACElV,OAAA;oBAAOiV,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAEhE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACRtV,OAAA;oBACEwB,KAAK,EAAEwC,QAAQ,CAACmB,aAAa,IAAI,EAAG;oBACpCuQ,QAAQ,EAAG3G,CAAC,IAAK9K,WAAW,CAACyC,IAAI,KAAK;sBAAE,GAAGA,IAAI;sBAAEvB,aAAa,EAAE4J,CAAC,CAACC,MAAM,CAACxN;oBAAM,CAAC,CAAC,CAAE;oBACnFyT,SAAS,EAAC,aAAa;oBAAAC,QAAA,gBAEvBlV,OAAA;sBAAQwB,KAAK,EAAC,EAAE;sBAAA0T,QAAA,EAAC;oBAAW;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACrCtV,OAAA;sBAAQwB,KAAK,EAAC,SAAS;sBAAA0T,QAAA,EAAC;oBAAa;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAC9CtV,OAAA;sBAAQwB,KAAK,EAAC,UAAU;sBAAA0T,QAAA,EAAC;oBAAe;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACjDtV,OAAA;sBAAQwB,KAAK,EAAC,cAAc;sBAAA0T,QAAA,EAAC;oBAAkB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN,EAEAtR,QAAQ,CAACa,UAAU,KAAK,UAAU,iBACjC7E,OAAA;gBAAKiV,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxBlV,OAAA;kBAAAkV,QAAA,gBACElV,OAAA;oBAAOiV,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAEhE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACRtV,OAAA;oBACEwQ,IAAI,EAAC,KAAK;oBACVhP,KAAK,EAAEwC,QAAQ,CAACqB,WAAW,IAAI,EAAG;oBAClCqQ,QAAQ,EAAG3G,CAAC,IAAK9K,WAAW,CAACyC,IAAI,KAAK;sBAAE,GAAGA,IAAI;sBAAErB,WAAW,EAAE0J,CAAC,CAACC,MAAM,CAACxN;oBAAM,CAAC,CAAC,CAAE;oBACjFiU,WAAW,EAAC,kCAAkC;oBAC9CR,SAAS,EAAC;kBAAa;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNtV,OAAA;kBAAAkV,QAAA,gBACElV,OAAA;oBAAOiV,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAEhE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACRtV,OAAA;oBACEwB,KAAK,EAAEwC,QAAQ,CAACsB,SAAS,IAAI,MAAO;oBACpCoQ,QAAQ,EAAG3G,CAAC,IAAK9K,WAAW,CAACyC,IAAI,KAAK;sBAAE,GAAGA,IAAI;sBAAEpB,SAAS,EAAEyJ,CAAC,CAACC,MAAM,CAACxN;oBAAM,CAAC,CAAC,CAAE;oBAC/EyT,SAAS,EAAC,aAAa;oBAAAC,QAAA,gBAEvBlV,OAAA;sBAAQwB,KAAK,EAAC,KAAK;sBAAA0T,QAAA,EAAC;oBAAG;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAChCtV,OAAA;sBAAQwB,KAAK,EAAC,MAAM;sBAAA0T,QAAA,EAAC;oBAAI;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAClCtV,OAAA;sBAAQwB,KAAK,EAAC,KAAK;sBAAA0T,QAAA,EAAC;oBAAG;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAChCtV,OAAA;sBAAQwB,KAAK,EAAC,OAAO;sBAAA0T,QAAA,EAAC;oBAAK;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACpCtV,OAAA;sBAAQwB,KAAK,EAAC,QAAQ;sBAAA0T,QAAA,EAAC;oBAAM;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eACNtV,OAAA;kBAAAkV,QAAA,gBACElV,OAAA;oBAAOiV,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAEhE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACRtV,OAAA;oBACEwB,KAAK,EAAEwC,QAAQ,CAACuB,UAAU,IAAI,EAAG;oBACjCmQ,QAAQ,EAAG3G,CAAC,IAAK9K,WAAW,CAACyC,IAAI,KAAK;sBAAE,GAAGA,IAAI;sBAAEnB,UAAU,EAAEwJ,CAAC,CAACC,MAAM,CAACxN;oBAAM,CAAC,CAAC,CAAE;oBAChFiU,WAAW,EAAC,+EAAuE;oBACnFR,SAAS,EAAC,kBAAkB;oBAC5BkB,IAAI,EAAE;kBAAE;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNtV,OAAA;kBAAAkV,QAAA,gBACElV,OAAA;oBAAOiV,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAEhE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACRtV,OAAA;oBACEwB,KAAK,EAAEwC,QAAQ,CAACwB,OAAO,IAAI,EAAG;oBAC9BkQ,QAAQ,EAAG3G,CAAC,IAAK9K,WAAW,CAACyC,IAAI,KAAK;sBAAE,GAAGA,IAAI;sBAAElB,OAAO,EAAEuJ,CAAC,CAACC,MAAM,CAACxN;oBAAM,CAAC,CAAC,CAAE;oBAC7EiU,WAAW,EAAC,oFAA4E;oBACxFR,SAAS,EAAC,kBAAkB;oBAC5BkB,IAAI,EAAE;kBAAE;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC,eACFtV,OAAA;oBAAGiV,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,GAAC,MACpC,EAAC,mBAAmB,EAAC,4CAC3B;kBAAA;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN,EAEAtR,QAAQ,CAACa,UAAU,KAAK,WAAW,iBAClC7E,OAAA;gBAAKiV,SAAS,EAAC,WAAW;gBAAAC,QAAA,eACxBlV,OAAA;kBAAAkV,QAAA,gBACElV,OAAA;oBAAOiV,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAEhE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACRtV,OAAA;oBACEwB,KAAK,EAAEgF,KAAK,CAACC,OAAO,CAACzC,QAAQ,CAACoB,cAAc,CAAC,GAAGpB,QAAQ,CAACoB,cAAc,CAAC6S,IAAI,CAAC,IAAI,CAAC,GAAG,EAAG;oBACxFvC,QAAQ,EAAG3G,CAAC,IAAK9K,WAAW,CAACyC,IAAI,KAAK;sBACpC,GAAGA,IAAI;sBACPtB,cAAc,EAAE2J,CAAC,CAACC,MAAM,CAACxN,KAAK,CAAC8P,KAAK,CAAC,IAAI,CAAC,CAAChK,MAAM,CAAC4Q,KAAK,IAAIA,KAAK,CAACnQ,IAAI,CAAC,CAAC;oBACzE,CAAC,CAAC,CAAE;oBACJ0N,WAAW,EAAC,uDAAgE;oBAC5ER,SAAS,EAAC,kBAAkB;oBAC5BkB,IAAI,EAAE;kBAAE;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC,eACFtV,OAAA;oBAAGiV,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,EAAC;kBAE1C;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN,EAGAtR,QAAQ,CAACyB,aAAa,KAAK,QAAQ,iBAClCzF,OAAA;gBAAKiV,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,gBACtClV,OAAA;kBAAIiV,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAkB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACzEtV,OAAA;kBAAKiV,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBACrClV,OAAA;oBAAAkV,QAAA,gBACElV,OAAA;sBAAOiV,SAAS,EAAC,+CAA+C;sBAAAC,QAAA,EAAC;oBAEjE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eACRtV,OAAA;sBACEwB,KAAK,EAAEwC,QAAQ,CAAC+B,QAAQ,IAAI,EAAG;sBAC/B2P,QAAQ,EAAG3G,CAAC,IAAK9K,WAAW,CAACyC,IAAI,KAAK;wBAAE,GAAGA,IAAI;wBAAEX,QAAQ,EAAEgJ,CAAC,CAACC,MAAM,CAACxN,KAAK,IAAI;sBAAK,CAAC,CAAC,CAAE;sBACtFyT,SAAS,EAAC,qDAAqD;sBAAAC,QAAA,gBAE/DlV,OAAA;wBAAQwB,KAAK,EAAC,EAAE;wBAAA0T,QAAA,EAAC;sBAAgB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,EACzC/R,QAAQ,CAACQ,KAAK,CAACkG,GAAG,CAAC,CAAC1B,IAAI,EAAE4P,SAAS,kBAClCnY,OAAA;wBAAwBwB,KAAK,EAAE2W,SAAS,GAAG,CAAE;wBAAAjD,QAAA,GAC1CiD,SAAS,GAAG,CAAC,EAAC,IAAE,EAAC5P,IAAI,CAACpI,IAAI;sBAAA,GADhBgY,SAAS;wBAAAhD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAEd,CACT,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eACNtV,OAAA;oBAAAkV,QAAA,gBACElV,OAAA;sBAAOiV,SAAS,EAAC,6CAA6C;sBAAAC,QAAA,EAAC;oBAE/D;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eACRtV,OAAA;sBACEwB,KAAK,EAAEwC,QAAQ,CAACgC,SAAS,IAAI,EAAG;sBAChC0P,QAAQ,EAAG3G,CAAC,IAAK9K,WAAW,CAACyC,IAAI,KAAK;wBAAE,GAAGA,IAAI;wBAAEV,SAAS,EAAE+I,CAAC,CAACC,MAAM,CAACxN,KAAK,IAAI;sBAAK,CAAC,CAAC,CAAE;sBACvFyT,SAAS,EAAC,iDAAiD;sBAAAC,QAAA,gBAE3DlV,OAAA;wBAAQwB,KAAK,EAAC,EAAE;wBAAA0T,QAAA,EAAC;sBAAgB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,EACzC/R,QAAQ,CAACQ,KAAK,CAACkG,GAAG,CAAC,CAAC1B,IAAI,EAAE4P,SAAS,kBAClCnY,OAAA;wBAAwBwB,KAAK,EAAE2W,SAAS,GAAG,CAAE;wBAAAjD,QAAA,GAC1CiD,SAAS,GAAG,CAAC,EAAC,IAAE,EAAC5P,IAAI,CAACpI,IAAI;sBAAA,GADhBgY,SAAS;wBAAAhD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAEd,CACT,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNtV,OAAA;kBAAGiV,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAErC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CACN,EAGAtR,QAAQ,CAACG,QAAQ,KAAK,WAAW,iBAChCnE,OAAA;cAAKiV,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBlV,OAAA;gBAAAkV,QAAA,gBACElV,OAAA;kBAAOiV,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAEhE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRtV,OAAA;kBACEwB,KAAK,EAAEwC,QAAQ,CAACyB,aAAa,IAAI,eAAgB;kBACjDiQ,QAAQ,EAAG3G,CAAC,IAAK9K,WAAW,CAACyC,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAEjB,aAAa,EAAEsJ,CAAC,CAACC,MAAM,CAACxN;kBAAM,CAAC,CAAC,CAAE;kBACnFyT,SAAS,EAAC,aAAa;kBAAAC,QAAA,gBAEvBlV,OAAA;oBAAQwB,KAAK,EAAC,eAAe;oBAAA0T,QAAA,EAAC;kBAAsB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC7DtV,OAAA;oBAAQwB,KAAK,EAAC,gBAAgB;oBAAA0T,QAAA,EAAC;kBAAuB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC/DtV,OAAA;oBAAQwB,KAAK,EAAC,YAAY;oBAAA0T,QAAA,EAAC;kBAAoB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACxDtV,OAAA;oBAAQwB,KAAK,EAAC,QAAQ;oBAAA0T,QAAA,EAAC;kBAAgB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,EAELtR,QAAQ,CAACyB,aAAa,KAAK,eAAe,iBACzCzF,OAAA;gBAAAkV,QAAA,gBACElV,OAAA;kBAAOiV,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAEhE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRtV,OAAA;kBAAKiV,SAAS,EAAC,WAAW;kBAAAC,QAAA,GACvB,CAAClR,QAAQ,CAAC0B,kBAAkB,IAAI,EAAE,EAAEuE,GAAG,CAAC,CAACmO,SAAS,EAAElO,KAAK,kBACxDlK,OAAA;oBAAiBiV,SAAS,EAAC,gBAAgB;oBAAAC,QAAA,gBACzClV,OAAA;sBACEwQ,IAAI,EAAC,MAAM;sBACXhP,KAAK,EAAE4W,SAAS,CAACnC,OAAO,IAAI,EAAG;sBAC/BP,QAAQ,EAAG3G,CAAC,IAAK;wBACf,MAAMsJ,OAAO,GAAG,CAAC,IAAIrU,QAAQ,CAAC0B,kBAAkB,IAAI,EAAE,CAAC,CAAC;wBACxD2S,OAAO,CAACnO,KAAK,CAAC,GAAG;0BAAE,GAAGmO,OAAO,CAACnO,KAAK,CAAC;0BAAE+L,OAAO,EAAElH,CAAC,CAACC,MAAM,CAACxN;wBAAM,CAAC;wBAC/DyC,WAAW,CAACyC,IAAI,KAAK;0BAAE,GAAGA,IAAI;0BAAEhB,kBAAkB,EAAE2S;wBAAQ,CAAC,CAAC,CAAC;sBACjE,CAAE;sBACF5C,WAAW,EAAC,kBAAkB;sBAC9BR,SAAS,EAAC;oBAAoB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/B,CAAC,eACFtV,OAAA;sBACEwB,KAAK,EAAE4W,SAAS,CAACE,QAAQ,IAAI,EAAG;sBAChC5C,QAAQ,EAAG3G,CAAC,IAAK;wBACf,MAAMsJ,OAAO,GAAG,CAAC,IAAIrU,QAAQ,CAAC0B,kBAAkB,IAAI,EAAE,CAAC,CAAC;wBACxD2S,OAAO,CAACnO,KAAK,CAAC,GAAG;0BAAE,GAAGmO,OAAO,CAACnO,KAAK,CAAC;0BAAEoO,QAAQ,EAAEvJ,CAAC,CAACC,MAAM,CAACxN;wBAAM,CAAC;wBAChEyC,WAAW,CAACyC,IAAI,KAAK;0BAAE,GAAGA,IAAI;0BAAEhB,kBAAkB,EAAE2S;wBAAQ,CAAC,CAAC,CAAC;sBACjE,CAAE;sBACFpD,SAAS,EAAC,kBAAkB;sBAAAC,QAAA,gBAE5BlV,OAAA;wBAAQwB,KAAK,EAAC,EAAE;wBAAA0T,QAAA,EAAC;sBAAgB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,EACzC/R,QAAQ,CAACQ,KAAK,CAACkG,GAAG,CAAC,CAAC1B,IAAI,EAAE4P,SAAS,kBAClCnY,OAAA;wBAAwBwB,KAAK,EAAE2W,SAAS,GAAG,CAAE;wBAAAjD,QAAA,GAC1CiD,SAAS,GAAG,CAAC,EAAC,IAAE,EAAC5P,IAAI,CAACpI,IAAI;sBAAA,GADhBgY,SAAS;wBAAAhD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAEd,CACT,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACI,CAAC,eACTtV,OAAA;sBACEwQ,IAAI,EAAC,QAAQ;sBACb+E,OAAO,EAAEA,CAAA,KAAM;wBACb,MAAM8C,OAAO,GAAG,CAACrU,QAAQ,CAAC0B,kBAAkB,IAAI,EAAE,EAAE4B,MAAM,CAAC,CAACyG,CAAC,EAAEzF,CAAC,KAAKA,CAAC,KAAK4B,KAAK,CAAC;wBACjFjG,WAAW,CAACyC,IAAI,KAAK;0BAAE,GAAGA,IAAI;0BAAEhB,kBAAkB,EAAE2S;wBAAQ,CAAC,CAAC,CAAC;sBACjE,CAAE;sBACFpD,SAAS,EAAC,2CAA2C;sBAAAC,QAAA,EACtD;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA,GArCDpL,KAAK;oBAAAiL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAsCV,CACN,CAAC,eACFtV,OAAA;oBACEwQ,IAAI,EAAC,QAAQ;oBACb+E,OAAO,EAAEA,CAAA,KAAM;sBACb,MAAM8C,OAAO,GAAG,CAAC,IAAIrU,QAAQ,CAAC0B,kBAAkB,IAAI,EAAE,CAAC,EAAE;wBAAEuQ,OAAO,EAAE,EAAE;wBAAEqC,QAAQ,EAAE;sBAAG,CAAC,CAAC;sBACvFrU,WAAW,CAACyC,IAAI,KAAK;wBAAE,GAAGA,IAAI;wBAAEhB,kBAAkB,EAAE2S;sBAAQ,CAAC,CAAC,CAAC;oBACjE,CAAE;oBACFpD,SAAS,EAAC,mFAAmF;oBAAAC,QAAA,EAC9F;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eACNtV,OAAA;kBAAGiV,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAC;gBAE1C;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CACN,EAEAtR,QAAQ,CAACyB,aAAa,KAAK,gBAAgB,iBAC1CzF,OAAA;gBAAKiV,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxBlV,OAAA;kBAAAkV,QAAA,gBACElV,OAAA;oBAAOiV,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAEhE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACRtV,OAAA;oBACEwQ,IAAI,EAAC,MAAM;oBACXhP,KAAK,EAAEwC,QAAQ,CAAC2B,iBAAiB,IAAI,EAAG;oBACxC+P,QAAQ,EAAG3G,CAAC,IAAK9K,WAAW,CAACyC,IAAI,KAAK;sBAAE,GAAGA,IAAI;sBAAEf,iBAAiB,EAAEoJ,CAAC,CAACC,MAAM,CAACxN;oBAAM,CAAC,CAAC,CAAE;oBACvFiU,WAAW,EAAC,wBAAwB;oBACpCR,SAAS,EAAC;kBAAa;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNtV,OAAA;kBAAAkV,QAAA,gBACElV,OAAA;oBAAOiV,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAEhE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACRtV,OAAA;oBACEwB,KAAK,EAAEwC,QAAQ,CAAC4B,iBAAiB,IAAI,QAAS;oBAC9C8P,QAAQ,EAAG3G,CAAC,IAAK9K,WAAW,CAACyC,IAAI,KAAK;sBAAE,GAAGA,IAAI;sBAAEd,iBAAiB,EAAEmJ,CAAC,CAACC,MAAM,CAACxN;oBAAM,CAAC,CAAC,CAAE;oBACvFyT,SAAS,EAAC,aAAa;oBAAAC,QAAA,gBAEvBlV,OAAA;sBAAQwB,KAAK,EAAC,QAAQ;sBAAA0T,QAAA,EAAC;oBAAM;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACtCtV,OAAA;sBAAQwB,KAAK,EAAC,UAAU;sBAAA0T,QAAA,EAAC;oBAAQ;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAC1CtV,OAAA;sBAAQwB,KAAK,EAAC,YAAY;sBAAA0T,QAAA,EAAC;oBAAU;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAC9CtV,OAAA;sBAAQwB,KAAK,EAAC,UAAU;sBAAA0T,QAAA,EAAC;oBAAQ;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAC1CtV,OAAA;sBAAQwB,KAAK,EAAC,cAAc;sBAAA0T,QAAA,EAAC;oBAAY;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eACNtV,OAAA;kBAAAkV,QAAA,gBACElV,OAAA;oBAAOiV,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,EAAC;kBAEhE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACRtV,OAAA;oBACEwQ,IAAI,EAAC,MAAM;oBACXhP,KAAK,EAAEwC,QAAQ,CAAC6B,cAAc,IAAI,EAAG;oBACrC6P,QAAQ,EAAG3G,CAAC,IAAK9K,WAAW,CAACyC,IAAI,KAAK;sBAAE,GAAGA,IAAI;sBAAEb,cAAc,EAAEkJ,CAAC,CAACC,MAAM,CAACxN;oBAAM,CAAC,CAAC,CAAE;oBACpFiU,WAAW,EAAC,0BAA0B;oBACtCR,SAAS,EAAC;kBAAa;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN,EAEAtR,QAAQ,CAACyB,aAAa,KAAK,QAAQ,iBAClCzF,OAAA;gBAAAkV,QAAA,gBACElV,OAAA;kBAAOiV,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAEhE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACRtV,OAAA;kBAAKiV,SAAS,EAAC,WAAW;kBAAAC,QAAA,GACvB,CAAClR,QAAQ,CAAC8B,WAAW,IAAI,EAAE,EAAEmE,GAAG,CAAC,CAACsO,IAAI,EAAErO,KAAK,kBAC5ClK,OAAA;oBAAiBiV,SAAS,EAAC,gBAAgB;oBAAAC,QAAA,gBACzClV,OAAA;sBACEwQ,IAAI,EAAC,QAAQ;sBACbkG,GAAG,EAAC,GAAG;sBACPsB,GAAG,EAAC,KAAK;sBACTxW,KAAK,EAAE+W,IAAI,CAACC,MAAM,IAAI,EAAG;sBACzB9C,QAAQ,EAAG3G,CAAC,IAAK;wBACf,MAAMsJ,OAAO,GAAG,CAAC,IAAIrU,QAAQ,CAAC8B,WAAW,IAAI,EAAE,CAAC,CAAC;wBACjDuS,OAAO,CAACnO,KAAK,CAAC,GAAG;0BAAE,GAAGmO,OAAO,CAACnO,KAAK,CAAC;0BAAEsO,MAAM,EAAE1P,QAAQ,CAACiG,CAAC,CAACC,MAAM,CAACxN,KAAK,CAAC,IAAI;wBAAG,CAAC;wBAC9EyC,WAAW,CAACyC,IAAI,KAAK;0BAAE,GAAGA,IAAI;0BAAEZ,WAAW,EAAEuS;wBAAQ,CAAC,CAAC,CAAC;sBAC1D,CAAE;sBACF5C,WAAW,EAAC,UAAU;sBACtBR,SAAS,EAAC;oBAAkB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC7B,CAAC,eACFtV,OAAA;sBACEwB,KAAK,EAAE+W,IAAI,CAACD,QAAQ,IAAI,EAAG;sBAC3B5C,QAAQ,EAAG3G,CAAC,IAAK;wBACf,MAAMsJ,OAAO,GAAG,CAAC,IAAIrU,QAAQ,CAAC8B,WAAW,IAAI,EAAE,CAAC,CAAC;wBACjDuS,OAAO,CAACnO,KAAK,CAAC,GAAG;0BAAE,GAAGmO,OAAO,CAACnO,KAAK,CAAC;0BAAEoO,QAAQ,EAAEvJ,CAAC,CAACC,MAAM,CAACxN;wBAAM,CAAC;wBAChEyC,WAAW,CAACyC,IAAI,KAAK;0BAAE,GAAGA,IAAI;0BAAEZ,WAAW,EAAEuS;wBAAQ,CAAC,CAAC,CAAC;sBAC1D,CAAE;sBACFpD,SAAS,EAAC,oBAAoB;sBAAAC,QAAA,gBAE9BlV,OAAA;wBAAQwB,KAAK,EAAC,EAAE;wBAAA0T,QAAA,EAAC;sBAAgB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,EACzC/R,QAAQ,CAACQ,KAAK,CAACkG,GAAG,CAAC,CAAC1B,IAAI,EAAE4P,SAAS,kBAClCnY,OAAA;wBAAwBwB,KAAK,EAAE2W,SAAS,GAAG,CAAE;wBAAAjD,QAAA,GAC1CiD,SAAS,GAAG,CAAC,EAAC,IAAE,EAAC5P,IAAI,CAACpI,IAAI;sBAAA,GADhBgY,SAAS;wBAAAhD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAEd,CACT,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACI,CAAC,eACTtV,OAAA;sBACEwQ,IAAI,EAAC,QAAQ;sBACb+E,OAAO,EAAEA,CAAA,KAAM;wBACb,MAAM8C,OAAO,GAAG,CAACrU,QAAQ,CAAC8B,WAAW,IAAI,EAAE,EAAEwB,MAAM,CAAC,CAACyG,CAAC,EAAEzF,CAAC,KAAKA,CAAC,KAAK4B,KAAK,CAAC;wBAC1EjG,WAAW,CAACyC,IAAI,KAAK;0BAAE,GAAGA,IAAI;0BAAEZ,WAAW,EAAEuS;wBAAQ,CAAC,CAAC,CAAC;sBAC1D,CAAE;sBACFpD,SAAS,EAAC,2CAA2C;sBAAAC,QAAA,EACtD;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA,GAvCDpL,KAAK;oBAAAiL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAwCV,CACN,CAAC,eACFtV,OAAA;oBACEwQ,IAAI,EAAC,QAAQ;oBACb+E,OAAO,EAAEA,CAAA,KAAM;sBACb,MAAM8C,OAAO,GAAG,CAAC,IAAIrU,QAAQ,CAAC8B,WAAW,IAAI,EAAE,CAAC,EAAE;wBAAE0S,MAAM,EAAE,EAAE;wBAAEF,QAAQ,EAAE;sBAAG,CAAC,CAAC;sBAC/ErU,WAAW,CAACyC,IAAI,KAAK;wBAAE,GAAGA,IAAI;wBAAEZ,WAAW,EAAEuS;sBAAQ,CAAC,CAAC,CAAC;oBAC1D,CAAE;oBACFpD,SAAS,EAAC,mFAAmF;oBAAAC,QAAA,EAC9F;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eACNtV,OAAA;kBAAGiV,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAC;gBAE1C;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CACN,eAGDtV,OAAA;cAAAkV,QAAA,gBACElV,OAAA;gBAAOiV,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRtV,OAAA;gBACEwB,KAAK,EAAEwC,QAAQ,CAACM,UAAU,IAAI,EAAG;gBACjCoR,QAAQ,EAAE3F,4BAA6B;gBACvCkF,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAEvBlV,OAAA;kBAAQwB,KAAK,EAAC,EAAE;kBAAA0T,QAAA,EAAC;gBAAuB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EAChD/R,QAAQ,CAACQ,KAAK,CAACkG,GAAG,CAAC,CAAC1B,IAAI,EAAE2B,KAAK,KAAK;kBACnC;kBACA,MAAMuO,YAAY,GAAGvO,KAAK,GAAG,CAAC;kBAC9B,oBACElK,OAAA;oBAAoBwB,KAAK,EAAEiX,YAAa;oBAAAvD,QAAA,GACrCuD,YAAY,EAAC,IAAE,EAAC,OAAOlQ,IAAI,CAACpI,IAAI,KAAK,QAAQ,GAAGoI,IAAI,CAACpI,IAAI,GAAG,cAAc;kBAAA,GADhE+J,KAAK;oBAAAiL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEV,CAAC;gBAEb,CAAC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC,eACTtV,OAAA;gBAAGiV,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAE1C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAGH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENtV,OAAA;UAAKiV,SAAS,EAAC,4CAA4C;UAAAC,QAAA,eACzDlV,OAAA;YAAKiV,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BlV,OAAA;cACEuV,OAAO,EAAEA,CAAA,KAAM;gBACbrP,gBAAgB,CAAC,KAAK,CAAC;gBACvBuH,aAAa,CAAC,CAAC;gBACfrH,mBAAmB,CAAC,CAAC,CAAC,CAAC;cACzB,CAAE;cACF6O,SAAS,EAAC,sBAAsB;cAAAC,QAAA,EACjC;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTtV,OAAA;cACEuV,OAAO,EAAEvK,aAAc;cACvBiK,SAAS,EAAC,oBAAoB;cAC9BO,QAAQ,EAAE,CAACxR,QAAQ,CAAC7D,IAAI,CAAC4H,IAAI,CAAC,CAAC,IAAI,CAAC/D,QAAQ,CAACE,OAAO,CAAC6D,IAAI,CAAC,CAAE;cAAAmN,QAAA,EAE3D/O,gBAAgB,IAAI,CAAC,GAAG,aAAa,GAAG;YAAU;cAAAgP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAGAnS,aAAa,IAAIE,YAAY,iBAC5BrD,OAAA;MAAKiV,SAAS,EAAC,gFAAgF;MAAAC,QAAA,eAC7FlV,OAAA;QAAKiV,SAAS,EAAC,6EAA6E;QAAAC,QAAA,eAC1FlV,OAAA;UAAKiV,SAAS,EAAC,KAAK;UAAAC,QAAA,gBAClBlV,OAAA;YAAKiV,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBACrDlV,OAAA;cAAIiV,SAAS,EAAC,qCAAqC;cAAAC,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrEtV,OAAA;cACEuV,OAAO,EAAEA,CAAA,KAAMnS,gBAAgB,CAAC,KAAK,CAAE;cACvC6R,SAAS,EAAC,oEAAoE;cAAAC,QAAA,eAE9ElV,OAAA,CAACnB,SAAS;gBAACoW,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENtV,OAAA;YAAKiV,SAAS,EAAC,WAAW;YAAAC,QAAA,gBAExBlV,OAAA;cAAAkV,QAAA,gBACElV,OAAA;gBAAIiV,SAAS,EAAC,gCAAgC;gBAAAC,QAAA,EAAE,OAAO7R,YAAY,CAAClD,IAAI,KAAK,QAAQ,GAAGkD,YAAY,CAAClD,IAAI,GAAG2B,MAAM,CAACuB,YAAY,CAAClD,IAAI,IAAI,cAAc;cAAC;gBAAAgV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,EAC5JjS,YAAY,CAACjD,WAAW,iBACvBJ,OAAA;gBAAGiV,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAE7R,YAAY,CAACjD;cAAW;gBAAA+U,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CACxE,eAEDtV,OAAA;gBAAKiV,SAAS,EAAC,gCAAgC;gBAAAC,QAAA,gBAC7ClV,OAAA;kBAAAkV,QAAA,gBACElV,OAAA;oBAAMiV,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC9CtV,OAAA;oBAAMiV,SAAS,EAAE,4EACf5R,YAAY,CAACwH,SAAS,GAAG,6BAA6B,GAAG,2BAA2B,EACnF;oBAAAqK,QAAA,EACA7R,YAAY,CAACwH,SAAS,GAAG,QAAQ,GAAG;kBAAU;oBAAAsK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACNtV,OAAA;kBAAAkV,QAAA,gBACElV,OAAA;oBAAMiV,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC9CtV,OAAA;oBAAMiV,SAAS,EAAC,oBAAoB;oBAAAC,QAAA,EAAE,OAAO7R,YAAY,CAAC6S,WAAW,KAAK,QAAQ,GAAG7S,YAAY,CAAC6S,WAAW,GAAGpU,MAAM,CAACuB,YAAY,CAAC6S,WAAW,IAAI,gBAAgB;kBAAC;oBAAAf,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzK,CAAC,eACNtV,OAAA;kBAAAkV,QAAA,gBACElV,OAAA;oBAAMiV,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC7CtV,OAAA;oBAAMiV,SAAS,EAAC,oBAAoB;oBAAAC,QAAA,EAAE7R,YAAY,CAACyS,UAAU,IAAI;kBAAC;oBAAAX,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvE,CAAC,eACNtV,OAAA;kBAAAkV,QAAA,gBACElV,OAAA;oBAAMiV,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAC;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACrDtV,OAAA;oBAAMiV,SAAS,EAAC,oBAAoB;oBAAAC,QAAA,EAAE7R,YAAY,CAAC0S,kBAAkB,IAAI;kBAAC;oBAAAZ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/E,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNtV,OAAA;cAAAkV,QAAA,gBACElV,OAAA;gBAAIiV,SAAS,EAAC,gCAAgC;gBAAAC,QAAA,EAAC;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpEtV,OAAA;gBAAKiV,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,EAClC,CAAC,OAAO7R,YAAY,CAACuH,gBAAgB,KAAK,QAAQ,GAAGvH,YAAY,CAACuH,gBAAgB,GAAG9I,MAAM,CAACuB,YAAY,CAACuH,gBAAgB,IAAI,EAAE,CAAC,EAAE0G,KAAK,CAAC,GAAG,CAAC,CAACrH,GAAG,CAAC,CAACgM,OAAO,EAAE/L,KAAK,kBAC/JlK,OAAA;kBAEEiV,SAAS,EAAC,8EAA8E;kBAAAC,QAAA,EAEvFe,OAAO,CAAClO,IAAI,CAAC;gBAAC,GAHVmC,KAAK;kBAAAiL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAIN,CACP;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNtV,OAAA;cAAKiV,SAAS,EAAC,uBAAuB;cAAAC,QAAA,gBACpClV,OAAA;gBAAAkV,QAAA,GAAG,WAAS,EAAC,IAAI5H,IAAI,CAACjK,YAAY,CAACqV,UAAU,CAAC,CAACC,cAAc,CAAC,CAAC;cAAA;gBAAAxD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpEtV,OAAA;gBAAAkV,QAAA,GAAG,WAAS,EAAC,IAAI5H,IAAI,CAACjK,YAAY,CAACuV,UAAU,CAAC,CAACD,cAAc,CAAC,CAAC;cAAA;gBAAAxD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,EACnEjS,YAAY,CAACwV,cAAc,iBAC1B7Y,OAAA;gBAAAkV,QAAA,GAAG,kBAAgB,EAAC,IAAI5H,IAAI,CAACjK,YAAY,CAACwV,cAAc,CAAC,CAACF,cAAc,CAAC,CAAC;cAAA;gBAAAxD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAC/E;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENtV,OAAA;YAAKiV,SAAS,EAAC,uBAAuB;YAAAC,QAAA,eACpClV,OAAA;cACEuV,OAAO,EAAEA,CAAA,KAAMnS,gBAAgB,CAAC,KAAK,CAAE;cACvC6R,SAAS,EAAC,eAAe;cAAAC,QAAA,EAC1B;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAED,eAAetU,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}