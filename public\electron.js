// Starting Lead Wave WhatsApp Desktop - removed console.log for production

const { app, BrowserWindow, ipcMain, dialog, shell } = require('electron');
const path = require('path');
const fs = require('fs');
const os = require('os');
const crypto = require('crypto');

// Add fetch polyfill for Node.js
const fetch = require('node-fetch');

// Development detection - moved to top for early use
const forceProduction = process.argv.includes('--prod');
const isDev = !forceProduction && !app.isPackaged;

// Security: Initialize anti-tampering protection (only in production)
let antiTamper, hardwareFingerprint, licenseValidator;

// Only load security modules in production to avoid development issues
if (!isDev) {
  try {
    antiTamper = require('../security/anti-tamper');
    hardwareFingerprint = require('../security/hardware-fingerprint');
    licenseValidator = require('../security/license-validator');
    // Security modules loaded for production build
  } catch (error) {
    // Security modules not available - continuing without them
  }
} else {
  // Development mode: Security modules disabled
}

// Import reseller configuration with proper path resolution
let resellerConfig;
try {
  // Try relative path first (development)
  resellerConfig = require('../config/reseller-config');
} catch (error) {
  try {
    // Try from build directory (production)
    resellerConfig = require('./config/reseller-config');
  } catch (error2) {
    try {
      // Try absolute path from app root
      resellerConfig = require(path.join(__dirname, 'config', 'reseller-config'));
    } catch (error3) {
      // Failed to load reseller config - using fallback
      // Fallback configuration for Lead Wave admin (no reseller)
      resellerConfig = {
        getResellerCode: () => null,
        isResellerBuild: () => false,
        getResellerInfo: () => ({ name: 'Lead Wave' }),
        getTrialRegistrationEndpoint: () => 'https://admin.getleadwave.in/api/v1/trial/register',
        prepareTrialRegistrationData: (userData) => ({
          name: userData.name,
          email: userData.email,
          phone: userData.phone,
          machine_id: userData.machine_id,
          reseller_code: null
        })
      };
    }
  }
}

const {
  getResellerCode,
  isResellerBuild,
  getResellerInfo,
  getTrialRegistrationEndpoint,
  prepareTrialRegistrationData
} = resellerConfig;

// Global variables
let mainWindow = null;
let appService = null;
let databaseService = null;
let backupService = null;
let isQuitting = false;
let isShuttingDown = false;

// Enhanced logging system
const getAppDataPath = () => path.join(os.homedir(), 'Lead Wave');
const logDir = path.join(getAppDataPath(), 'logs');
const logFile = path.join(logDir, 'leadwave-debug.log');

// Ensure log directory exists
try {
  if (!fs.existsSync(logDir)) {
    fs.mkdirSync(logDir, { recursive: true });
  }
} catch (err) {
  // Only log directory creation errors in development mode
  if (isDev) {
    console.error('Failed to create log directory:', err);
  }
}

function logToFile(message) {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] ${message}\n`;

  // Only log to console in development mode
  if (isDev) {
    console.log(message);
  }

  try {
    fs.appendFileSync(logFile, logMessage);
  } catch (err) {
    // Only log file write errors in development mode
    if (isDev) {
      console.error('Failed to write to log file:', err);
    }
  }
}

logToFile('🚀 Starting Lead Wave WhatsApp Desktop...');
logToFile(`Process PID: ${process.pid}`);
logToFile(`Process execPath: ${process.execPath}`);
logToFile(`Process argv: ${JSON.stringify(process.argv)}`);
logToFile(`Log file location: ${logFile}`);

// Initialize backup service
const initializeBackupService = () => {
  if (!backupService && appService) {
    try {
      let BackupService;

      // Try different paths for backup service
      try {
        // Try relative path first (development)
        BackupService = require('../services/backup.service');
      } catch (error) {
        try {
          // Try from current directory (production)
          BackupService = require('./services/backup.service');
        } catch (error2) {
          try {
            // Try absolute path from app root
            BackupService = require(path.join(__dirname, 'services', 'backup.service'));
          } catch (error3) {
            throw new Error(`Failed to load backup service from any path: ${error3.message}`);
          }
        }
      }

      const databaseService = appService.getDatabaseService();
      backupService = new BackupService(databaseService);
      logToFile('✅ Backup service initialized');
    } catch (error) {
      logToFile(`❌ Failed to initialize backup service: ${error.message}`);
    }
  }
  return backupService;
};

// Register backup IPC handlers immediately
logToFile('🔄 Registering backup IPC handlers...');

// Create backup
ipcMain.handle('backup:create', async (event, options) => {
  try {
    logToFile('🔄 Creating backup...');
    if (!appService) {
      throw new Error('App service not available');
    }
    const service = initializeBackupService();
    if (!service) {
      throw new Error('Backup service not available');
    }
    const result = await service.createBackup(options);
    logToFile(`✅ Backup creation result: ${JSON.stringify(result)}`);
    return result;
  } catch (error) {
    logToFile(`❌ Backup creation error: ${error.message}`);
    return { success: false, error: error.message };
  }
});

// Restore from backup
ipcMain.handle('backup:restore', async (event, filePath, options) => {
  try {
    logToFile(`🔄 Restoring backup from: ${filePath}`);
    if (!appService) {
      throw new Error('App service not available');
    }
    const service = initializeBackupService();
    if (!service) {
      throw new Error('Backup service not available');
    }
    return await service.restoreFromBackup(filePath, options);
  } catch (error) {
    logToFile(`❌ Backup restore error: ${error.message}`);
    return { success: false, error: error.message };
  }
});

// Get backup history
ipcMain.handle('backup:get-history', async (event) => {
  try {
    logToFile('🔄 Getting backup history...');
    if (!appService) {
      throw new Error('App service not available');
    }
    const service = initializeBackupService();
    if (!service) {
      throw new Error('Backup service not available');
    }
    const history = await service.getBackupHistory();
    return { success: true, data: history };
  } catch (error) {
    logToFile(`❌ Get backup history error: ${error.message}`);
    return { success: false, error: error.message };
  }
});

// File dialog for backup file selection
ipcMain.handle('backup:select-file', async (event) => {
  try {
    const { dialog } = require('electron');
    const result = await dialog.showOpenDialog(mainWindow, {
      title: 'Select Backup File',
      filters: [
        { name: 'Backup Files', extensions: ['zip'] },
        { name: 'All Files', extensions: ['*'] }
      ],
      properties: ['openFile']
    });

    if (result.canceled) {
      return { success: false, canceled: true };
    }

    return { success: true, filePath: result.filePaths[0] };
  } catch (error) {
    logToFile(`❌ File selection error: ${error.message}`);
    return { success: false, error: error.message };
  }
});

// File dialog for backup save location
ipcMain.handle('backup:select-save-location', async (event, defaultName) => {
  try {
    const { dialog } = require('electron');
    const result = await dialog.showSaveDialog(mainWindow, {
      title: 'Save Backup As',
      defaultPath: defaultName || 'leadwave-backup.zip',
      filters: [
        { name: 'Backup Files', extensions: ['zip'] },
        { name: 'All Files', extensions: ['*'] }
      ]
    });

    if (result.canceled) {
      return { success: false, canceled: true };
    }

    return { success: true, filePath: result.filePath };
  } catch (error) {
    logToFile(`❌ Save location selection error: ${error.message}`);
    return { success: false, error: error.message };
  }
});

// Validate backup file
ipcMain.handle('backup:validate-file', async (event, filePath) => {
  try {
    if (!appService) {
      throw new Error('App service not available');
    }
    const service = initializeBackupService();
    if (!service) {
      throw new Error('Backup service not available');
    }
    return await service.validateBackupFile(filePath);
  } catch (error) {
    logToFile(`❌ Backup validation error: ${error.message}`);
    return { valid: false, error: error.message };
  }
});

// App restart handler
ipcMain.handle('app:restart', async (event) => {
  try {
    logToFile('🔄 Application restart requested');
    app.relaunch();
    app.exit(0);
    return { success: true };
  } catch (error) {
    logToFile(`❌ App restart error: ${error.message}`);
    return { success: false, error: error.message };
  }
});

logToFile('✅ Backup IPC handlers registered');

// Enhanced single instance management
const MUTEX_NAME = 'LeadWave WhatsAppDesktopMutex';
let singleInstanceLock = null;

// Development detection (already defined at top of file)

logToFile(`🔍 __dirname: ${__dirname}`);
logToFile(`🔍 __filename: ${__filename}`);
logToFile(`🔍 process.env.NODE_ENV: ${process.env.NODE_ENV}`);
logToFile(`🔍 process.defaultApp: ${process.defaultApp}`);
logToFile(`🔍 app.isPackaged: ${app.isPackaged}`);
logToFile(`🔍 process.execPath: ${process.execPath}`);
logToFile(`🔍 Development mode: ${isDev}`);

// Try to acquire single instance lock
try {
  singleInstanceLock = app.requestSingleInstanceLock();
  logToFile(`🔒 Single instance lock acquired: ${singleInstanceLock}`);
} catch (error) {
  logToFile(`❌ Failed to acquire single instance lock: ${error.message}`);
  singleInstanceLock = false;
}

if (!singleInstanceLock) {
  logToFile('❌ Another instance is already running. Exiting gracefully...');
  app.quit();
  process.exit(0);
}

// Handle second instance attempts
app.on('second-instance', (event, commandLine, workingDirectory) => {
  logToFile('🔄 Second instance detected, focusing existing window...');
  if (mainWindow && !mainWindow.isDestroyed()) {
    if (mainWindow.isMinimized()) {
      mainWindow.restore();
    }
    mainWindow.focus();
    mainWindow.show();
  }
});

// Fix for Baileys crypto compatibility
if (!globalThis.crypto) {
  const { webcrypto } = require('crypto');
  globalThis.crypto = webcrypto;
}

// Graceful shutdown function
async function gracefulShutdown() {
  if (isShuttingDown) {
    logToFile('🔄 Shutdown already in progress...');
    return;
  }
  
  isShuttingDown = true;
  logToFile('🔄 Starting graceful shutdown...');
  
  try {
    // Close main window first
    if (mainWindow && !mainWindow.isDestroyed()) {
      logToFile('🪟 Closing main window...');
      mainWindow.removeAllListeners();
      mainWindow.close();
      mainWindow = null;
    }
    
    // Shutdown app service
    if (appService) {
      logToFile('🔄 Shutting down app service...');
      await appService.shutdown();
      logToFile('✅ App service shutdown complete');
    }
    
    // Release single instance lock
    if (singleInstanceLock) {
      logToFile('🔓 Releasing single instance lock...');
      app.releaseSingleInstanceLock();
    }
    
    logToFile('✅ Graceful shutdown complete');
    
    // Force exit after a delay to ensure cleanup
    setTimeout(() => {
      logToFile('🔄 Force exiting process...');
      process.exit(0);
    }, 1000);
    
  } catch (error) {
    logToFile(`❌ Error during shutdown: ${error.message}`);
    process.exit(1);
  }
}

// Enhanced error handling - Log errors but don't exit unless critical
process.on('uncaughtException', (error) => {
  logToFile(`❌ Uncaught Exception: ${error.message}`);
  logToFile(`Stack: ${error.stack}`);

  // Only exit for critical system errors, not application errors
  if (error.code === 'EADDRINUSE' || error.code === 'EACCES' || error.message.includes('Cannot find module')) {
    logToFile('💥 Critical system error detected, shutting down...');
    gracefulShutdown();
  } else {
    logToFile('⚠️ Non-critical error, continuing operation...');
  }
});

process.on('unhandledRejection', (reason, promise) => {
  logToFile(`❌ Unhandled Rejection: ${reason}`);
  logToFile(`Promise: ${promise}`);

  // Log but don't exit for unhandled rejections - they're often non-critical
  logToFile('⚠️ Unhandled rejection logged, continuing operation...');
});

// Handle app termination signals
process.on('SIGTERM', () => {
  logToFile('🔄 Received SIGTERM');
  gracefulShutdown();
});

process.on('SIGINT', () => {
  logToFile('🔄 Received SIGINT');
  gracefulShutdown();
});

// Handle Windows-specific signals
if (process.platform === 'win32') {
  process.on('SIGBREAK', () => {
    logToFile('🔄 Received SIGBREAK');
    gracefulShutdown();
  });
}

// Prevent app from quitting when all windows are closed (except on macOS)
app.on('window-all-closed', () => {
  logToFile('🔄 All windows closed');
  if (process.platform !== 'darwin') {
    isQuitting = true;
    gracefulShutdown();
  }
});

// Handle app before-quit event
app.on('before-quit', async (event) => {
  logToFile('🔄 Before quit event triggered');
  if (!isQuitting) {
    event.preventDefault();

    // Show confirmation dialog if main window exists
    if (mainWindow && !mainWindow.isDestroyed()) {
      const { dialog } = require('electron');
      const choice = await dialog.showMessageBox(mainWindow, {
        type: 'question',
        buttons: ['Yes', 'No'],
        defaultId: 1,
        title: 'Confirm Exit',
        message: 'Are you sure you want to close Lead Wave?',
        detail: 'This will stop all WhatsApp sessions and close the application.',
        icon: null
      });

      if (choice.response === 0) { // User clicked "Yes"
        logToFile('🔄 User confirmed application exit via before-quit');
        isQuitting = true;
        gracefulShutdown();
      } else {
        logToFile('🔄 User cancelled application exit via before-quit');
      }
    } else {
      // No window to show dialog, proceed with quit
      isQuitting = true;
      gracefulShutdown();
    }
  }
});

// Handle app will-quit event
app.on('will-quit', (event) => {
  logToFile('🔄 Will quit event triggered');
  if (!isShuttingDown) {
    event.preventDefault();
    gracefulShutdown();
  }
});

// Helper function to set up window event handlers
function setupWindowEventHandlers(window) {
  // Handle window close event
  window.on('close', async (event) => {
    logToFile('🔄 Window close event triggered');
    if (!isQuitting) {
      event.preventDefault();

      try {
        // Send request to renderer and wait for response
        const response = await new Promise((resolve, reject) => {
          const timeout = setTimeout(() => {
            reject(new Error('Close confirmation timeout'));
          }, 10000); // 10 second timeout

          // Set up one-time listener for response
          const responseHandler = (responseEvent, confirmed) => {
            clearTimeout(timeout);
            ipcMain.removeListener('app:close-confirmation-response', responseHandler);
            resolve(confirmed);
          };

          ipcMain.once('app:close-confirmation-response', responseHandler);

          // Send request to renderer
          window.webContents.send('app:show-close-confirmation', {
            title: 'Confirm Exit',
            message: 'Are you sure you want to close Lead Wave?\n\nThis will stop all WhatsApp sessions and close the application.'
          });
        });

        if (response) {
          logToFile('🔄 User confirmed application exit via in-app dialog');
          isQuitting = true;
          gracefulShutdown();
        } else {
          logToFile('🔄 User cancelled application exit via in-app dialog');
        }
      } catch (error) {
        logToFile(`❌ Error showing close confirmation: ${error.message}`);
        // Fallback to immediate close if there's an error
        isQuitting = true;
        gracefulShutdown();
      }
    }
  });

  // Handle window closed event
  window.on('closed', () => {
    logToFile('🔄 Window closed event triggered');
    mainWindow = null;
    if (!isQuitting) {
      isQuitting = true;
      gracefulShutdown();
    }
  });
}

// Create main window function
function createWindow() {
  logToFile('🪟 Creating main window...');
  
  try {
    // Load window frame preference from database
    let showTitleBar = true; // default value
    try {
      if (databaseService && databaseService.db) {
        const framePreference = databaseService.db.prepare('SELECT value FROM app_settings WHERE key = ?').get('window_show_title_bar');
        if (framePreference) {
          showTitleBar = framePreference.value === 'true';
          logToFile(`📋 Loaded window frame preference: ${showTitleBar}`);
        } else {
          logToFile('📋 No window frame preference found, using default: true');
        }
      } else {
        logToFile('⚠️ Database not available during window creation, using default title bar setting');
      }
    } catch (prefError) {
      logToFile(`⚠️ Error loading window frame preference: ${prefError.message}, using default`);
    }

    mainWindow = new BrowserWindow({
      width: 1400,
      height: 900,
      minWidth: 1200,
      minHeight: 700,
      title: 'Lead Wave',
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        enableRemoteModule: false,
        devTools: isDev, // Only allow DevTools in development mode
        preload: path.join(__dirname, 'preload.js')
      },
      show: true,
      titleBarStyle: 'default',
      frame: true,
      autoHideMenuBar: !showTitleBar // Apply saved preference
    });

    logToFile('✅ Main window created successfully');

    // Apply the saved window frame preference immediately
    if (!showTitleBar) {
      mainWindow.setMenuBarVisibility(false);
      mainWindow.setAutoHideMenuBar(true);
      logToFile('🪟 Applied saved preference: title bar hidden');
    } else {
      mainWindow.setMenuBarVisibility(true);
      mainWindow.setAutoHideMenuBar(false);
      logToFile('🪟 Applied saved preference: title bar visible');
    }

    // Also apply preference after window is ready and database is fully initialized
    mainWindow.once('ready-to-show', async () => {
      logToFile('🪟 Window ready to show, re-applying frame preference...');

      try {
        // Wait longer for database to be fully ready and services initialized
        await new Promise(resolve => setTimeout(resolve, 500));

        // Re-check the database preference now that everything is initialized
        if (databaseService && databaseService.db) {
          logToFile('🔍 Database service available, checking frame preference...');

          try {
            const framePreference = databaseService.db.prepare('SELECT value FROM app_settings WHERE key = ?').get('window_show_title_bar');
            logToFile(`🔍 Frame preference query result: ${JSON.stringify(framePreference)}`);

            if (framePreference) {
              const shouldShowTitleBar = framePreference.value === 'true';
              logToFile(`🔄 Re-applying window frame preference: ${shouldShowTitleBar}`);

              if (!shouldShowTitleBar) {
                mainWindow.setMenuBarVisibility(false);
                mainWindow.setAutoHideMenuBar(true);
                logToFile('🪟 Final application: title bar hidden');
              } else {
                mainWindow.setMenuBarVisibility(true);
                mainWindow.setAutoHideMenuBar(false);
                logToFile('🪟 Final application: title bar visible');
              }
            } else {
              logToFile('🔍 No frame preference found in database, using default (title bar visible)');
              mainWindow.setMenuBarVisibility(true);
              mainWindow.setAutoHideMenuBar(false);
            }
          } catch (dbError) {
            logToFile(`❌ Database query error: ${dbError.message}`);
            // Fallback to default
            mainWindow.setMenuBarVisibility(true);
            mainWindow.setAutoHideMenuBar(false);
          }
        } else {
          logToFile('⚠️ Database service not available, using default frame settings');
          // Fallback to default
          mainWindow.setMenuBarVisibility(true);
          mainWindow.setAutoHideMenuBar(false);
        }
      } catch (error) {
        logToFile(`⚠️ Error re-applying frame preference: ${error.message}`);
        // Fallback to default
        mainWindow.setMenuBarVisibility(true);
        mainWindow.setAutoHideMenuBar(false);
      }

      mainWindow.show();
      mainWindow.focus();
    });

    // Block browser notifications - we use in-app notifications instead
    mainWindow.webContents.session.setPermissionRequestHandler((webContents, permission, callback) => {
      if (permission === 'notifications') {
        logToFile('🚫 Blocking browser notification permission request - using in-app notifications instead');
        callback(false); // Deny browser notifications
      } else {
        callback(true); // Allow other permissions
      }
    });

    // Also block notifications at the session level
    mainWindow.webContents.session.setPermissionCheckHandler((webContents, permission, requestingOrigin, details) => {
      if (permission === 'notifications') {
        logToFile('🚫 Blocking notification permission check - using in-app notifications instead');
        return false;
      }
      return true;
    });

    logToFile('🧹 Notification permissions will be blocked by permission handlers');

    // Override browser Notification API immediately when webContents is created
    mainWindow.webContents.executeJavaScript(`
      // Override the Notification constructor to prevent system notifications
      window.Notification = class {
        constructor() {
          // Browser notification blocked - using in-app notifications instead
          return {};
        }
        static get permission() { return 'denied'; }
        static requestPermission() {
          // Notification.requestPermission() blocked - using in-app notifications instead
          return Promise.resolve('denied');
        }
      };

      // Also override any existing Notification references
      if (window.webkitNotifications) {
        window.webkitNotifications = undefined;
      }

      // Browser Notification API overridden to prevent system notifications
    `).catch(err => {
      logToFile(`⚠️ Failed to override Notification API initially: ${err.message}`);
    });

    // Override browser Notification API after page loads as well (double protection)
    mainWindow.webContents.on('dom-ready', () => {
      mainWindow.webContents.executeJavaScript(`
        // Override the Notification constructor to prevent system notifications
        window.Notification = class {
          constructor() {
            // Browser notification blocked - using in-app notifications instead
            return {};
          }
          static get permission() { return 'denied'; }
          static requestPermission() {
            // Notification.requestPermission() blocked - using in-app notifications instead
            return Promise.resolve('denied');
          }
        };

        // Also override any existing Notification references
        if (window.webkitNotifications) {
          window.webkitNotifications = undefined;
        }

        // Browser Notification API overridden on DOM ready to prevent system notifications
      `).catch(err => {
        logToFile(`⚠️ Failed to override Notification API on DOM ready: ${err.message}`);
      });
    });

    // Additional protection - override on navigation
    mainWindow.webContents.on('did-navigate', () => {
      mainWindow.webContents.executeJavaScript(`
        // Override the Notification constructor to prevent system notifications
        window.Notification = class {
          constructor() {
            // Browser notification blocked - using in-app notifications instead
            return {};
          }
          static get permission() { return 'denied'; }
          static requestPermission() {
            // Notification.requestPermission() blocked - using in-app notifications instead
            return Promise.resolve('denied');
          }
        };

        // Also override any existing Notification references
        if (window.webkitNotifications) {
          window.webkitNotifications = undefined;
        }

        // Browser Notification API overridden on navigation to prevent system notifications
      `).catch(err => {
        logToFile(`⚠️ Failed to override Notification API on navigation: ${err.message}`);
      });
    });

    // Show window when ready
    mainWindow.once('ready-to-show', () => {
      logToFile('🪟 Window ready to show');
      mainWindow.show();
      mainWindow.focus();
    });

    // Set up event handlers
    setupWindowEventHandlers(mainWindow);

    // Load the app
    if (isDev) {
      logToFile('🔄 Loading development server...');
      mainWindow.loadURL('http://localhost:3000').catch(err => {
        logToFile(`❌ Failed to load dev server: ${err.message}`);
        const errorHtml = `
          <html>
            <head><title>Development Server Not Running</title></head>
            <body style="font-family: Arial, sans-serif; padding: 40px; text-align: center;">
              <h1 style="color: #e74c3c;">Development Server Not Running</h1>
              <p>Please run "npm run dev:react" first.</p>
            </body>
          </html>
        `;
        mainWindow.loadURL(`data:text/html,${encodeURIComponent(errorHtml)}`);
      });
    } else {
      // Production mode - load built files
      const indexPath = path.join(__dirname, 'index.html');
      logToFile(`🔄 Loading production build from: ${indexPath}`);
      
      if (fs.existsSync(indexPath)) {
        mainWindow.loadFile(indexPath);
      } else {
        logToFile(`❌ Index file not found: ${indexPath}`);
        const errorHtml = `
          <html>
            <head><title>Build Not Found</title></head>
            <body style="font-family: Arial, sans-serif; padding: 40px; text-align: center;">
              <h1 style="color: #e74c3c;">Build Not Found</h1>
              <p>Application files could not be located.</p>
              <p><strong>Path:</strong> ${indexPath}</p>
            </body>
          </html>
        `;
        mainWindow.loadURL(`data:text/html,${encodeURIComponent(errorHtml)}`);
      }
    }

    // Show window when ready
    mainWindow.once('ready-to-show', () => {
      logToFile('✅ Window ready to show');
      mainWindow.show();

      if (isDev) {
        mainWindow.webContents.openDevTools();
      }
    });

    // Security: Prevent DevTools from being opened in production
    if (!isDev) {
      mainWindow.webContents.on('before-input-event', (event, input) => {
        // Block Ctrl+Shift+I, Ctrl+Shift+J, F12
        if (
          (input.control && input.shift && (input.key.toLowerCase() === 'i' || input.key.toLowerCase() === 'j')) ||
          input.key.toLowerCase() === 'f12'
        ) {
          event.preventDefault();
        }
      });

      // Block context menu (right-click) in production
      mainWindow.webContents.on('context-menu', (event) => {
        event.preventDefault();
      });

      // Prevent DevTools from being opened programmatically
      mainWindow.webContents.on('devtools-opened', () => {
        mainWindow.webContents.closeDevTools();
      });
    }

    // Handle external links
    mainWindow.webContents.setWindowOpenHandler(({ url }) => {
      shell.openExternal(url);
      return { action: 'deny' };
    });

  } catch (error) {
    logToFile(`❌ Failed to create window: ${error.message}`);
    gracefulShutdown();
  }
}

// Load app service function with timeout
function loadAppService() {
  return new Promise((resolve) => {
    // Set a timeout to prevent hanging
    const timeout = setTimeout(() => {
      logToFile('❌ AppService loading timed out after 10 seconds');
      resolve(false);
    }, 10000);

    try {
      logToFile('🔄 Starting to load app service...');
      const possiblePaths = [
        './services/app.service',
        '../services/app.service',
        path.join(__dirname, 'services/app.service'),
        path.join(__dirname, '../services/app.service'),
      ];

      let servicePath = null;
      for (const testPath of possiblePaths) {
        try {
          require.resolve(testPath);
          servicePath = testPath;
          logToFile(`🔍 Found service at: ${testPath}`);
          break;
        } catch (e) {
          logToFile(`🔍 Service not found at: ${testPath}`);
        }
      }

      if (servicePath) {
        logToFile(`🔄 Loading AppService from: ${servicePath}`);

        // Try to load with timeout - use async IIFE
        setTimeout(() => {
          (async () => {
            try {
              const AppService = require(servicePath);
              logToFile('🔄 Creating AppService instance...');
              appService = new AppService();

              logToFile('🔄 Initializing AppService...');
              await appService.initialize();

              // Set up global services for cross-service communication
              global.services = {
                whatsapp: appService.getWhatsAppService()
              };



              logToFile(`✅ App service loaded and initialized from: ${servicePath}`);
              clearTimeout(timeout);
              resolve(true);
            } catch (err) {
              logToFile(`❌ Failed to create AppService instance: ${err.message}`);
              logToFile(`❌ Error stack: ${err.stack}`);
              clearTimeout(timeout);
              resolve(false);
            }
          })();
        }, 100);
      } else {
        logToFile('❌ App service not found in any expected location');
        clearTimeout(timeout);
        resolve(false);
      }
    } catch (err) {
      logToFile(`❌ Failed to load app service: ${err.message}`);
      logToFile(`❌ Error stack: ${err.stack}`);
      clearTimeout(timeout);
      resolve(false);
    }
  });
}

// Setup event forwarding after app initialization
function setupEventForwarding() {
  if (!appService) return;

  const eventService = appService.getEventService();
  if (eventService && mainWindow) {
    // Forward WhatsApp events to renderer process
    eventService.on('qr_code_generated', (data) => {
      console.log('🔄 Main process forwarding qr_code_generated event:', data.sessionId);
      if (mainWindow) {
        mainWindow.webContents.send('whatsapp:qr-code', data);
        console.log('📤 Sent whatsapp:qr-code event to renderer');
      } else {
        console.log('❌ Main window not available for QR code forwarding');
      }
    });

    eventService.on('session_connected', (data) => {
      if (mainWindow) {
        mainWindow.webContents.send('whatsapp:session-connected', data);
      }
    });

    eventService.on('session_disconnected', (data) => {
      if (mainWindow) {
        mainWindow.webContents.send('whatsapp:session-disconnected', data);
      }
    });

    eventService.on('message_received', (data) => {
      if (mainWindow) {
        mainWindow.webContents.send('whatsapp:message-received', data);
      }
    });

    logToFile('✅ Event forwarding setup complete');
  }
}

// App ready handler
app.whenReady().then(async () => {
  try {
    logToFile('🚀 Electron app is ready');

    // Try to load and initialize app service before creating window
    logToFile('🔄 Attempting to load app service...');
    const serviceLoaded = await loadAppService();

    if (serviceLoaded && appService) {
      try {
        logToFile('🔄 Initializing app service before window creation...');
        await appService.initialize();
        logToFile('✅ App service initialized');

        // Get database service reference for window creation
        databaseService = appService.getDatabaseService();
        if (databaseService) {
          logToFile('✅ Database service available for window creation');
        } else {
          logToFile('❌ Database service is null/undefined');
        }
      } catch (serviceError) {
        logToFile(`❌ App service initialization failed: ${serviceError.message}`);
        logToFile(`❌ Service error stack: ${serviceError.stack}`);
        // Continue without app service
      }
    } else {
      logToFile('⚠️ Continuing without app service');
    }

    // Create main window (now with database service available)
    logToFile('🪟 Creating main window...');
    createWindow();

    // Setup event forwarding after window is created
    if (appService) {
      setupEventForwarding();
    }

  } catch (error) {
    logToFile(`❌ Failed to initialize: ${error.message}`);
    logToFile(`❌ Error stack: ${error.stack}`);
    // Still try to create window
    try {
      logToFile('🪟 Creating window despite error...');
      createWindow();
    } catch (windowError) {
      logToFile(`❌ Failed to create window: ${windowError.message}`);
      gracefulShutdown();
    }
  }
});

// Handle activate event (macOS)
app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    logToFile('🔄 App activated, creating new window...');
    createWindow();
  }
});

// IPC handler for close confirmation response
ipcMain.on('app:close-confirmation-response', (event, confirmed) => {
  // This is handled by the promise in the close event handler
  logToFile(`🔄 Close confirmation response received: ${confirmed}`);
});

// Enhanced Machine ID Generation with Hardware Fingerprinting
function generateMachineId() {
  try {
    // In production, use enhanced hardware fingerprinting for better security
    if (!isDev && hardwareFingerprint) {
      return hardwareFingerprint.getMachineId();
    }

    // In development or fallback, use the original method
    const hostname = os.hostname();
    const platform = os.platform();
    const arch = os.arch();
    const cpus = os.cpus();
    const networkInterfaces = os.networkInterfaces();

    // Create a unique string from system info
    let machineString = `${hostname}-${platform}-${arch}`;

    // Add CPU info
    if (cpus && cpus.length > 0) {
      machineString += `-${cpus[0].model}-${cpus.length}`;
    }

    // Add MAC addresses from network interfaces
    const macAddresses = [];
    for (const interfaceName in networkInterfaces) {
      const interfaces = networkInterfaces[interfaceName];
      for (const iface of interfaces) {
        if (iface.mac && iface.mac !== '00:00:00:00:00:00') {
          macAddresses.push(iface.mac);
        }
      }
    }

    if (macAddresses.length > 0) {
      machineString += `-${macAddresses.sort().join('-')}`;
    }

    // Generate SHA-256 hash
    const hash = crypto.createHash('sha256').update(machineString).digest('hex');
    return hash.substring(0, 32).toUpperCase();
  } catch (error) {
    logToFile(`❌ Error generating machine ID: ${error.message}`);
    // Fallback to a random ID if system info fails
    return crypto.randomBytes(16).toString('hex').toUpperCase();
  }
}

// License Management IPC Handlers
ipcMain.handle('license:get-machine-id', () => {
  return generateMachineId();
});

// Reseller Configuration IPC Handlers
ipcMain.handle('reseller:get-config', () => {
  return {
    isResellerBuild: isResellerBuild(),
    resellerCode: getResellerCode(),
    resellerInfo: getResellerInfo()
  };
});

ipcMain.handle('license:activate', async (event, licenseKey) => {
  try {
    const machineId = generateMachineId();
    logToFile(`🔑 Attempting license activation for: ${licenseKey} with machine ID: ${machineId}`);

    // First check the license status before attempting activation
    logToFile('🔍 Checking license status before activation...');
    try {
      const statusResponse = await fetch('https://admin.getleadwave.in/api/v1/license/status', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: JSON.stringify({
          license_key: licenseKey
        })
      });

      const statusResult = await statusResponse.json();
      logToFile(`🔍 Status check result: ${JSON.stringify(statusResult)}`);

      if (statusResult.success && statusResult.data) {
        const status = statusResult.data.status;
        logToFile(`🔍 License status: ${status}`);

        // Check if license is suspended, revoked, or expired
        if (status === 'suspended') {
          logToFile('🚫 License is suspended - blocking activation');
          return {
            success: false,
            message: 'This license has been suspended. Please contact your administrator.',
            error_code: 'SUSPENDED'
          };
        } else if (status === 'revoked') {
          logToFile('🚫 License is revoked - blocking activation');
          return {
            success: false,
            message: 'This license has been revoked. Please contact your administrator.',
            error_code: 'REVOKED'
          };
        } else if (status === 'expired') {
          logToFile('🚫 License is expired - blocking activation');
          return {
            success: false,
            message: 'This license has expired. Please contact your administrator.',
            error_code: 'EXPIRED'
          };
        } else if (status !== 'pending' && status !== 'trial' && status !== 'active') {
          logToFile(`🚫 License status ${status} - blocking activation`);
          return {
            success: false,
            message: `This license is ${status}. Please contact your administrator.`,
            error_code: 'LICENSE_INACTIVE'
          };
        }
      }
    } catch (statusError) {
      logToFile(`⚠️ Status check failed: ${statusError.message}`);
      // Continue with activation attempt if status check fails
    }

    // Get the appropriate endpoint
    const endpoint = 'https://admin.getleadwave.in/api/v1/license/activate';

    logToFile(`📤 Sending activation request to: ${endpoint}`);
    logToFile(`📤 Request data: ${JSON.stringify({ license_key: licenseKey, machine_id: machineId })}`);

    const response = await fetch(endpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: JSON.stringify({
        license_key: licenseKey,
        machine_id: machineId,
        app_version: app.getVersion()
      })
    });

    logToFile(`📥 Response status: ${response.status} ${response.statusText}`);

    const result = await response.json();
    logToFile(`📥 Response data: ${JSON.stringify(result)}`);

    if (result.success) {
      // Store license info locally
      const licenseData = {
        license_key: result.data.license_key,
        customer_name: result.data.customer_name,
        expires_at: result.data.expires_at,
        machine_id: machineId,
        activated_at: new Date().toISOString(),
        status: result.data.status,
        plan_name: result.data.plan_name,
        duration_days: result.data.duration_days,
        features: result.data.features || [],
        isTrial: result.data.isTrial || false,
        isUpgraded: result.data.isUpgraded || false,
        days_remaining: result.data.days_remaining
      };

      // Save to local storage file
      const appDataPath = getAppDataPath();
      if (!fs.existsSync(appDataPath)) {
        fs.mkdirSync(appDataPath, { recursive: true });
      }

      const licenseFile = path.join(appDataPath, 'license.json');
      fs.writeFileSync(licenseFile, JSON.stringify(licenseData, null, 2));

      logToFile(`✅ License activated successfully: ${result.data.license_key}`);
    }

    return result;
  } catch (error) {
    logToFile(`❌ Error activating license: ${error.message}`);
    logToFile(`❌ Error stack: ${error.stack}`);
    return {
      success: false,
      message: 'Failed to connect to license server. Please check your internet connection.',
      error: error.message
    };
  }
});

// License upgrade handler
ipcMain.handle('license:upgrade', async (event, newLicenseKey) => {
  try {
    logToFile(`🔄 Starting license upgrade with new key: ${newLicenseKey}`);

    const machineId = generateMachineId();
    logToFile(`🔄 Generated machine ID: ${machineId}`);

    // Get current license key from stored license
    const appDataPath = getAppDataPath();
    const licenseFile = path.join(appDataPath, 'license.json');

    if (!fs.existsSync(licenseFile)) {
      logToFile(`❌ No existing license found for upgrade`);
      return {
        success: false,
        message: 'No existing license found. Please activate a license first.',
        error_code: 'NO_EXISTING_LICENSE'
      };
    }

    const currentLicenseData = JSON.parse(fs.readFileSync(licenseFile, 'utf8'));
    const currentLicenseKey = currentLicenseData.license_key;

    logToFile(`🔄 Current license key: ${currentLicenseKey}`);

    // Call the upgrade endpoint
    const endpoint = 'https://admin.getleadwave.in/api/v1/license/upgrade';

    logToFile(`📤 Sending upgrade request to: ${endpoint}`);
    logToFile(`📤 Request data: ${JSON.stringify({
      current_license_key: currentLicenseKey,
      new_license_key: newLicenseKey,
      machine_id: machineId
    })}`);

    const response = await fetch(endpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: JSON.stringify({
        current_license_key: currentLicenseKey,
        new_license_key: newLicenseKey,
        machine_id: machineId,
        app_version: app.getVersion()
      })
    });

    logToFile(`📥 Response status: ${response.status} ${response.statusText}`);

    const result = await response.json();
    logToFile(`📥 Response data: ${JSON.stringify(result)}`);

    if (result.success && result.data) {
      // Save updated license data to file
      const licenseData = {
        license_key: result.data.license_key,
        customer_name: result.data.customer_name,
        expires_at: result.data.expires_at,
        machine_id: machineId,
        activated_at: new Date().toISOString(),
        status: result.data.status,
        plan_name: result.data.plan_name,
        duration_days: result.data.duration_days,
        features: result.data.features || [],
        isTrial: result.data.isTrial || false,
        isUpgraded: result.data.isUpgraded || false,
        days_remaining: result.data.days_remaining
      };

      fs.writeFileSync(licenseFile, JSON.stringify(licenseData, null, 2));

      logToFile(`✅ License upgraded and saved successfully`);
      logToFile(`✅ New license data: ${JSON.stringify(licenseData)}`);

      return result;
    } else {
      logToFile(`❌ License upgrade failed: ${result.message}`);
      return {
        success: false,
        message: result.message || 'License upgrade failed',
        error_code: result.error_code
      };
    }
  } catch (error) {
    logToFile(`❌ License upgrade error: ${error.message}`);
    return {
      success: false,
      message: 'Failed to upgrade license',
      error: error.message
    };
  }
});

ipcMain.handle('license:register-trial', async (event, userData) => {
  try {

    const machineId = generateMachineId();
    logToFile(`🔑 Attempting trial registration for: ${userData.email} with machine ID: ${machineId}`);

    // Check if this is a reseller build
    if (isResellerBuild()) {
      logToFile(`🏢 Reseller build detected. Reseller code: ${getResellerCode()}`);
    }

    // Prepare request data using reseller configuration
    const requestData = {
      name: userData.name,
      email: userData.email,
      phone: userData.phone,
      machine_id: machineId,
      app_version: app.getVersion(),
      platform: require('os').platform(),
      reseller_code: getResellerCode()
    };

    // Get the appropriate endpoint
    const endpoint = getTrialRegistrationEndpoint();

    logToFile(`📤 Sending request to: ${endpoint}`);
    logToFile(`📤 Request data: ${JSON.stringify(requestData)}`);

    const response = await fetch(endpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: JSON.stringify(requestData)
    });

    logToFile(`📥 Response status: ${response.status} ${response.statusText}`);

    const result = await response.json();
    logToFile(`📥 Response data: ${JSON.stringify(result)}`);

    if (result.success) {
      // Store license info locally
      const licenseData = {
        license_key: result.data.license_key,
        customer_name: result.data.customer_name,
        expires_at: result.data.expires_at,
        machine_id: machineId,
        registered_at: new Date().toISOString()
      };

      // Save to local storage file
      const appDataPath = getAppDataPath();
      if (!fs.existsSync(appDataPath)) {
        fs.mkdirSync(appDataPath, { recursive: true });
      }

      const licenseFile = path.join(appDataPath, 'license.json');
      fs.writeFileSync(licenseFile, JSON.stringify(licenseData, null, 2));

      logToFile(`✅ Trial license registered successfully: ${result.data.license_key}`);
    } else {
      // Log detailed error information
      logToFile(`❌ Trial registration failed: ${result.message}`);
      if (result.errors) {
        logToFile(`❌ Validation errors: ${JSON.stringify(result.errors)}`);
      }
    }

    return result;
  } catch (error) {
    logToFile(`❌ Error registering trial license: ${error.message}`);
    logToFile(`❌ Error stack: ${error.stack}`);

    // Check if it's a network error or server error
    let errorMessage = 'Failed to connect to license server. Please check your internet connection.';
    if (error.message.includes('fetch')) {
      errorMessage = 'Network error: Unable to reach license server. Please check your internet connection.';
    } else if (error.message.includes('JSON')) {
      errorMessage = 'Server error: Invalid response from license server. Please try again later.';
    }

    return {
      success: false,
      message: errorMessage,
      error: error.message
    };
  }
});

ipcMain.handle('license:validate', async (event) => {
  try {

    // Read local license file
    const appDataPath = getAppDataPath();
    const licenseFile = path.join(appDataPath, 'license.json');

    if (!fs.existsSync(licenseFile)) {
      logToFile(`🔍 No local license file found at: ${licenseFile}`);
      return {
        success: false,
        message: 'No license found',
        error_code: 'NO_LICENSE'
      };
    }

    const licenseData = JSON.parse(fs.readFileSync(licenseFile, 'utf8'));
    const machineId = generateMachineId();

    logToFile(`🔍 Validating license - Key: ${licenseData.license_key}, Machine ID: ${machineId}`);

    // Validate with server using legacy endpoint (has correct upgrade logic)
    const response = await fetch('https://admin.getleadwave.in/api/v1/license/validate-legacy', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: JSON.stringify({
        license_key: licenseData.license_key,
        machine_id: machineId,
        app_version: app.getVersion()
      })
    });

    const result = await response.json();
    // License validation result logged to file
    logToFile(`🔍 License validation result: ${JSON.stringify(result)}`);

    if (result.success) {
      // Update local license data
      licenseData.last_validated = new Date().toISOString();
      fs.writeFileSync(licenseFile, JSON.stringify(licenseData, null, 2));
    }

    return result;
  } catch (error) {
    logToFile(`❌ Error validating license: ${error.message}`);
    // Error validating license logged to file
    return {
      success: false,
      message: 'Failed to validate license. Please check your internet connection.',
      error: error.message
    };
  }
});

ipcMain.handle('license:get-local-info', () => {
  try {
    const appDataPath = getAppDataPath();
    const licenseFile = path.join(appDataPath, 'license.json');

    if (!fs.existsSync(licenseFile)) {
      return null;
    }

    return JSON.parse(fs.readFileSync(licenseFile, 'utf8'));
  } catch (error) {
    logToFile(`❌ Error reading local license: ${error.message}`);
    return null;
  }
});

ipcMain.handle('license:clear-local-data', () => {
  try {
    const appDataPath = getAppDataPath();
    const licenseFile = path.join(appDataPath, 'license.json');

    if (fs.existsSync(licenseFile)) {
      fs.unlinkSync(licenseFile);
      logToFile(`✅ Local license data cleared`);
    }

    return { success: true };
  } catch (error) {
    logToFile(`❌ Error clearing local license: ${error.message}`);
    return { success: false, error: error.message };
  }
});

// Debug function to clear license for testing
ipcMain.handle('license:debug-clear', () => {
  try {
    const appDataPath = getAppDataPath();
    const licenseFile = path.join(appDataPath, 'license.json');

    if (fs.existsSync(licenseFile)) {
      fs.unlinkSync(licenseFile);
      logToFile(`🗑️ DEBUG: Local license file deleted for testing`);
      // Local license file deleted for testing
    } else {
      logToFile(`🗑️ DEBUG: No license file found to delete`);
      // No license file found to delete
    }

    return { success: true, message: 'License cleared for testing' };
  } catch (error) {
    logToFile(`❌ Error clearing local license: ${error.message}`);
    // Error clearing local license logged to file
    return { success: false, error: error.message };
  }
});

ipcMain.handle('license:save-local-info', (event, licenseData) => {
  try {
    const appDataPath = getAppDataPath();
    if (!fs.existsSync(appDataPath)) {
      fs.mkdirSync(appDataPath, { recursive: true });
    }

    const licenseFile = path.join(appDataPath, 'license.json');
    fs.writeFileSync(licenseFile, JSON.stringify(licenseData, null, 2));

    logToFile(`✅ Local license data saved: ${licenseData.customer_name}`);
    return { success: true };
  } catch (error) {
    logToFile(`❌ Error saving local license: ${error.message}`);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('license:check-machine', async (event) => {
  try {
    const machineId = generateMachineId();
    logToFile(`🔍 Checking machine ID: ${machineId}`);

    const response = await fetch('https://admin.getleadwave.in/api/v1/license/check-machine', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: JSON.stringify({
        machine_id: machineId
      })
    });

    // Parse JSON response first (Laravel returns 400 when license exists)
    const result = await response.json();

    // Check if this is a 400 response with license data (expected behavior)
    if (response.status === 400 && result.has_license) {
      logToFile(`✅ Found existing license on server (400 response expected)`);
      // Found existing license on server (400 response expected)
      return result;
    }

    // Handle other error responses
    if (!response.ok) {
      const errorMessage = result.message || 'Server error';
      logToFile(`❌ Server returned error: ${response.status} - ${errorMessage}`);
      // Server returned error logged to file

      return {
        success: false,
        message: errorMessage,
        error_code: 'SERVER_ERROR',
        status_code: response.status
      };
    }
    logToFile(`🔍 Machine check result: ${JSON.stringify(result)}`);
    // Machine check result logged to file

    return result;
  } catch (error) {
    logToFile(`❌ Error checking machine ID: ${error.message}`);
    // Error checking machine ID logged to file

    // Return a more specific error response
    return {
      success: false,
      message: 'Failed to connect to license server. Please check your internet connection.',
      error: error.message,
      error_code: 'NETWORK_ERROR'
    };
  }
});

ipcMain.handle('license:check-status', async (event, licenseKey) => {
  try {
    logToFile(`🔍 Checking license status for: ${licenseKey}`);
    // Checking license status logged to file

    const response = await fetch('https://admin.getleadwave.in/api/v1/license/status', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: JSON.stringify({
        license_key: licenseKey
      })
    });

    const result = await response.json();
    logToFile(`🔍 License status result: ${JSON.stringify(result)}`);
    // License status result logged to file

    return result;
  } catch (error) {
    logToFile(`❌ Error checking license status: ${error.message}`);
    // Error checking license status logged to file
    return {
      success: false,
      message: 'Failed to connect to license server',
      error: error.message
    };
  }
});

ipcMain.handle('license:force-refresh', async (event) => {
  try {
    // Force refresh license triggered from renderer
    logToFile('🔄 Force refresh license triggered from renderer...');

    // Call the license validation handler directly
    const result = await new Promise((resolve) => {
      ipcMain.handleOnce('license:validate-temp', async () => {
        return await ipcMain.handle('license:validate', () => {});
      });
      resolve(ipcMain.emit('license:validate-temp'));
    });

    // Actually, let's just call the validation logic directly
    return await validateLicenseDirectly();
  } catch (error) {
    logToFile(`❌ Error in force refresh: ${error.message}`);
    return {
      success: false,
      message: 'Failed to refresh license',
      error: error.message
    };
  }
});

// Helper function to validate license directly
async function validateLicenseDirectly() {
  try {
    // Read local license file
    const appDataPath = getAppDataPath();
    const licenseFile = path.join(appDataPath, 'license.json');

    if (!fs.existsSync(licenseFile)) {
      logToFile(`🔍 No local license file found at: ${licenseFile}`);
      return {
        success: false,
        message: 'No license found',
        error_code: 'NO_LICENSE'
      };
    }

    const licenseData = JSON.parse(fs.readFileSync(licenseFile, 'utf8'));
    const machineId = generateMachineId();

    logToFile(`🔍 Force validating license - Key: ${licenseData.license_key}, Machine ID: ${machineId}`);

    // Validate with server using legacy endpoint (has correct upgrade logic)
    const response = await fetch('https://admin.getleadwave.in/api/v1/license/validate-legacy', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: JSON.stringify({
        license_key: licenseData.license_key,
        machine_id: machineId,
        app_version: app.getVersion()
      })
    });

    const result = await response.json();
    logToFile(`🔍 Force license validation result: ${JSON.stringify(result)}`);

    if (result.success) {
      // Update local license data
      licenseData.last_validated = new Date().toISOString();
      fs.writeFileSync(licenseFile, JSON.stringify(licenseData, null, 2));
    }

    return result;
  } catch (error) {
    logToFile(`❌ Error in force validation: ${error.message}`);
    return {
      success: false,
      message: 'Failed to validate license. Please check your internet connection.',
      error: error.message
    };
  }
}

// Background license validator status
ipcMain.handle('license:background-status', async (event) => {
  try {
    if (backgroundLicenseValidator) {
      return {
        success: true,
        status: backgroundLicenseValidator.getStatus()
      };
    }
    return {
      success: false,
      message: 'Background license validator not available'
    };
  } catch (error) {
    logToFile(`❌ Error getting background license status: ${error.message}`);
    return {
      success: false,
      error: error.message
    };
  }
});

// Clean Installation Handler
ipcMain.handle('app:clean-installation', async (event) => {
  try {
    const { cleanInstallation } = require('../../scripts/clean-installation.js');
    await cleanInstallation();
    return { success: true, message: 'Clean installation completed successfully' };
  } catch (error) {
    logToFile(`❌ Error during clean installation: ${error.message}`);
    return { success: false, message: 'Failed to clean installation', error: error.message };
  }
});

// Basic IPC Handlers
ipcMain.handle('app-version', () => {
  return app.getVersion();
});

ipcMain.handle('app-quit', async () => {
  logToFile('🔄 App quit requested from renderer process');
  isQuitting = true;
  await gracefulShutdown();
  return { success: true };
});

// Window Control IPC Handlers
ipcMain.handle('window:toggle-frame', async (event, showFrame) => {
  try {
    if (!mainWindow) {
      return { success: false, error: 'Main window not available' };
    }

    logToFile(`🪟 Toggling menu bar: ${showFrame ? 'show' : 'hide'}`);

    // Use the safer approach of just toggling the menu bar visibility
    // instead of recreating the entire window
    if (showFrame) {
      // Show the menu bar
      mainWindow.setMenuBarVisibility(true);
      mainWindow.setAutoHideMenuBar(false);
      logToFile('✅ Menu bar shown');
    } else {
      // Hide the menu bar
      mainWindow.setMenuBarVisibility(false);
      mainWindow.setAutoHideMenuBar(true);
      logToFile('✅ Menu bar hidden');
    }

    logToFile(`✅ Window frame toggled successfully: ${showFrame ? 'visible' : 'hidden'}`);
    return { success: true };
  } catch (error) {
    logToFile(`❌ Error toggling window frame: ${error.message}`);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('window:get-frame-status', () => {
  try {
    if (!mainWindow) {
      return { success: false, error: 'Main window not available' };
    }

    // Check if menu bar is visible (this is what we're actually controlling)
    const hasFrame = mainWindow.isMenuBarVisible();
    logToFile(`🔍 Menu bar visible: ${hasFrame}`);
    return { success: true, hasFrame };
  } catch (error) {
    logToFile(`❌ Error getting frame status: ${error.message}`);
    return { success: false, error: error.message };
  }
});

// Apply window frame preference from database (called after database is ready)
ipcMain.handle('window:apply-saved-preference', async () => {
  try {
    if (!mainWindow) {
      return { success: false, error: 'Main window not available' };
    }

    if (!databaseService || !databaseService.db) {
      return { success: false, error: 'Database not available' };
    }

    logToFile('🔄 Applying saved window frame preference from database...');

    const framePreference = databaseService.db.prepare('SELECT value FROM app_settings WHERE key = ?').get('window_show_title_bar');

    if (framePreference) {
      const shouldShowTitleBar = framePreference.value === 'true';
      logToFile(`📋 Found saved preference: ${shouldShowTitleBar}`);

      if (!shouldShowTitleBar) {
        mainWindow.setMenuBarVisibility(false);
        mainWindow.setAutoHideMenuBar(true);
        logToFile('🪟 Applied saved preference: title bar hidden');
      } else {
        mainWindow.setMenuBarVisibility(true);
        mainWindow.setAutoHideMenuBar(false);
        logToFile('🪟 Applied saved preference: title bar visible');
      }

      return { success: true, applied: true, showTitleBar: shouldShowTitleBar };
    } else {
      logToFile('📋 No saved preference found');
      return { success: true, applied: false, showTitleBar: true };
    }
  } catch (error) {
    logToFile(`❌ Error applying saved preference: ${error.message}`);
    return { success: false, error: error.message };
  }
});



ipcMain.handle('app:is-development', () => {
  return isDev;
});

ipcMain.handle('show-message-box', async (event, options) => {
  const result = await dialog.showMessageBox(mainWindow, options);
  return result;
});

ipcMain.handle('show-open-dialog', async (event, options) => {
  const result = await dialog.showOpenDialog(mainWindow, options);
  return result;
});

ipcMain.handle('show-save-dialog', async (event, options) => {
  const result = await dialog.showSaveDialog(mainWindow, options);
  return result;
});

// WhatsApp Service IPC Handlers
ipcMain.handle('whatsapp:create-session', async (event, sessionData) => {
  try {
    if (!appService) {
      return { success: false, message: 'App service not available' };
    }
    const result = await appService.createWhatsAppSession(sessionData.name || sessionData.device_name);
    return result;
  } catch (error) {
    logToFile(`❌ Error creating WhatsApp session: ${error.message}`);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('whatsapp:disconnect-session', async (event, sessionId) => {
  try {
    if (!appService) {
      return { success: false, message: 'App service not available' };
    }
    return await appService.disconnectWhatsAppSession(sessionId);
  } catch (error) {
    logToFile(`❌ Error disconnecting WhatsApp session: ${error.message}`);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('whatsapp:get-sessions', async () => {
  try {
    if (!appService) {
      return { success: false, sessions: [] };
    }
    const sessions = await appService.getWhatsAppSessions();
    return { success: true, sessions: sessions };
  } catch (error) {
    logToFile(`❌ Error getting WhatsApp sessions: ${error.message}`);
    return { success: false, sessions: [] };
  }
});

ipcMain.handle('whatsapp:send-message', async (event, sessionId, to, message, type, options) => {
  try {
    if (!appService) {
      return { success: false, error: 'App service not available' };
    }
    return await appService.sendMessage(sessionId, to, message, type, options);
  } catch (error) {
    logToFile(`❌ Error sending message: ${error.message}`);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('whatsapp:reconnect-session', async (event, sessionId) => {
  try {
    if (!appService) {
      return { success: false, message: 'App service not available' };
    }
    return await appService.reconnectWhatsAppSession(sessionId);
  } catch (error) {
    logToFile(`❌ Error reconnecting WhatsApp session: ${error.message}`);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('whatsapp:delete-session', async (event, sessionId) => {
  try {
    if (!appService) {
      return { success: false, message: 'App service not available' };
    }

    // Add timeout to prevent hanging
    const deletePromise = appService.deleteWhatsAppSession(sessionId);
    const timeoutPromise = new Promise((resolve) =>
      setTimeout(() => resolve({ success: false, message: 'Delete operation timeout' }), 10000)
    );

    const result = await Promise.race([deletePromise, timeoutPromise]);
    return result;
  } catch (error) {
    logToFile(`❌ Error deleting WhatsApp session: ${error.message}`);
    logToFile(`❌ Error stack: ${error.stack}`);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('whatsapp:get-session-status', async (event, sessionId) => {
  try {
    if (!appService) {
      return { success: false, error: 'App service not available' };
    }
    const whatsappService = appService.getWhatsAppService();
    return whatsappService.getSessionStatus(sessionId);
  } catch (error) {
    logToFile(`❌ Error getting session status: ${error.message}`);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('whatsapp:request-pairing-code', async (event, sessionId, phoneNumber) => {
  try {
    if (!appService) {
      return { success: false, error: 'App service not available' };
    }
    logToFile(`🔢 Requesting pairing code for session: ${sessionId}, phone: ${phoneNumber}`);
    const result = await appService.requestPairingCode(sessionId, phoneNumber);
    logToFile(`🔢 Pairing code result:`, result);
    return result;
  } catch (error) {
    logToFile(`❌ Error requesting pairing code: ${error.message}`);
    return {
      success: false,
      error: error.message,
      sessionId: sessionId,
      phoneNumber: phoneNumber
    };
  }
});

ipcMain.handle('whatsapp:create-pairing-session', async (event, phoneNumber) => {
  try {
    if (!appService) {
      return { success: false, error: 'App service not available' };
    }
    logToFile(`🔢 Creating new pairing code session for phone: ${phoneNumber}`);
    const result = await appService.createPairingCodeSession(phoneNumber);
    logToFile(`🔢 Pairing session creation result:`, result);
    return result;
  } catch (error) {
    logToFile(`❌ Error creating pairing code session: ${error.message}`);
    return {
      success: false,
      error: error.message,
      phoneNumber: phoneNumber
    };
  }
});

ipcMain.handle('whatsapp:send-template-message', async (event, sessionId, to, template, variables) => {
  try {
    if (!appService) {
      return { success: false, error: 'App service not available' };
    }
    const whatsappService = appService.getWhatsAppService();
    return await whatsappService.sendTemplateMessage(sessionId, to, template, variables);
  } catch (error) {
    logToFile(`❌ Error sending template message: ${error.message}`);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('whatsapp:check-number', async (event, sessionId, phoneNumber) => {
  try {
    if (!appService) {
      return { success: false, error: 'App service not available' };
    }
    const whatsappService = appService.getWhatsAppService();
    return await whatsappService.checkNumberExists(sessionId, phoneNumber);
  } catch (error) {
    logToFile(`❌ Error checking number: ${error.message}`);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('whatsapp:verify-number', async (event, phoneNumber) => {
  try {
    if (!appService) {
      return { success: false, error: 'App service not available' };
    }
    const whatsappService = appService.getWhatsAppService();
    return await whatsappService.verifyNumber(phoneNumber);
  } catch (error) {
    logToFile(`❌ Error verifying number: ${error.message}`);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('whatsapp:get-chats', async (event, sessionId) => {
  try {
    if (!appService) {
      return { success: false, error: 'App service not available' };
    }
    const whatsappService = appService.getWhatsAppService();
    return await whatsappService.getChats(sessionId);
  } catch (error) {
    logToFile(`❌ Error getting chats: ${error.message}`);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('whatsapp:trigger-outgoing-call', async (event, sessionId, contactJid) => {
  try {
    if (!appService) {
      return { success: false, error: 'App service not available' };
    }
    const whatsappService = appService.getWhatsAppService();
    return await whatsappService.triggerOutgoingCallResponse(sessionId, contactJid);
  } catch (error) {
    logToFile(`❌ Error triggering outgoing call response: ${error.message}`);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('whatsapp:get-chat-history', async (event, sessionId, chatId, limit) => {
  try {
    if (!appService) {
      return { success: false, error: 'App service not available' };
    }
    const whatsappService = appService.getWhatsAppService();
    return await whatsappService.getChatHistory(sessionId, chatId, limit);
  } catch (error) {
    logToFile(`❌ Error getting chat history: ${error.message}`);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('whatsapp:mark-chat-as-read', async (event, sessionId, chatId) => {
  try {
    if (!appService) {
      return { success: false, error: 'App service not available' };
    }
    const whatsappService = appService.getWhatsAppService();
    return await whatsappService.markChatAsRead(sessionId, chatId);
  } catch (error) {
    logToFile(`❌ Error marking chat as read: ${error.message}`);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('whatsapp:download-media', async (event, sessionId, messageKey) => {
  try {
    if (!appService) {
      return { success: false, error: 'App service not available' };
    }
    const whatsappService = appService.getWhatsAppService();
    return await whatsappService.downloadMedia(sessionId, messageKey);
  } catch (error) {
    logToFile(`❌ Error downloading media: ${error.message}`);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('whatsapp:upload-media', async (event, filePath) => {
  try {
    if (!appService) {
      return { success: false, error: 'App service not available' };
    }
    const whatsappService = appService.getWhatsAppService();
    return await whatsappService.uploadMedia(filePath);
  } catch (error) {
    logToFile(`❌ Error uploading media: ${error.message}`);
    return { success: false, error: error.message };
  }
});

// Group Management IPC Handlers
ipcMain.handle('whatsapp:fetch-all-groups', async (event, sessionId) => {
  try {
    if (!appService) {
      return { success: false, error: 'App service not available', groups: [] };
    }
    const whatsappService = appService.getWhatsAppService();
    return await whatsappService.fetchAllGroups(sessionId);
  } catch (error) {
    logToFile(`❌ Error fetching all groups: ${error.message}`);
    return { success: false, error: error.message, groups: [] };
  }
});

ipcMain.handle('whatsapp:get-group-metadata', async (event, sessionId, groupId) => {
  try {
    if (!appService) {
      return { success: false, error: 'App service not available' };
    }
    const whatsappService = appService.getWhatsAppService();
    return await whatsappService.getGroupMetadata(sessionId, groupId);
  } catch (error) {
    logToFile(`❌ Error getting group metadata: ${error.message}`);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('whatsapp:get-group-invite-code', async (event, sessionId, groupId) => {
  try {
    if (!appService) {
      return { success: false, error: 'App service not available' };
    }
    const whatsappService = appService.getWhatsAppService();
    return await whatsappService.getGroupInviteCode(sessionId, groupId);
  } catch (error) {
    logToFile(`❌ Error getting group invite code: ${error.message}`);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('whatsapp:get-group-info-by-invite', async (event, sessionId, inviteCode) => {
  try {
    if (!appService) {
      return { success: false, error: 'App service not available' };
    }
    const whatsappService = appService.getWhatsAppService();
    return await whatsappService.getGroupInfoByInviteCode(sessionId, inviteCode);
  } catch (error) {
    logToFile(`❌ Error getting group info by invite code: ${error.message}`);
    return { success: false, error: error.message };
  }
});

// Database IPC Handlers
ipcMain.handle('db-query', async (event, query, params) => {
  try {
    if (!appService) {
      return { success: false, error: 'App service not available' };
    }
    const db = appService.getDatabaseService();
    const result = await db.query(query, params);
    return result;
  } catch (error) {
    logToFile(`❌ Database query error: ${error.message}`);
    return { success: false, error: error.message };
  }
});

// Campaign Scheduler IPC Handlers
ipcMain.handle('campaign-scheduler:get-status', async () => {
  try {
    if (!appService) {
      return { success: false, error: 'App service not available' };
    }
    const scheduler = appService.getCampaignScheduler();
    return { success: true, status: scheduler.getStatus() };
  } catch (error) {
    logToFile(`❌ Campaign scheduler status error: ${error.message}`);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('campaign-scheduler:trigger-check', async () => {
  try {
    if (!appService) {
      return { success: false, error: 'App service not available' };
    }
    const scheduler = appService.getCampaignScheduler();
    await scheduler.triggerCheck();
    return { success: true };
  } catch (error) {
    logToFile(`❌ Campaign scheduler trigger error: ${error.message}`);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('campaign-scheduler:start-campaign', async (event, campaignId) => {
  try {
    logToFile(`🚀 IPC: Starting campaign ${campaignId}`);
    if (!appService) {
      logToFile(`❌ IPC: App service not available`);
      return { success: false, error: 'App service not available' };
    }
    const scheduler = appService.getCampaignScheduler();
    logToFile(`🔧 IPC: Got scheduler, calling processCampaign(${campaignId})`);
    await scheduler.processCampaign(campaignId);
    logToFile(`✅ IPC: Campaign ${campaignId} processed successfully`);
    return { success: true };
  } catch (error) {
    logToFile(`❌ Campaign start error: ${error.message}`);
    return { success: false, error: error.message };
  }
});

// Application Stats and Health IPC Handlers
ipcMain.handle('app-stats', async (event) => {
  try {
    if (!appService) {
      return { success: false, error: 'App service not available' };
    }
    const stats = await appService.getStats();
    return { success: true, data: stats };
  } catch (error) {
    logToFile(`❌ Stats retrieval error: ${error.message}`);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('app-recent-activities', async (event, limit) => {
  try {
    if (!appService) {
      return { success: false, error: 'App service not available' };
    }
    const activities = await appService.getRecentActivities(limit);
    return { success: true, data: activities };
  } catch (error) {
    logToFile(`❌ Recent activities retrieval error: ${error.message}`);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('app-health', async (event) => {
  try {
    if (!appService) {
      return { success: false, error: 'App service not available' };
    }
    const health = await appService.getHealthCheck();
    return { success: true, data: health };
  } catch (error) {
    logToFile(`❌ Health check error: ${error.message}`);
    return { success: false, error: error.message };
  }
});

// File System IPC Handlers
ipcMain.handle('fs-read-file', async (event, filePath) => {
  try {
    const data = fs.readFileSync(filePath, 'utf8');
    return { success: true, data };
  } catch (error) {
    logToFile(`❌ Error reading file: ${error.message}`);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('fs-write-file', async (event, filePath, data) => {
  try {
    fs.writeFileSync(filePath, data, 'utf8');
    return { success: true };
  } catch (error) {
    logToFile(`❌ Error writing file: ${error.message}`);
    return { success: false, error: error.message };
  }
});

// Shell IPC Handlers
ipcMain.handle('shell-open-external', async (event, url) => {
  try {
    await shell.openExternal(url);
    return { success: true };
  } catch (error) {
    logToFile(`❌ Error opening external URL: ${error.message}`);
    return { success: false, error: error.message };
  }
});

// Notification Service
let notificationService = null;
let lastNotificationCheck = null;

class NotificationService {
  constructor() {
    this.isRunning = false;
    this.checkInterval = null;
    this.lastCheck = new Date().toISOString();
    this.processedNotifications = new Set(); // Track processed notification IDs
  }

  start() {
    if (this.isRunning) return;

    this.isRunning = true;
    logToFile('🔔 Starting notification service...');

    // Check immediately
    this.checkForNotifications();

    // Then check every 5 minutes to reduce system load
    this.checkInterval = setInterval(() => {
      this.checkForNotifications();
    }, 300000);
  }

  stop() {
    if (!this.isRunning) return;

    this.isRunning = false;
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
      this.checkInterval = null;
    }
    logToFile('🔔 Notification service stopped');
  }

  async checkForNotifications() {
    try {
      const response = await fetch('https://admin.getleadwave.in/api/v1/notifications/latest', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (data.success && data.notifications && data.notifications.length > 0) {
        // Filter out already processed notifications
        const newNotifications = data.notifications.filter(notification =>
          !this.processedNotifications.has(notification.id)
        );

        if (newNotifications.length > 0) {
          // Send new notifications to renderer
          newNotifications.forEach(notification => {
            if (mainWindow && !mainWindow.isDestroyed()) {
              logToFile(`🔔 Sending new notification to renderer: ${JSON.stringify(notification)}`);
              mainWindow.webContents.send('notifications:new-notification', notification);

              // Also send as in-app toast notification
              mainWindow.webContents.send('notifications:show-toast', {
                type: 'info',
                title: 'New Notification',
                message: notification.title || notification.message || 'You have a new notification',
                duration: 5000,
                data: notification
              });

              // Mark as processed
              this.processedNotifications.add(notification.id);
            }
          });

          logToFile(`🔔 Found ${newNotifications.length} new notifications (${data.notifications.length} total)`);
        } else {
          logToFile(`🔔 No new notifications (${data.notifications.length} already processed)`);
        }
      }

      this.lastCheck = new Date().toISOString();
    } catch (error) {
      logToFile(`❌ Error checking notifications: ${error.message}`);
    }
  }
}

// Background License Validator Class
class BackgroundLicenseValidator {
  constructor() {
    this.isRunning = false;
    this.validationInterval = null;
    this.lastValidation = new Date().toISOString();
  }

  start() {
    if (this.isRunning) return;

    this.isRunning = true;
    logToFile('🔐 Starting background license validator...');

    // Validate immediately
    this.validateLicense();

    // Set up periodic validation every 15 minutes for regular licenses
    // This runs in the background without affecting the UI
    this.validationInterval = setInterval(() => {
      this.validateLicense();
    }, 900000); // 15 minutes
  }

  stop() {
    if (!this.isRunning) return;

    this.isRunning = false;
    if (this.validationInterval) {
      clearInterval(this.validationInterval);
      this.validationInterval = null;
    }
    logToFile('🔐 Background license validator stopped');
  }

  async validateLicense() {
    try {
      logToFile('🔐 Background license validation started...');
      this.lastValidation = new Date().toISOString();

      // Read local license file
      const appDataPath = getAppDataPath();
      const licenseFile = path.join(appDataPath, 'license.json');

      if (!fs.existsSync(licenseFile)) {
        logToFile('🔐 No license file found during background validation');
        return;
      }

      const licenseData = JSON.parse(fs.readFileSync(licenseFile, 'utf8'));
      const machineId = generateMachineId();

      // Validate with server silently using legacy endpoint (has correct upgrade logic)
      const response = await fetch('https://admin.getleadwave.in/api/v1/license/validate-legacy', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: JSON.stringify({
          license_key: licenseData.license_key,
          machine_id: machineId,
          app_version: app.getVersion(),
          background_validation: true
        })
      });

      const result = await response.json();

      if (result.success) {
        logToFile('🔐 Background license validation successful');
        // Update local license data silently
        licenseData.last_validated = new Date().toISOString();
        fs.writeFileSync(licenseFile, JSON.stringify(licenseData, null, 2));
      } else {
        logToFile(`🔐 Background license validation failed: ${result.message}`);
        // Don't take action here - let the frontend handle license issues when user interacts
      }
    } catch (error) {
      logToFile(`🔐 Background license validation error: ${error.message}`);
      // Silent failure - don't interrupt user experience
    }
  }

  getStatus() {
    return {
      isRunning: this.isRunning,
      lastValidation: this.lastValidation
    };
  }
}

// Initialize services
notificationService = new NotificationService();
const backgroundLicenseValidator = new BackgroundLicenseValidator();

// Notification IPC Handlers
ipcMain.handle('notifications:get-notifications', async (event) => {
  try {
    logToFile('🔔 Fetching notifications from API...');
    const response = await fetch('https://admin.getleadwave.in/api/v1/notifications', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    logToFile(`🔔 API Response: ${JSON.stringify(data)}`);
    return { success: true, data };
  } catch (error) {
    logToFile(`❌ Error fetching notifications: ${error.message}`);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('notifications:get-latest', async (event, lastCheck) => {
  try {
    const url = lastCheck
      ? `https://admin.getleadwave.in/api/v1/notifications/latest?last_check=${encodeURIComponent(lastCheck)}`
      : 'https://admin.getleadwave.in/api/v1/notifications/latest';

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return { success: true, data };
  } catch (error) {
    logToFile(`❌ Error fetching latest notifications: ${error.message}`);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('notifications:mark-as-read', async (event, notificationId) => {
  try {
    const response = await fetch(`https://admin.getleadwave.in/api/v1/notifications/${notificationId}/read`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return { success: true, data };
  } catch (error) {
    logToFile(`❌ Error marking notification as read: ${error.message}`);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('notifications:get-stats', async (event) => {
  try {
    const response = await fetch('https://admin.getleadwave.in/api/v1/notifications/stats', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return { success: true, data };
  } catch (error) {
    logToFile(`❌ Error fetching notification stats: ${error.message}`);
    return { success: false, error: error.message };
  }
});

// AI Chatbot IPC Handlers
ipcMain.handle('ai-providers:get-all', async () => {
  try {
    if (!appService) {
      return { success: false, error: 'App service not available' };
    }
    const databaseService = appService.getDatabaseService();
    if (!databaseService) {
      return { success: false, error: 'Database service not available' };
    }
    const result = await databaseService.query('SELECT * FROM ai_providers WHERE is_active = 1 ORDER BY created_at DESC');
    return result; // Return the result directly, it already has { success, data } structure
  } catch (error) {
    logToFile(`❌ AI Providers get error: ${error.message}`);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('ai-providers:create', async (event, providerData) => {
  try {
    if (!appService) {
      return { success: false, error: 'App service not available' };
    }
    const databaseService = appService.getDatabaseService();
    if (!databaseService) {
      return { success: false, error: 'Database service not available' };
    }

    const result = await databaseService.query(`
      INSERT INTO ai_providers (
        name, type, api_key, model, temperature, max_tokens,
        is_active, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    `, [
      providerData.name,
      providerData.type,
      providerData.apiKey,
      providerData.model,
      providerData.temperature,
      providerData.maxTokens,
      providerData.isActive ? 1 : 0
    ]);

    return { success: true, data: { id: result.lastID } };
  } catch (error) {
    logToFile(`❌ AI Provider create error: ${error.message}`);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('ai-providers:update', async (event, id, providerData) => {
  try {
    if (!appService) {
      return { success: false, error: 'App service not available' };
    }
    const databaseService = appService.getDatabaseService();
    if (!databaseService) {
      return { success: false, error: 'Database service not available' };
    }

    await databaseService.query(`
      UPDATE ai_providers SET
        name = ?, type = ?, api_key = ?, model = ?,
        temperature = ?, max_tokens = ?, is_active = ?,
        updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `, [
      providerData.name,
      providerData.type,
      providerData.apiKey,
      providerData.model,
      providerData.temperature,
      providerData.maxTokens,
      providerData.isActive ? 1 : 0,
      id
    ]);

    return { success: true };
  } catch (error) {
    logToFile(`❌ AI Provider update error: ${error.message}`);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('ai-providers:delete', async (event, id) => {
  try {
    if (!appService) {
      return { success: false, error: 'App service not available' };
    }
    const databaseService = appService.getDatabaseService();
    if (!databaseService) {
      return { success: false, error: 'Database service not available' };
    }

    // Check if provider is being used by any chatbots
    const chatbotsResult = await databaseService.query('SELECT COUNT(*) as count FROM ai_chatbots WHERE provider_id = ?', [id]);
    if (!chatbotsResult.success) {
      return { success: false, error: 'Failed to check chatbot dependencies' };
    }

    const chatbots = chatbotsResult.data;
    if (chatbots && chatbots.length > 0 && chatbots[0].count > 0) {
      return { success: false, error: 'Cannot delete provider - it is being used by chatbots' };
    }

    await databaseService.query('DELETE FROM ai_providers WHERE id = ?', [id]);
    return { success: true };
  } catch (error) {
    logToFile(`❌ AI Provider delete error: ${error.message}`);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('ai-chatbots:get-all', async () => {
  try {
    if (!appService) {
      return { success: false, error: 'App service not available' };
    }
    const databaseService = appService.getDatabaseService();
    if (!databaseService) {
      return { success: false, error: 'Database service not available' };
    }
    const result = await databaseService.query(`
      SELECT c.*, p.name as provider_name, p.type as provider_type
      FROM ai_chatbots c
      LEFT JOIN ai_providers p ON c.provider_id = p.id
      ORDER BY c.created_at DESC
    `);
    return result; // Return the result directly, it already has { success, data } structure
  } catch (error) {
    logToFile(`❌ AI Chatbots get error: ${error.message}`);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('ai-chatbots:create', async (event, chatbotData) => {
  try {
    if (!appService) {
      return { success: false, error: 'App service not available' };
    }
    const databaseService = appService.getDatabaseService();
    if (!databaseService) {
      return { success: false, error: 'Database service not available' };
    }

    const result = await databaseService.query(`
      INSERT INTO ai_chatbots (
        name, description, provider_id, system_prompt, language,
        personality, industry, session_ids, trigger_keywords, stop_keywords, is_active, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    `, [
      chatbotData.name,
      chatbotData.description,
      chatbotData.providerId,
      chatbotData.systemPrompt,
      chatbotData.language || 'en',
      chatbotData.personality,
      chatbotData.industry,
      JSON.stringify(chatbotData.sessionIds || []),
      JSON.stringify(chatbotData.triggerKeywords || []),
      JSON.stringify(chatbotData.stopKeywords || []),
      chatbotData.isActive ? 1 : 0
    ]);

    return { success: true, data: { id: result.lastID } };
  } catch (error) {
    logToFile(`❌ AI Chatbot create error: ${error.message}`);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('ai-chatbots:update', async (event, id, chatbotData) => {
  try {
    if (!appService) {
      return { success: false, error: 'App service not available' };
    }
    const databaseService = appService.getDatabaseService();
    if (!databaseService) {
      return { success: false, error: 'Database service not available' };
    }

    await databaseService.query(`
      UPDATE ai_chatbots SET
        name = ?, description = ?, provider_id = ?, system_prompt = ?,
        language = ?, personality = ?, industry = ?, session_ids = ?, trigger_keywords = ?,
        stop_keywords = ?, is_active = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `, [
      chatbotData.name,
      chatbotData.description,
      chatbotData.providerId,
      chatbotData.systemPrompt,
      chatbotData.language || 'en',
      chatbotData.personality,
      chatbotData.industry,
      JSON.stringify(chatbotData.sessionIds || []),
      JSON.stringify(chatbotData.triggerKeywords || []),
      JSON.stringify(chatbotData.stopKeywords || []),
      chatbotData.isActive ? 1 : 0,
      id
    ]);

    return { success: true };
  } catch (error) {
    logToFile(`❌ AI Chatbot update error: ${error.message}`);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('ai-chatbots:delete', async (event, id) => {
  try {
    if (!appService) {
      return { success: false, error: 'App service not available' };
    }
    const databaseService = appService.getDatabaseService();
    if (!databaseService) {
      return { success: false, error: 'Database service not available' };
    }

    logToFile(`🗑️ Deleting AI chatbot with ID: ${id}`);

    // 1. End all active conversations for this chatbot
    const endConversationsResult = await databaseService.query(
      'UPDATE ai_conversations SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE chatbot_id = ? AND status = ?',
      ['completed', id, 'active']
    );
    logToFile(`🗑️ Ended ${endConversationsResult.changes || 0} active conversations for chatbot ${id}`);

    // 2. Delete related data (messages will be deleted by CASCADE if properly set up)
    await databaseService.query('DELETE FROM ai_intents WHERE chatbot_id = ?', [id]);
    await databaseService.query('DELETE FROM ai_knowledge_base WHERE chatbot_id = ?', [id]);

    // 3. Delete the chatbot itself (this should cascade delete conversations and messages)
    const deleteResult = await databaseService.query('DELETE FROM ai_chatbots WHERE id = ?', [id]);

    if (deleteResult.success) {
      logToFile(`✅ Successfully deleted AI chatbot ${id} and cleaned up related data`);

      // 4. Clear any cached data by notifying the AI service
      try {
        const aiService = appService.getAIService();
        if (aiService && typeof aiService.clearChatbotCache === 'function') {
          aiService.clearChatbotCache(id);
        }
      } catch (cacheError) {
        logToFile(`⚠️ Warning: Could not clear chatbot cache: ${cacheError.message}`);
      }
    }

    return { success: true };
  } catch (error) {
    logToFile(`❌ AI Chatbot delete error: ${error.message}`);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('ai-chatbots:toggle-status', async (event, id) => {
  try {
    if (!appService) {
      return { success: false, error: 'App service not available' };
    }
    const databaseService = appService.getDatabaseService();
    if (!databaseService) {
      return { success: false, error: 'Database service not available' };
    }

    await databaseService.query(`
      UPDATE ai_chatbots SET
        is_active = CASE WHEN is_active = 1 THEN 0 ELSE 1 END,
        updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `, [id]);

    return { success: true };
  } catch (error) {
    logToFile(`❌ AI Chatbot toggle status error: ${error.message}`);
    return { success: false, error: error.message };
  }
});

// Manual AI Schema Migration Handler (for development/debugging)
ipcMain.handle('ai-schema:force-migration', async () => {
  try {
    if (!appService) {
      return { success: false, error: 'App service not available' };
    }

    const databaseService = appService.getDatabaseService();
    if (!databaseService) {
      return { success: false, error: 'Database service not available' };
    }

    // Forcing AI schema migration
    await databaseService.runAIChatbotMigrations();
    // AI schema migration completed

    return { success: true, message: 'AI schema migration completed successfully' };
  } catch (error) {
    logToFile(`❌ AI Schema migration error: ${error.message}`);
    return { success: false, error: error.message };
  }
});

// Clean up orphaned chatbot conversations
ipcMain.handle('chatbot:cleanup-orphaned-conversations', async (event) => {
  try {
    if (!appService) {
      return { success: false, error: 'App service not available' };
    }
    const databaseService = appService.getDatabaseService();
    if (!databaseService) {
      return { success: false, error: 'Database service not available' };
    }

    logToFile('🧹 Cleaning up orphaned chatbot conversations...');

    // End conversations for inactive flows
    const inactiveFlowConversations = await databaseService.query(`
      UPDATE chatbot_conversations
      SET is_active = 0, completed_at = CURRENT_TIMESTAMP
      WHERE is_active = 1 AND flow_id IN (
        SELECT id FROM chatbot_flows WHERE is_active = 0
      )
    `);

    // End conversations for deleted flows
    const deletedFlowConversations = await databaseService.query(`
      UPDATE chatbot_conversations
      SET is_active = 0, completed_at = CURRENT_TIMESTAMP
      WHERE is_active = 1 AND flow_id NOT IN (
        SELECT id FROM chatbot_flows
      )
    `);

    const totalCleaned = (inactiveFlowConversations.changes || 0) + (deletedFlowConversations.changes || 0);

    logToFile(`🧹 Cleaned up ${totalCleaned} orphaned conversations`);

    return {
      success: true,
      cleaned: totalCleaned,
      inactiveFlows: inactiveFlowConversations.changes || 0,
      deletedFlows: deletedFlowConversations.changes || 0
    };
  } catch (error) {
    logToFile(`❌ Cleanup orphaned conversations error: ${error.message}`);
    return { success: false, error: error.message };
  }
});

// Start services when app is ready
app.whenReady().then(() => {
  if (notificationService) {
    notificationService.start();
  }
  if (backgroundLicenseValidator) {
    backgroundLicenseValidator.start();
  }
});

// Stop services when app is quitting
app.on('before-quit', () => {
  if (notificationService) {
    notificationService.stop();
  }
  if (backgroundLicenseValidator) {
    backgroundLicenseValidator.stop();
  }
});

logToFile('✅ Electron main process setup complete');
