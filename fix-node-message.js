const initSqlJs = require('sql.js');
const fs = require('fs');
const path = require('path');

async function fixNodeMessage() {
  console.log('🔧 Fixing chatbot node message content...');
  
  const dbPath = path.join(process.env.APPDATA, 'Electron', 'leadwave.db');
  
  if (!fs.existsSync(dbPath)) {
    console.log('❌ Database not found');
    return;
  }
  
  const SQL = await initSqlJs();
  const filebuffer = fs.readFileSync(dbPath);
  const db = new SQL.Database(filebuffer);
  
  try {
    // 1. Get the current active session ID
    const sessions = db.exec('SELECT id FROM whatsapp_sessions WHERE status = "connected" LIMIT 1');
    if (sessions.length === 0 || sessions[0].values.length === 0) {
      console.log('❌ No active WhatsApp session found');
      return;
    }
    
    const activeSessionId = sessions[0].values[0][0];
    console.log(`✅ Active session ID: ${activeSessionId}`);
    
    // 2. Fix session ID in chatbot flows (again, to be sure)
    console.log('\n🔧 Step 1: Ensuring correct session ID...');
    db.exec(`UPDATE chatbot_flows SET session_id = '${activeSessionId}' WHERE is_active = 1`);
    console.log('✅ Updated chatbot flows session ID');
    
    // 3. Clean up old conversations
    console.log('\n🔧 Step 2: Cleaning up conversations...');
    db.exec(`DELETE FROM chatbot_conversations WHERE session_id != '${activeSessionId}'`);
    db.exec(`UPDATE chatbot_conversations SET is_active = 0 WHERE is_active = 1`);
    console.log('✅ Cleaned up old conversations');
    
    // 4. Check current node
    console.log('\n🔧 Step 3: Checking current chatbot node...');
    const nodes = db.exec('SELECT id, name, message, node_type FROM chatbot_nodes WHERE flow_id = 1');
    if (nodes.length > 0) {
      const [nodeId, nodeName, nodeMessage, nodeType] = nodes[0].values[0];
      console.log(`Current node: ID=${nodeId}, Name="${nodeName}", Message="${nodeMessage}", Type="${nodeType}"`);
      
      // Fix the message content
      const properMessage = 'Hello! 👋 Thanks for your interest in PHP development!\\n\\nI can help you with:\\n\\n1️⃣ PHP Basics\\n2️⃣ Advanced PHP Concepts\\n3️⃣ PHP Frameworks (Laravel, Symfony)\\n4️⃣ PHP Best Practices\\n5️⃣ Contact Our Development Team\\n\\nPlease reply with the number of your choice (1-5) or ask me any PHP-related question!';
      
      db.exec(`UPDATE chatbot_nodes SET message = '${properMessage}', name = 'Welcome Message' WHERE id = ${nodeId}`);
      console.log('✅ Updated node with proper message content');
      console.log(`New message: "${properMessage.substring(0, 100)}..."`);
    } else {
      console.log('❌ No chatbot nodes found');
    }
    
    // 5. Save changes
    const data = db.export();
    fs.writeFileSync(dbPath, data);
    console.log('\n✅ All changes saved to database');
    
    // 6. Verify the fixes
    console.log('\n🔍 Final verification...');
    
    // Check flows
    const updatedFlows = db.exec('SELECT id, name, session_id, trigger_keywords FROM chatbot_flows WHERE is_active = 1');
    if (updatedFlows.length > 0) {
      console.log('✅ Active chatbot flows:');
      updatedFlows[0].values.forEach((row, index) => {
        const [id, name, sessionId, keywords] = row;
        console.log(`  Flow ${index + 1}: ID=${id}, Name="${name}", Session="${sessionId}", Keywords="${keywords}"`);
      });
    }
    
    // Check nodes
    const updatedNodes = db.exec('SELECT id, name, message FROM chatbot_nodes WHERE flow_id = 1');
    if (updatedNodes.length > 0) {
      console.log('✅ Chatbot nodes:');
      updatedNodes[0].values.forEach((row, index) => {
        const [id, name, message] = row;
        console.log(`  Node ${index + 1}: ID=${id}, Name="${name}", Message="${message.substring(0, 50)}..."`);
      });
    }
    
    console.log('\n🎉 Chatbot is now properly configured!');
    console.log('💡 Test it by sending "PHP" or "development" to your WhatsApp number.');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    db.close();
  }
}

fixNodeMessage().catch(console.error);
