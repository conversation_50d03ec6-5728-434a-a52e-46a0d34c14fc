const DatabaseService = require('./src/services/database.service');

async function checkChatbotFlows() {
  console.log('🤖 Checking Chatbot Flows Configuration...');
  
  const db = new DatabaseService();
  await db.initialize();
  
  console.log('\n📋 All Chatbot Flows:');
  const allFlows = await db.all('SELECT * FROM chatbot_flows ORDER BY created_at DESC');
  if (allFlows.success && allFlows.data && allFlows.data.length > 0) {
    allFlows.data.forEach((flow, index) => {
      console.log(`\n${index + 1}. Flow: "${flow.name}"`);
      console.log(`   ID: ${flow.id}`);
      console.log(`   Active: ${flow.is_active ? 'YES' : 'NO'}`);
      console.log(`   Keywords: "${flow.trigger_keywords}"`);
      console.log(`   Description: ${flow.description || 'None'}`);
      console.log(`   Created: ${flow.created_at}`);
    });
  } else {
    console.log('  ❌ No chatbot flows found');
  }
  
  console.log('\n🟢 Active Chatbot Flows:');
  const activeFlows = await db.all('SELECT * FROM chatbot_flows WHERE is_active = 1');
  if (activeFlows.success && activeFlows.data && activeFlows.data.length > 0) {
    activeFlows.data.forEach((flow, index) => {
      console.log(`\n${index + 1}. "${flow.name}"`);
      console.log(`   Keywords: "${flow.trigger_keywords}"`);
      const keywords = flow.trigger_keywords ? flow.trigger_keywords.split(',').map(k => k.trim().toLowerCase()) : [];
      console.log(`   Parsed Keywords: [${keywords.join(', ')}]`);
      
      // Check if "php" and "development" are in keywords
      const hasPhp = keywords.some(k => k.includes('php'));
      const hasDevelopment = keywords.some(k => k.includes('development'));
      console.log(`   Contains "php": ${hasPhp ? 'YES' : 'NO'}`);
      console.log(`   Contains "development": ${hasDevelopment ? 'YES' : 'NO'}`);
    });
  } else {
    console.log('  ❌ No active chatbot flows found');
  }
  
  console.log('\n🔍 Checking for "php" and "development" keywords:');
  const phpFlows = await db.all(`
    SELECT * FROM chatbot_flows 
    WHERE is_active = 1 
    AND (
      LOWER(trigger_keywords) LIKE '%php%' 
      OR LOWER(trigger_keywords) LIKE '%development%'
    )
  `);
  
  if (phpFlows.success && phpFlows.data && phpFlows.data.length > 0) {
    console.log('  ✅ Found flows with php/development keywords:');
    phpFlows.data.forEach(flow => {
      console.log(`    - "${flow.name}": "${flow.trigger_keywords}"`);
    });
  } else {
    console.log('  ❌ No flows found with php/development keywords');
  }
  
  console.log('\n📝 Checking Chatbot Nodes:');
  if (activeFlows.success && activeFlows.data && activeFlows.data.length > 0) {
    for (const flow of activeFlows.data) {
      console.log(`\n🔗 Nodes for flow "${flow.name}":`);
      const nodes = await db.all('SELECT * FROM chatbot_nodes WHERE flow_id = ? ORDER BY position ASC', [flow.id]);
      if (nodes.success && nodes.data && nodes.data.length > 0) {
        nodes.data.forEach((node, index) => {
          console.log(`   ${index + 1}. ${node.node_type}: "${node.content}"`);
          if (node.node_type === 'message' && node.options) {
            try {
              const options = JSON.parse(node.options);
              console.log(`      Options: ${JSON.stringify(options)}`);
            } catch (e) {
              console.log(`      Options: ${node.options}`);
            }
          }
        });
      } else {
        console.log('     ❌ No nodes found for this flow');
      }
    }
  }
  
  console.log('\n⏰ Recent Chatbot Activity:');
  const recentConversations = await db.all(`
    SELECT * FROM chatbot_conversations 
    WHERE created_at > datetime('now', '-1 hour')
    ORDER BY created_at DESC 
    LIMIT 10
  `);
  
  if (recentConversations.success && recentConversations.data && recentConversations.data.length > 0) {
    console.log('  Recent conversations:');
    recentConversations.data.forEach(conv => {
      console.log(`    - ${conv.created_at}: User ${conv.user_phone}, Flow ${conv.flow_id}, Active: ${conv.is_active}`);
    });
  } else {
    console.log('  ❌ No recent chatbot conversations');
  }
  
  const recentCooldowns = await db.all(`
    SELECT * FROM chatbot_cooldowns 
    WHERE created_at > datetime('now', '-1 hour')
    ORDER BY created_at DESC 
    LIMIT 5
  `);
  
  if (recentCooldowns.success && recentCooldowns.data && recentCooldowns.data.length > 0) {
    console.log('  Recent cooldowns:');
    recentCooldowns.data.forEach(cd => {
      console.log(`    - ${cd.created_at}: User ${cd.user_phone}, Flow ${cd.flow_id}`);
    });
  } else {
    console.log('  ❌ No recent cooldowns');
  }
  
  console.log('\n💡 DIAGNOSIS:');
  if (!activeFlows.success || !activeFlows.data || activeFlows.data.length === 0) {
    console.log('❌ ISSUE: No active chatbot flows found');
    console.log('🔧 SOLUTION: Create and activate chatbot flows');
  } else if (!phpFlows.success || !phpFlows.data || phpFlows.data.length === 0) {
    console.log('❌ ISSUE: No flows found with "php" or "development" keywords');
    console.log('🔧 SOLUTION: Add flows with these keywords or check keyword spelling');
  } else {
    console.log('✅ Chatbot flows look configured correctly');
    console.log('💡 The issue might be in message processing or parsing');
  }
  
  process.exit(0);
}

checkChatbotFlows().catch(console.error);
