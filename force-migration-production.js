const initSqlJs = require('sql.js');
const fs = require('fs');
const path = require('path');

async function forceMigrationProduction() {
  console.log('🔧 Force migrating production database...');
  
  // Use the actual production database path that the application uses
  const dbPath = path.join(process.env.APPDATA, 'Electron', 'leadwave.db');
  
  if (!fs.existsSync(dbPath)) {
    console.log('❌ Production database not found at:', dbPath);
    return;
  }
  
  console.log(`📁 Migrating: ${dbPath}`);
  
  try {
    const SQL = await initSqlJs();
    const filebuffer = fs.readFileSync(dbPath);
    const database = new SQL.Database(filebuffer);
    
    // Check current table schema
    console.log('🔍 Checking current schema...');
    const tableInfo = database.exec('PRAGMA table_info(chatbot_flows)');
    
    if (tableInfo.length === 0) {
      console.log('❌ chatbot_flows table not found');
      database.close();
      return;
    }
    
    const columns = tableInfo[0].values.map(row => row[1]); // column names
    console.log(`📋 Current columns: ${columns.join(', ')}`);
    
    let migrationNeeded = false;
    
    // Check and add keyword_match_type column
    if (!columns.includes('keyword_match_type')) {
      console.log('🔧 Adding keyword_match_type column...');
      database.exec("ALTER TABLE chatbot_flows ADD COLUMN keyword_match_type TEXT DEFAULT 'contains'");
      migrationNeeded = true;
    } else {
      console.log('✅ keyword_match_type column already exists');
    }
    
    // Check and add keyword_case_sensitive column
    if (!columns.includes('keyword_case_sensitive')) {
      console.log('🔧 Adding keyword_case_sensitive column...');
      database.exec("ALTER TABLE chatbot_flows ADD COLUMN keyword_case_sensitive BOOLEAN DEFAULT 0");
      migrationNeeded = true;
    } else {
      console.log('✅ keyword_case_sensitive column already exists');
    }
    
    if (migrationNeeded) {
      // Save the updated database
      console.log('💾 Saving updated database...');
      const data = database.export();
      fs.writeFileSync(dbPath, Buffer.from(data));
      console.log('✅ Database updated successfully');
      
      // Verify the migration
      console.log('🧪 Verifying migration...');
      const verifyInfo = database.exec('PRAGMA table_info(chatbot_flows)');
      const newColumns = verifyInfo[0].values.map(row => row[1]);
      console.log(`📋 Updated columns: ${newColumns.join(', ')}`);
      
      // Update existing flows with default values
      console.log('🔄 Updating existing flows with default values...');
      database.exec(`
        UPDATE chatbot_flows 
        SET keyword_match_type = 'contains', keyword_case_sensitive = 0 
        WHERE keyword_match_type IS NULL OR keyword_case_sensitive IS NULL
      `);
      
      // Save again after updating values
      const finalData = database.export();
      fs.writeFileSync(dbPath, Buffer.from(finalData));
      
      // Show updated flows
      const flows = database.exec('SELECT id, name, keyword_match_type, keyword_case_sensitive FROM chatbot_flows');
      if (flows.length > 0 && flows[0].values.length > 0) {
        console.log('📊 Updated flows:');
        flows[0].values.forEach(row => {
          const [id, name, matchType, caseSensitive] = row;
          console.log(`   - "${name}": match_type=${matchType}, case_sensitive=${caseSensitive}`);
        });
      }
      
    } else {
      console.log('✅ Database already up to date');
    }
    
    database.close();
    
    console.log('\n🎉 Migration complete!');
    console.log('🔄 Please restart the LeadWave application to pick up the changes.');
    console.log('📱 Then try editing your chatbot flow again.');
    
  } catch (error) {
    console.error('❌ Error during migration:', error.message);
    console.error('Stack:', error.stack);
  }
}

forceMigrationProduction().catch(console.error);
