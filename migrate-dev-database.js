const initSqlJs = require('sql.js');
const fs = require('fs');
const path = require('path');

async function migrateDevDatabase() {
  console.log('🔧 Migrating development database...');
  
  const dbPath = path.join(process.cwd(), 'data', 'leadwave.db');
  
  if (!fs.existsSync(dbPath)) {
    console.log('❌ Development database not found at:', dbPath);
    return;
  }
  
  console.log('✅ Found development database at:', dbPath);
  
  try {
    const SQL = await initSqlJs();
    const filebuffer = fs.readFileSync(dbPath);
    const database = new SQL.Database(filebuffer);
    
    // Check current schema
    console.log('\n🔍 Checking current chatbot_flows schema...');
    const tableInfo = database.exec('PRAGMA table_info(chatbot_flows)');
    
    if (tableInfo.length === 0) {
      console.log('❌ chatbot_flows table not found');
      database.close();
      return;
    }
    
    const columns = tableInfo[0].values.map(row => row[1]);
    console.log('📋 Current columns:', columns.join(', '));
    
    let needsMigration = false;
    
    // Check and add keyword_match_type column
    if (!columns.includes('keyword_match_type')) {
      console.log('🔧 Adding keyword_match_type column...');
      database.exec("ALTER TABLE chatbot_flows ADD COLUMN keyword_match_type TEXT DEFAULT 'contains'");
      needsMigration = true;
    } else {
      console.log('✅ keyword_match_type column already exists');
    }
    
    // Check and add keyword_case_sensitive column
    if (!columns.includes('keyword_case_sensitive')) {
      console.log('🔧 Adding keyword_case_sensitive column...');
      database.exec("ALTER TABLE chatbot_flows ADD COLUMN keyword_case_sensitive BOOLEAN DEFAULT 0");
      needsMigration = true;
    } else {
      console.log('✅ keyword_case_sensitive column already exists');
    }
    
    if (needsMigration) {
      // Update existing flows with default values
      console.log('🔧 Updating existing flows with default values...');
      database.exec(`
        UPDATE chatbot_flows 
        SET keyword_match_type = 'contains', keyword_case_sensitive = 0 
        WHERE keyword_match_type IS NULL OR keyword_case_sensitive IS NULL
      `);
      
      // Save the updated database
      const data = database.export();
      fs.writeFileSync(dbPath, Buffer.from(data));
      console.log('✅ Development database migration completed successfully');
      
      // Show flows in this database
      const flows = database.exec('SELECT id, name, keyword_match_type, keyword_case_sensitive FROM chatbot_flows');
      if (flows.length > 0 && flows[0].values.length > 0) {
        console.log('\n📊 Flows in development database:');
        flows[0].values.forEach(row => {
          const [id, name, matchType, caseSensitive] = row;
          console.log(`  - "${name}": match_type=${matchType}, case_sensitive=${caseSensitive}`);
        });
      } else {
        console.log('\n📊 No flows found in development database');
      }
    } else {
      console.log('✅ Development database is already up to date');
    }
    
    database.close();
    
  } catch (error) {
    console.error('❌ Error migrating development database:', error.message);
  }
}

migrateDevDatabase().catch(console.error);
