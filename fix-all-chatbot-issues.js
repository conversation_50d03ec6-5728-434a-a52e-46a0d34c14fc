const initSqlJs = require('sql.js');
const fs = require('fs');
const path = require('path');

async function fixAllChatbotIssues() {
  console.log('🔧 Fixing all chatbot issues...');
  
  const dbPath = path.join(process.env.APPDATA, 'Electron', 'leadwave.db');
  
  if (!fs.existsSync(dbPath)) {
    console.log('❌ Database not found');
    return;
  }
  
  const SQL = await initSqlJs();
  const filebuffer = fs.readFileSync(dbPath);
  const db = new SQL.Database(filebuffer);
  
  try {
    // 1. Get the current active session ID
    const sessions = db.exec('SELECT id FROM whatsapp_sessions WHERE status = "connected" LIMIT 1');
    if (sessions.length === 0 || sessions[0].values.length === 0) {
      console.log('❌ No active WhatsApp session found');
      return;
    }
    
    const activeSessionId = sessions[0].values[0][0];
    console.log(`✅ Active session ID: ${activeSessionId}`);
    
    // 2. Fix session ID in chatbot flows
    console.log('\n🔧 Step 1: Fixing session ID in chatbot flows...');
    db.exec(`UPDATE chatbot_flows SET session_id = '${activeSessionId}' WHERE is_active = 1`);
    console.log('✅ Updated chatbot flows session ID');
    
    // 3. Clean up old conversations
    console.log('\n🔧 Step 2: Cleaning up old conversations...');
    db.exec(`UPDATE chatbot_conversations SET is_active = 0 WHERE session_id != '${activeSessionId}'`);
    db.exec(`UPDATE chatbot_conversations SET is_active = 0 WHERE is_active = 1`);
    console.log('✅ Deactivated old conversations');
    
    // 4. Fix node content
    console.log('\n🔧 Step 3: Fixing chatbot node content...');
    
    // Check current node content
    const nodes = db.exec('SELECT id, content FROM chatbot_nodes WHERE flow_id = 1');
    if (nodes.length > 0) {
      const nodeId = nodes[0].values[0][0];
      const currentContent = nodes[0].values[0][1];
      console.log(`Current node content: "${currentContent}"`);
      
      if (!currentContent || currentContent === 'undefined' || currentContent.trim() === '') {
        // Set a proper message content
        const newContent = 'Hello! Thanks for your interest in PHP development. How can I help you today?\\n\\n1. PHP Basics\\n2. Advanced PHP\\n3. PHP Frameworks\\n4. Contact Support\\n\\nPlease reply with the number of your choice.';
        
        db.exec(`UPDATE chatbot_nodes SET content = '${newContent}' WHERE id = ${nodeId}`);
        console.log('✅ Updated node content with proper message');
        console.log(`New content: "${newContent}"`);
      } else {
        console.log('✅ Node content looks good');
      }
    }
    
    // 5. Save changes
    const data = db.export();
    fs.writeFileSync(dbPath, data);
    console.log('\n✅ All changes saved to database');
    
    // 6. Verify the fixes
    console.log('\n🔍 Verifying fixes...');
    
    // Check flows
    const updatedFlows = db.exec('SELECT id, name, session_id FROM chatbot_flows WHERE is_active = 1');
    if (updatedFlows.length > 0) {
      console.log('Updated flows:');
      updatedFlows[0].values.forEach((row, index) => {
        const [id, name, sessionId] = row;
        console.log(`  Flow ${index + 1}: ID=${id}, Name="${name}", Session="${sessionId}"`);
      });
    }
    
    // Check nodes
    const updatedNodes = db.exec('SELECT id, content FROM chatbot_nodes WHERE flow_id = 1');
    if (updatedNodes.length > 0) {
      console.log('Updated nodes:');
      updatedNodes[0].values.forEach((row, index) => {
        const [id, content] = row;
        console.log(`  Node ${index + 1}: ID=${id}, Content="${content.substring(0, 50)}..."`);
      });
    }
    
    // Check active conversations
    const activeConversations = db.exec('SELECT COUNT(*) FROM chatbot_conversations WHERE is_active = 1');
    const activeCount = activeConversations[0].values[0][0];
    console.log(`Active conversations: ${activeCount}`);
    
    console.log('\n🎉 All chatbot issues fixed!');
    console.log('💡 Now try sending "PHP" or "development" to test the flow.');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    db.close();
  }
}

fixAllChatbotIssues().catch(console.error);
